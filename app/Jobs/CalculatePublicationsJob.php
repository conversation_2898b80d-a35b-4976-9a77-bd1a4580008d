<?php

namespace App\Jobs;

use App\Customer;
use App\Emails\TrackingInformation;
use App\Product;
use App\Services\NotificationService;
use App\Shipment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\SubscriptionMonth;
use App\Subscription;
use App\Services\ExportService;
use App\Exports\ActiveSubscriptionExport;
use App\Services\ShipStationOrderService;
use App\Services\CreateLabelForOrderService;
use App\ShipStationConfiguration;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CalculatePublicationsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $fields;

    /**
     * Create a new job instance.
     *
     * @param $fields
     */
    public function __construct($fields)
    {
        $this->fields = $fields;
    }

    public function handle(ShipStationOrderService $shipStationService, CreateLabelForOrderService $createLabelForOrderService, NotificationService $notificationService)
    {
        $errorMessages = [];
        $exportService = app()->make(ExportService::class);
        $config = ShipStationConfiguration::find(1);
        $updatedSubscriptionIds = [];
        // Eager load orders to minimize N+1 problem.
        $subscriptions = Subscription::with('order')->where('issues', '>=', 1)->where('canceled', false)->get();

        $batchId = Carbon::now()->format('Y-m-d_H-i-s');
        if ($this->fields->increment_publications) {
            $nextPublicationMonth = SubscriptionMonth::where('is_next', 1)->first() ?? null;
            foreach ($subscriptions as $subscription) {
                $subscription->incrementPublicationCount();
                $subscription->DecreaseIssuesCount();
                $updatedSubscriptionIds[] = $subscription->id;

                $customer = Customer::where('id', $subscription->customer_id)->first();

                if ($nextPublicationMonth && $subscription->order) {
                    $product = Product::cycles()->where('attribute_data->name->value',  $subscription->subscription_type)->first();
                    $address = $subscription->lunar_order?->shippingAddress;

                    $namedPublication = Shipment::query()->create([
                        'subscription_month_id' => $nextPublicationMonth->id,
                        'name' => $nextPublicationMonth->name,
                        'purchasable_type' => $product->variant->getMorphClass(),
                        'purchasable_id' => $product->variant->id,
                        'subscription_id' => $subscription->id,
                        'order_id' => $subscription->order_id,
                        'zone_id' => $subscription->zone_id,
                        'address_id' => $address?->id,
                        'label_path' => null,
                        'amount' => $subscription->lunar_order->total,
                        'line_one' => $address->line_one,
                        'line_two' => $address->line_two,
                        'city' => $address->city,
                        'state' => $address->state,
                        'postcode' => $address->postcode,
                    ]);
//                    $namedPublication = NamedPublication::create([
//
//                        'subscription_id' => $subscription->id,
//                        'subscription_month_id' => $nextPublicationMonth->id,
//                        'name' => $nextPublicationMonth->name,
//                        'amount' => $subscription->order->amount, // Get amount from the related order
//                        'subscription_type' => $subscription->subscription_type
//                    ]);

                    $addressData = [
                        'name' => $subscription->name,
                        'street1' => $subscription->address_line_1,
                        'street2' => $subscription->address_line_2,
                        'street3' => $subscription->apt_number,
                        'city' => $subscription->city,
                        'state' => $subscription->state,
                        'postalCode' => $subscription->postal_code,
                        'country' => 'US',
                        'phone' => $subscription->customer->phone,
                        'residential' => true,  // Placeholder, change as needed
                    ];

                    $shipStationOrderData = [
                        'orderNumber' => $namedPublication->id,
                        'orderDate' => $namedPublication->created_at->toIso8601String(),
                        'orderStatus' => 'awaiting_shipment',
                        'billTo' => $addressData,
                        'shipTo' => $addressData
                    ];
                    if (!empty($this->fields->shipment_date)) {
                        $labelResult = null;
                        try {
                            // Create order in ShipStation
                            $result = $shipStationService->createOrder($shipStationOrderData);

                            if (!$result) {
                                throw new \Exception('Failed to create ShipStation order.');
                            }

                            $namedPublication->update([
                                'order_id' => $result['orderId'] ?? null,
                            ]);
                            // Prepare data to create label
                            $labelData = [
                                "orderId" => $result['orderId'],
                                "carrierCode" => $config->carrier_code,
                                "serviceCode" => $config->service_code,
                                "confirmation" => "delivery",
                                "shipDate" => $this->fields->shipment_date,
                                "packageCode" => $config->package_code,
                                "weight" => [
                                    "value" => $config->weight_value,
                                    "units" => $config->weight_units
                                ],
                                "testLabel" => env('SHIPSTATION_TEST')
                            ];

                            // Create label for the order
                            $labelResult = $createLabelForOrderService->createLabelForOrder($labelData, $batchId, $namedPublication->id);

                            $trackingData = [
                                'user_name' => $subscription->name,
                                'tracking_name' => $labelResult['trackingNumber'],
                                'issue' => $nextPublicationMonth->name,
                                'tracking_url' => 'https://tools.usps.com/go/TrackConfirmAction_input?strOrigTrackNum=' . $labelResult['trackingNumber'],
                            ];
                            (new TrackingInformation)
                                ->withData($trackingData)
                                ->withUser($customer)
                                ->sendTo();

                            if (!$labelResult) {
                                throw new \Exception('Failed to create ShipStation label.');
                            }
                        } catch (\Exception $e) {
                            $detailedMessage = $e->getMessage();

                            if ($labelResult) {
                                $detailedMessage .= '. API Response: ' . json_encode($labelResult);
                            }

                            Log::error($detailedMessage);
                            $errorMessages[] = $detailedMessage;
                        }
                    }
                }
            }
            $mergedPdfPath = $createLabelForOrderService->mergeBatchPdfs($batchId);

            if ($mergedPdfPath) {
                Log::info("ZIP file generated at path: {$mergedPdfPath}");
            } else {
                Log::warning("Failed to generate ZIP file for batch ID: {$batchId}");
            }
        } else {
            foreach ($subscriptions as $subscription) {
                $updatedSubscriptionIds[] = $subscription->id;
            }
        }

        $currentTimestamp = Carbon::now()->format('Y_m_d_H_i');
        $fileName = "Active_subscriptions_" . $currentTimestamp;

        $activeIssuesExport = new ActiveSubscriptionExport($updatedSubscriptionIds);

        $fileExtension = '.csv';
        $filePath = 'public/exports/Active/' . $fileName . $fileExtension;

        $exportDetails = $exportService->createVersionedExport($fileName, $filePath, $activeIssuesExport);
        $errorString = implode('. ', $errorMessages);

        $data = [
            'name' => $exportDetails['name'],
            'id' => $exportDetails['id'],
            'errors' => $errorString
        ];


        $notificationService->adminExportJobCompleted($data);
    }
}
