<?php

namespace App\Jobs\Subscription;

use App\Cart;
use App\Services\NotificationService;
use App\Subscription;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Lunar\Facades\Payments;

class ResumeJob implements ShouldQueue
{
    use Queueable;

    protected $id;
    protected $product = null;
    protected $free = false;
    protected $resource = 'auto-renew';
    protected $failedRenew = false;

    public function __construct(
        $id,
        $product = null,
        $free = false,
        $resource = 'auto-renew',
        $failedRenew = false,
    )
    {
        $this->id = $id;
        $this->product = $product;
        $this->free = $free;
        $this->resource = $resource;
        $this->failedRenew = $failedRenew;
    }

    /**
     * Execute the job.
     */
    public function handle(NotificationService $service): void
    {
        $subscription = Subscription::query()->find($this->id);
        logToCsv('renew', "resuming #$this->id");
        logToCsv('renew', $subscription->toJson());

        $customer = $subscription->customer;

        $cart = Cart::query()->create([]);

        if (!$this->product) {
            $cycleId = getCycleIds()[$subscription->subscription_type];
            $this->product = \App\Product::query()->find($cycleId);
        }
        $cart->addOrUpdate($this->product->variant);

        if($subscription->coupon_code) {
            $cart->update([
                'coupon_code' => $subscription->coupon_code
            ]);
        }

        $cart->setShippingAddress([
            'country_id' => 236,
            'title' => null,
            'first_name' => $customer->name,
            'last_name' => $customer->name,
            'city' => $subscription->city,
            'name' => $subscription->name,
            'state' => $subscription->state,
            'postcode' => $subscription->postal_code,
            'line_one' => $subscription->address_line_1,
            'line_two' => $subscription->address_line_2,
            'shipping_option' => 'basic',
        ]);

        $cart->setBillingAddress(
            $cart->shippingAddress
        );

        $cart->calculate();
        $order = $cart->createOrder();

        if($this->free) {
            $order->update([
                'status' => 'cash',
                'customer_id' => $customer->id,
            ]);
            $subscription_type = $this->product->name;
            $months = $subscription_type === 'yearly' ? 12 : 1;
            $finish = $subscription_type === 'yearly' ? '+1 year' : '+1 month';

            $subscription->increment('issues', $months);

            $order->digitalLines->first()?->update([
                'meta->subscription' => $subscription
            ]);
            app()->make(\App\Services\NotificationService::class)->subscriptionRenewalConfirmation($order, $subscription);
            return;
        }

        $order->update([
            'meta' => [
                'creditCardData' => collect($subscription->creditCard)->merge(['creditCardId' => $subscription->creditCard->id]),
                'customer_id' => $customer->id,
                'payment_type' => 'credit'
            ],
            'customer_id' => $customer->id,
            'resource' => $this->resource,
        ]);

        $driver = Payments::driver($subscription->creditCard->payment_method);
        $driver->order($order);

        if ($driver->charge()->success) {
            $order->update([
                'status' => 'payment-received'
            ]);
            $subscription_type = $this->product->name;
            $months = $subscription_type === 'yearly' ? 12 : 1;
            $finish = $subscription_type === 'yearly' ? '+1 year' : '+1 month';

            $subscription->increment('issues', $months);

            $order->digitalLines->first()?->update([
                'meta->subscription' => $subscription
            ]);
            app()->make(NotificationService::class)->subscriptionRenewalConfirmation($order, $subscription);
        } else {
            $subscription->update([
                'renew' => $this->failedRenew,
                'meta->failed_at' => now(),
            ]);

            $order->update([
                'status' => 'payment-declined'
            ]);

            if($this->failedRenew == true){
                $service->subscriptionRenewalFailedWillRetry(
                    order: $order,
                    subscription: $subscription,
                );
            }else{
                $service->subscriptionRenewalFailedFinal(
                    order: $order,
                    subscription: $subscription
                );
            }
        }
    }
}
