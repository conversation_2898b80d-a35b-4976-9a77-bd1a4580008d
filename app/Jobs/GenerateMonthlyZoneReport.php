<?php

namespace App\Jobs;

use App\Exports\MonthlyExport;
use App\Services\NotificationService;
use App\SubscriptionMonth;
use App\Zone;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Export;
use Illuminate\Support\Facades\Storage;

class GenerateMonthlyZoneReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $subscription_month_id;
    protected $zone_id;

    public function __construct($subscription_month_id, $zone_id)
    {
        $this->subscription_month_id = $subscription_month_id;
        $this->zone_id = $zone_id;
    }

    public function handle(NotificationService $notificationService)
    {
        Log::info('Job Started');

        $zone = Zone::find($this->zone_id);
        $month = SubscriptionMonth::find($this->subscription_month_id);

        if (!$zone) {
            return;
        }

        $zoneName = $zone->name;
        $monthName = $month->name;

        $fileName = Carbon::now()->format('Y-m-d-H-i') . "-{$monthName}-subscriptions-" . $zone->name;
        $fileExtension = '.csv';

        $filePath = "public/exports/{$zoneName}/{$fileName}{$fileExtension}";

        Storage::makeDirectory("public/exports/{$zoneName}");

        Excel::store(new MonthlyExport($this->subscription_month_id, $this->zone_id), $filePath);
        Log::info('Esta es la zon' . $this->zone_id);

        $export = Export::create([
            'name' => $fileName,
            'file_path' => $filePath,
            'zone_id' => $this->zone_id
        ]);

        $data = [
            'name' => $export->name,
            'id' => $export->id
        ];

        $notificationService->adminExportJobCompleted($data);

    }
}
