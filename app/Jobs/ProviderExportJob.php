<?php

namespace App\Jobs;

use App\Emails\ProviderReport;
use App\Services\NotificationService;
use App\User;
use App\Zone;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Export;
use App\Exports\ZoneExport;
use Illuminate\Support\Facades\Storage;

class ProviderExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $zone_id;
    protected $subscription_month_id;

    /**
     * Create a new job instance.
     *
     * @param int $zone_id
     * @return void
     */
    public function __construct($zone_id)
    {
        $this->zone_id = $zone_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(NotificationService $notificationService)
    {
        Log::info('Job Started');

        $zone = Zone::find($this->zone_id);
        $provider = $zone->provider;
        if (!$zone) {
            return;
        }

        $zoneName = $zone->name;
        $fileName = Carbon::now()->format('Y-m-d-H-i') . "-{$zone->provider->name}-subscriptions-" . $zone->name;
        $fileExtension = '.csv';

        $filePath = "public/exports/{$zone->provider->name}/{$fileName}{$fileExtension}";

        Storage::makeDirectory("public/exports/{$zone->provider->name}");

        Excel::store(new ZoneExport($this->zone_id), $filePath);
        Log::info('Esta es la zon' . $this->zone_id);
        $export = Export::create([
            'name' => $fileName,
            'file_path' => $filePath,
            'zone_id' => $this->zone_id
        ]);

        // Base64 encode the generated file
        $fileContent = Storage::get($filePath);
        $fileBase64 = base64_encode($fileContent);

        $attachment = [
            [
                "Content" => $fileBase64,
                "Name" => "{$fileName}{$fileExtension}",
                "Type" => "text/csv"
            ]
        ];

        $data = [
            'name' => $zone->provider->name,
            'zone_name' => $zoneName,
            'report_name' => "{$fileName}{$fileExtension}",
            'id' => $export->id,
        ];

        // Admin notification
        $notificationService->adminExportJobCompleted($data);

        if ($provider) {
            $providerEmail = $provider->email;
            $additionalEmailsJson = $provider->additional_emails;

            // Handle additional emails based on the type of $additionalEmailsJson
            if (is_array($additionalEmailsJson)) {
                $additionalEmails = $additionalEmailsJson;
            } elseif (is_string($additionalEmailsJson)) {
                $additionalEmails = json_decode($additionalEmailsJson, true);
            }

            // Sending email to the main provider
            if ($providerEmail) {
                (new ProviderReport)
                    ->withData($data)
                    ->withAttachments($attachment)  // Attach the file
                    ->withUser(User::make(['email' => $providerEmail]))
                    ->sendTo();
            }

            // Sending emails to additional providers if any
            if (isset($additionalEmails) && is_array($additionalEmails)) {
                foreach ($additionalEmails as $emailObj) {
                    if (isset($emailObj['email'])) {
                        (new ProviderReport)
                            ->withData($data)
                            ->withAttachments($attachment)  // Attach the file
                            ->withUser(User::make(['email' => $emailObj['email']]))
                            ->sendTo();
                    }
                }
            }
        }
    }
}
