<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Route4Me\Route;

class SubscriptionRoute extends Model
{
    use HasFactory;

    public $guarded = [];

    public $casts = [
        'meta' => 'array'
    ];

    public function subscriptions()
    {
        return $this->belongsToMany(Subscription::class);
    }

    public function zone()
    {
        return $this->belongsTo(Zone::class);
    }

    public function subscriptionSubscriptionRoutes()
    {
        return $this->hasMany(SubscriptionSubscriptionRoute::class);
    }

    public function updateSortDataFromR4M()
    {
        $addresses = Route::getRoutes(['route_id' => $this->route_id])->addresses;

        $shipmentIds = collect($addresses)->filter()->pluck('custom_fields_str_json')
            ->map(function ($custom_fields) {
                return data_get(json_decode($custom_fields), 'ShipmentId');
            })
            ->filter()
            ->values();

        $this->update([
            'meta->sort' => $shipmentIds,
        ]);
    }
}
