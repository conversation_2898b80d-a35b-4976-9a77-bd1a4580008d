<?php

namespace App\Traits;

use App\Note;

trait NotesTrait
{
    public function messages()
    {
        return $this->morphMany(Note::class, 'model');
    }

    public function getNotesAttribute()
    {
        return [
            'model_id' => $this->id,
            'model_type' => get_class($this),
            'notes' => $this->messages->sortByDesc('created_at')->values()->map->frontEnd,
        ];
    }
}
