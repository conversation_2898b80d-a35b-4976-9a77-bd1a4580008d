<?php

namespace App\Traits;

trait PaymentTrait
{
    public function getBalanceAttribute()
    {
        $payments = $this->payments->where('status', '!=', 'cancelled');
        if (!$payments) {
            return $this->price;
        }
        return $this->price - ($payments->map->amount->sum() - $payments->map(function ($payment) {
                    return optional($payment->creditCardPayment)->refund;
                })->sum());
    }

    public function setBalanceAttribute()
    {
    }

    public function setAddPaymentAttribute($value)
    {
    }

    public function getAddPaymentAttribute()
    {
        return;
    }

    public function setCardAttribute($value)
    {
    }

    public function getCardAttribute()
    {
        return;
    }

    public function setExpirationAttribute($value)
    {
    }

    public function getExpirationAttribute()
    {
        return;
    }

    public function setSecurityAttribute($value)
    {
    }

    public function getSecurityAttribute()
    {
        return;
    }

    public function setAmountAttribute($value)
    {
    }

    public function getAmountAttribute()
    {
        return;
    }
}
