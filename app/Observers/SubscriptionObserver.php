<?php

namespace App\Observers;

use App\CreditCard;
use App\Cycle;
use App\Http\Controllers\RouteForMeController;
use App\Payment;
use App\Price;
use App\Product;
use App\Promo;
use App\Services\NotificationService;
use App\Subscription;
use App\Zone;
use DateTime;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Lunar\Facades\Payments;

class SubscriptionObserver
{
    public function created(Subscription $subscription)
    {
        if (!!optional($subscription->gift)->extension) {
            $subscription->withoutEvents(function () use ($subscription) {
                $subscription->updateQuietly(['finish' => now()->parse($subscription->finish)->addMonths(optional($subscription->gift)->months)]);
            });
        }

        Log::info('Esta es la subscripcion' . $subscription);

        // Fetch subscription type and determine the number of months
//        $subscription_type = $subscription->subscription_type;
//        $months = $subscription_type === 'yearly' ? 12 : 1;


        // Fetch the correct cycle based on months
//        $cycle = Cycle::where('months', $months)->first();

//            if (!$cycle) {
        // abort(400, 'Selected cycle does not exist.');
//            }

        if (request()->is('nova-api/*')) {
            $subscription->updateZoneBasedOnPostalCode();
            /*
            $price = null;
            $subscriptionType = $subscription->subscription_type;
            $selectedPrice = Price::where('type', $subscriptionType)->first();
            $promo = Promo::find(request()->promo_id);
            // Setting the base price
            if ($selectedPrice) {
                $price = $selectedPrice->amount;
            } else {
                abort(400, 'Invalid subscription type selected'); // Or set $price to some default value if preferred
            }

            // Check for promo and adjust price if applicable
            if ($promo) {
                $promoPriceData = app('App\Http\Controllers\PromoController')->calculatePriceWithPromo(new Request([
                    'subscription_type' => $subscription->subscription_type,
                    'promo_code' => $promo->code
                ]));

                if (!isset($promoPriceData->getData()->error)) {
                    $price = $promoPriceData->getData()->Total;
                }
            }

            $order = \App\Order::create([
                'amount' => $price,
                'group_id' => 'Admin',
                'promo_id' => optional($promo)->id,
                'subscription_id' => $subscription->id,
                'customer_id' => $subscription->customer->id,
            ]);

            $credit_card = CreditCard::find(request()->credit_card_id);

            if (request()->payment_type == 'Credit Card') {
                if ($credit_card) {
                    $credit_card_payment = $credit_card->charge($price, $subscription->customer->name);
                    Payment::create([
                        'amount' => $price,
                        'order_id' => $order->id,
                        'credit_card_payment_id' => $credit_card_payment->id,
                        'credit_card_id' => $credit_card->id,
                        'payment_type' => 'Credit Card',
                    ]);
                }
            } else {
                Payment::create([
                    'amount' => $price,
                    'order_id' => $order->id,
                    'credit_card_payment_id' => null,
                    'credit_card_id' => null,
                    'payment_type' => request()->payment_type,
                ]);
            }
            $order = $order ? $order : \App\Order::where(['subscription_id' => $subscription->id])->first();
            $customer = $order->customer;
            $last_name = explode(' ', $customer->name);

            $promo = '';
            if (isset($promoPriceData)) {
                $promo = $promoPriceData->getData()->discount;
            }
*/
            //$this->notificationService->sendNewSubscriptionNotification($subscription, $order, $promo);
        }
    }
    public function updated(Subscription $subscription)
    {
        \Log::info('updated subscription #' . $subscription->id);
        // Check if the address fields have been changed
        if (
            $subscription->isDirty('address_line_1') ||
            $subscription->isDirty('address_line_2') ||
            $subscription->isDirty('city') ||
            $subscription->isDirty('state') ||
            $subscription->isDirty('postal_code')
        ) {
            $oldAttributes = $subscription->getOriginal();
            $newAttributes = array_merge($oldAttributes, $subscription->getDirty());

            app()->make(NotificationService::class)->subscriptionUpdateAddress($oldAttributes,$newAttributes);
        }

        return;
        if (
            $subscription->isDirty('cycle_id')
        ) {
            $old = $subscription->getOriginal('cycle_id');
            $new = $subscription->getDirty()['cycle_id'];

            $cycles = Cycle::query()->whereIn('id', [$old, $new])->get();

            throw_if(
                count($cycles) != 2,
                \Exception::class,
                'Cycle not found!'
            );

            $cycleOld = $cycles->where('id', $old)->first();
            $cycleNew = $cycles->where('id', $new)->first();


            $old_type = $cycleOld->months === 1 ? 'monthly' : 'yearly';
            $new_type = $cycleNew->months === 1 ? 'monthly' : 'yearly';

            $creditCard = $subscription->creditCard;

            if(!$creditCard){
                return;
            }

            $prices = Product::query()->cycles()->get();

            throw_if(
                count($prices) != 2,
                \Exception::class,
                'Prices not found!'
            );

            $old_price = $prices->firstWhere('name', $old_type)->price;
            $new_price = $prices->firstWhere('name', $new_type)->price;

            $issues_left = $subscription->issues;
            $adjusted_old_price = Subscription::calculateRefundAmount($old_price, $issues_left);

            $order = $subscription->lunar_order;
            $orderSubscriptionLine = $order->lines()->where('type', 'digital')
                ->where('meta->subscription->id', $subscription->id)->first();

            $promoDiscount = $orderSubscriptionLine?->discountTotal?->decimal() ?? 0;

            $diff = $new_price - $adjusted_old_price + $promoDiscount;

            if ($diff > 0) {
                $paymentDriver = Payments::driver($creditCard->payment_method);
                $paymentDriver->order($order);
                $paymentDriver->setAmount($diff);
                $paymentDriver->charge();
                $subscription->issues = 11;
                $subscription->saveQuietly();
            } elseif ($diff < 0) {
                $creditCard->refund(abs($diff)*100, $order?->id); //++
                $subscription->issues = 1;
                $subscription->saveQuietly();
            }
        }
    }

    public function creating(Subscription $subscription)
    {
        \Log::info('Creating subscription');
        $subscription->id = Subscription::withTrashed()->max('id') + 3;
        $subscription->user_id = auth()->id();
        if (request()->is('nova-api/*')) {
            $subscription_type = $subscription->subscription_type;
            $months = $subscription_type === 'yearly' ? 12 : 1;
            $finish = $subscription_type === 'yearly' ? '+1 year' : '+1 month';
            $cycle = Cycle::where('months', $months)->first();
            $subscription->finish = (new DateTime())->modify($finish);
            $subscription->start = new DateTime();
            $subscription->issues = $months;
            if (!$cycle) {
                abort(400, 'Selected cycle does not exist.');
            }

            $subscription->cycle_id = $cycle->id;

            if (!$subscription->name) {
                $subscription->name = $subscription->customer->name;
            }
        }
    }

    public function saving(Subscription $subscription)
    {
        \Log::info('Saving subscription');
        if (request()->has('credit_card_id')) {
            $subscription->credit_card_id = request('credit_card_id');
        }
        $subscription->verified_address = false;
        $subscription->meta = array_merge($subscription->meta ?? [], RouteForMeController::geocodeAddress($subscription->addressString()) ?? []);
        if (collect(explode(',', data_get($subscription, 'meta.address')))->count() >= 4) {
            $subscription->verified_address = true;
        }
    }

    public function updating(Subscription $subscription)
    {
        \Log::info('Updating subscription');

        if($subscription->isDirty('postal_code')) {
            if ($zone = Zone::getZip($subscription->postal_code)) {
                $subscription->zone_id = $zone->id;
            } else {
                $subscription->zone_id = 0;
                Log::warning('Subscription with postal code ' . $subscription->postal_code . ' does not have an associated Zone.');
            }
        }


        if($subscription->isDirty(['address_line_1', 'city', 'state', 'postal_code'])) {
            $subscription->verified_address = false;
            $subscription->meta = array_merge($subscription->meta ?? [], RouteForMeController::geocodeAddress($subscription->addressString()) ?? []);
            if (collect(explode(',', data_get($subscription, 'meta.address')))->count() >= 4) {
                $subscription->verified_address = true;
            }
        }
        if( $subscription->isDirty(['cycle_id', 'subscription_type'])) {
            $cycles = Cycle::all();
            if($subscription->isDirty(['subscription_type'])) {
                $months = $subscription->subscription_type === 'yearly' ? 12 : 1;
                $newCycle = $cycles->where('months', $months)->first();
                $newType = $subscription->subscription_type;
                $oldType = $subscription->getOriginal('subscription_type');
            }
            else {
                $newCycle = $cycles->where('id', $subscription->cycle_id)->first();
                $newType = $newCycle->months == 12 ? 'yearly' : 'monthly';
                $oldType = $subscription->getOriginal('subscription_type');
                $months = $newCycle->months;
            }
            $subscription->subscription_type = $newType;
            $subscription->cycle_id = $newCycle->id;
        }
    }
}
