<?php

namespace App\Listeners;

use App\Emails\ProviderReport;
use App\Setting;
use App\User;
use Spatie\MediaLibrary\MediaCollections\Events\MediaHasBeenAdded;

class EmailProviderAfterUpload
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function handle(MediaHasBeenAdded $event)
    {
        $media = $event->media;

        if ($media->disk != 'exports') {
            return;
        }

        $zone = $media->model;

        $data = [
            'link' => $media->getTemporaryUrl(now()->addDays(3)),
            'date' => $media->getCustomProperty('date'),
            'zone' => $zone->name,
            'parsha' => $media->getCustomProperty('parsha'),
        ];
        // (new \App\Emails\ProviderReport) ->withData($data) ->sendTo(\App\User::make(['email'=> '<EMAIL>']));

        if ($email = optional($zone->provider)->email) {
            (new ProviderReport)
                ->withData($data)
                ->withUser(User::make(['email' => $email]))
                ->sendTo();
        }

        collect(data_get($zone->provider, 'additional_emails'))
            ->map(function ($item) use ($data) {
                (new ProviderReport)
                    ->withData($data)
                    ->withUser(User::make(['email' => data_get($item, 'email')]))
                    ->sendTo();
            });

        collect(
            setting()->getOptions('additional_provider_emails')
        )->each(function ($email) use ($data) {
            (new ProviderReport)
                ->withData($data)
                ->withUser(User::make(['email' => trim($email)]))
                ->sendTo();
        });
    }
}
