<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionMonth extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'is_current' => 'boolean',
        'is_next' => 'boolean',
    ];

    public function getStatusAttribute()
    {
        if ($this->is_current) return 'current';
        if ($this->is_next) return 'next';
        return null;
    }

    public function subscriptions()
    {
        return $this->hasMany(\App\Subscription::class, 'subscription_month_id');
    }

    public function named_publications()
    {
        return $this->hasMany(\App\NamedPublication::class, 'subscription_month_id');
    }

    /**
     * The "booting" method of the resource.
     *
     * @return void
     */
    protected static function _boot()
    {
        parent::boot();

        static::saving(function ($model) {
            if ($model->is_current && !$model->hasSingleCurrentOrNext('is_current', true)) {
                $existingCurrentMonth = self::where('is_current', true)->first();
                $existingMonthName = $existingCurrentMonth ? $existingCurrentMonth->name : 'Unknown';
                throw new \Exception("There can only be one 'Current' month. Please set the current month '{$existingMonthName}' to 'None'.");
            }

            if ($model->is_next && !$model->hasSingleCurrentOrNext('is_next', true)) {
                $existingNextMonth = self::where('is_next', true)->first();
                $existingMonthName = $existingNextMonth ? $existingNextMonth->name : 'Unknown';
                throw new \Exception("There can only be one 'Next' month. Please set the next month '{$existingMonthName}' to 'None'.");
            }
        });
    }


    private function hasSingleCurrentOrNext($attribute, $value)
    {
        return $this->where($attribute, $value)->where('id', '!=', $this->id)->count() < 1;
    }

    public function getIsCurrentAttribute()
    {
        return hebDateArray()['month'] == $this->month
            && hebDateArray()['year'] == $this->year;
    }

    public function getIsNextAttribute()
    {
        return hebDateArray(nextRoshChodesh())['month'] == $this->month
            && hebDateArray(nextRoshChodesh())['year'] == $this->year;
    }

    public static function getFindByDate($year, $month, $day = 1)
    {
        $name = hebDateStringArray(HebArrayToEnglishDate(['day'=> $day, 'year'=> $year, 'month' => $month]));

        return static::firstOrCreate(
            [
                'month' => $month,
                'year' => $year,
            ],
            [
                'name' => "{$name['month']} {$name['year']}",
                'description' => static::getDescription([
                    'month' => $month,
                    'year' => $year,
                ])
            ]
        );
    }


    public static function getCurrent()
    {
        $current = currentHebRoshChodesh();
        $name = hebDateStringArray(nextRoshChodesh());

        return static::firstOrCreate(
            [
                'month' => $current['month'],
                'year' => $current['year'],
            ],
            [
                'name' => "{$name['month']} {$name['year']}",
                'description' => static::getDescription(currentHebRoshChodesh())
            ]
        );
    }

    public static function getNext($date = null)
    {
        $next = nextHebRoshChodesh($date);
        $name = hebDateStringArray(nextRoshChodesh($date));
        return static::firstOrCreate(
            [
                'month' => $next['month'],
                'year' => $next['year'],
            ],
            [
                'name' => "{$name['month']} {$name['year']}",
                'description' => static::getDescription(currentHebRoshChodesh($date))
            ]
        );
    }

    public static function pastMonths()
    {
        return static::where('year', '<', hebDateArray()['year'])
            ->orwhere(function ($query) {
                return $query
                    ->where('year', '=', hebDateArray()['year'])
                    ->where('month', '<=', hebDateArray()['month']);
            })
            ->orderBy('year')
            ->orderBy('month');
    }

    public static function getDescription($date)
    {
        $startYear = config('custom.start.year');
        $startMonth = config('custom.start.month');

        if ($date['month'] == $startMonth) {
            $year = ($date['year'] + 1) % $startYear;
        } else {
            $year = $date['year'] % $startYear;
        }
        $month = ($date['month'] % $startMonth) + 1;

        if(!isJewishLeapYear($date['year']) && $month >= 8){
            $month--;
        }

        return "Year " . $year . ' Book ' . $month;
    }

    public function createProduct()
    {
        $product = \App\ProductVariant::where('sku', 'issue_' . $this->id)->first()?->product;
        if (!$product) {
            $config = \App\ShipStationConfiguration::find(1);
            $product = \App\Product::createFromArray([
                'name' => $this->name,
                'description' => $this->description,
                'product_type_id' => 2,
                'price' => setting()->getValue('price_for_subscription_month'),
                'length' => 11,
                'width' => 8.5,
                'height' => 1,
                'weight' => $config->weight_value,
                'issue_id' => $this->id,
            ]);
        }
        return $product;
    }
}
