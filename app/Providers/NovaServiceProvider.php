<?php

namespace App\Providers;

use App\Nova\ContactInfo;
use App\Nova\Customer;
use App\Nova\Cycle;
use App\Nova\Dashboards\Main;
use App\Nova\Discount;
use App\Nova\Export;
use App\Nova\Gift;
use App\Nova\Issue;
use App\Nova\NamedPublication;
use App\Nova\Order;
use App\Nova\Product;
use App\Nova\Setting;
use App\Nova\Shipment;
use App\Nova\Subscriber;
use App\Nova\Subscription;
use App\Nova\SubscriptionRoute;
use App\Nova\User;
use App\Nova\Zone;
use Illuminate\Support\Facades\Blade;
use Laravel\Nova\Menu\MenuSection;
use Laravel\Nova\Nova;
use Laravel\Nova\Menu\MenuItem;
use Illuminate\Support\Facades\Gate;
use Laravel\Nova\NovaApplicationServiceProvider;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Nova::script('wds-order-repeater-product', base_path('nova-components/OrderRepeaterProduct/dist/js/field.js'));
        Nova::style('wds-order-repeater-product', base_path('nova-components/OrderRepeaterProduct/dist/css/field.css'));

        Nova::script('wds-stripe-card-field', base_path('nova-components/StripeCardField/dist/js/field.js'));
        Nova::style('wds-stripe-card-field', base_path('nova-components/StripeCardField/dist/css/field.css'));

        Nova::script('wds-stripe-belong-field', base_path('nova-components/StripeBelongToField/dist/js/field.js'));
        Nova::style('wds-stripe-belong-field', base_path('nova-components/StripeBelongToField/dist/css/field.css'));

        Nova::style('nova-sortable', base_path('vendor/outl1ne/nova-sortable/dist/css/tool.css'));


        Nova::style('user', public_path('css/user.css'));
        Nova::footer(function () {
            return Blade::render('
            <p class="mt-8 text-center text-xs text-80">
                &copy; {!! $year !!} Vinderkind
                <span class="px-1">&middot;</span>
                Powered by <a href="https://capitalc.co" target="_blank" class="ml-0 text-primary dim no-underline">Capital C</a>
                <span class="px-1">&middot;</span>
                Version {!! $version !!}
            </p>'
                , [
                    'version' => '1.0.0',
                    'year' => date('Y'),
                ]);
        });

        if(config('custom.is_nova_custom_menu')) {
            Nova::mainMenu(function () {
                return [
                    MenuSection::dashboard(Main::class)->icon('chart-bar'),

                    MenuSection::make('Store', [
                        MenuItem::resource(Order::class),
                        MenuItem::resource(Shipment::class),
                        MenuItem::resource(Customer::class),
                        MenuItem::resource(Product::class),
//                        MenuItem::resource(Promo::class),
                        MenuItem::resource(Discount::class),
                        MenuItem::resource(Cycle::class),
                        // MenuItem::resource(Cart::class),
                        MenuItem::resource(Gift::class),
                        MenuItem::resource(Issue::class),
                    ])
                    ->icon('shopping-cart')->collapsable(),

                    MenuSection::make('Subscriptions', [
                        MenuItem::resource(Subscription::class),
                        // MenuItem::resource(ChargeSubscription::class),
                    ])
                    ->icon('currency-dollar')
                    ->collapsable(),



                    MenuSection::make('Settings', [
                        MenuItem::resource(Setting::class),
                        MenuItem::resource(Subscriber::class),
                        MenuItem::resource(ContactInfo::class),
                        // MenuItem::resource(Faq::class),
                        MenuItem::resource(SubscriptionRoute::class),
                        MenuItem::resource(User::class),
                        MenuItem::resource(Zone::class),
                        MenuItem::resource(Export::class),
                    ])->icon('cog')->collapsable(),

                    MenuSection::make('Other', [
                        // MenuItem::resource(Cover::class),
                        MenuItem::resource(NamedPublication::class),
                    ])->icon('hashtag')->collapsable(),

                    // MenuSection::make('Configurations', [
                    //     MenuItem::resource(Provider::class),
                    // ])->icon('adjustments')->collapsable(),


                    // MenuSection::make('Campaigns', [
                    //     MenuItem::resource(Campaign::class),
                    // ])->icon('document-text')->collapsable(),

                    // MenuSection::make('External Campaigns', [
                    //     MenuItem::resource(Company::class),
                    //     MenuItem::resource(ExternalCampaign::class),
                    // ])->icon('users')->collapsable(),

                        // MenuItem::resource(SubscriptionMonth::class),


//
//                MenuSection::make('Content', [
//                    MenuItem::resource(Series::class),
//                    MenuItem::resource(Release::class),
//                ])->icon('document-text')->collapsable(),
                ];
            });
        }
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()
            ->withAuthenticationRoutes()
            ->withPasswordResetRoutes()
            ->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', function () {
            return true;
        });
    }


    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [
            // new \Anaseqal\NovaImport\NovaImport,
        ];
    }

    protected function dashboards()
    {
        return [
            new Main,
        ];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if (request()->is('nova-api/*')) {
            config(['app.debug' => true]);
        }
    }
}
