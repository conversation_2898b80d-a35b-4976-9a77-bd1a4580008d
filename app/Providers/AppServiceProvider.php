<?php

namespace App\Providers;

use App\CreditCard;
use App\Lunar\DiscountTypes\AmountOff;
use App\Lunar\DiscountTypes\BuyXGetY;
use App\Lunar\Drivers\CustomTaxDriver;
use App\Lunar\Modifiers\CustomShippingModifier;
use App\Lunar\Payments\Stripe;
use App\Lunar\Payments\UsaEPay;
use App\Observers\CreditCardObserver;
use App\Services\SettingService;
use App\Subscription;
use App\Observers\SubscriptionObserver;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use App\User;
use Illuminate\Support\Facades\Gate;
use Lunar\Admin\Support\Facades\LunarPanel;
use Lunar\Base\ShippingModifiers;
use Lunar\Facades\Discounts;
use Lunar\Facades\Payments;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if (
            // true ||
            // request()->is('nova-api/search') || 
            env('LOG_SQL')
        ) {
            DB::listen(function ($query) {
                Log::channel('sql')->info($query->time . 'sec  ' . $query->sql);
                Log::channel('sql')->info($query->bindings);
            });
        }

        LunarPanel::register();


        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }


        $this->app->singleton(SettingService::class, function () {
            $class = new SettingService();
            $class->init();
            return $class;
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(ShippingModifiers $shippingModifiers)
    {
        Subscription::observe(SubscriptionObserver::class);
        CreditCard::observe(CreditCardObserver::class);

        Gate::define('viewPulse', function (User $user) {
            return $user->is_admin;
        });


        Payments::extend('stripe', function ($app) {
            return $app->make(Stripe::class);
        });

        Payments::extend('usaepay', function ($app) {
            return $app->make(UsaEPay::class);
        });

        $shippingModifiers->add(
            CustomShippingModifier::class
        );


        Discounts::addType(AmountOff::class);
        Discounts::addType(BuyXGetY::class);


        \Lunar\Facades\Taxes::extend('custom', function ($app) {
            return $app->make(CustomTaxDriver::class);
        });
    }
}
