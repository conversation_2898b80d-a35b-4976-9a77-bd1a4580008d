<?php

namespace App\Providers;

use App\Campaign;
use App\Policies\CampaignPolicy;
use App\Policies\SubscriberPolicy;
use App\Policies\UserPolicy;
use App\Subscriber;
use App\User;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model' => 'App\Policies\ModelPolicy',
        Campaign::class => CampaignPolicy::class,
        Subscriber::class => SubscriberPolicy::class,
        User::class => UserPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
    }
}
