<?php

namespace App;

class HebrewDate
{

    //see https://github.com/erelsgl/tnk/blob/master/script/prjot.php

    public static function JDToSortableJewish($jd)
    {
        // add zeros to make the date sortable:
        return
            preg_replace("|(\d+)/(\d+)/(\d+)|", "$3-$1-$2",  // year-month-day
                preg_replace("|/(\d)/|", "/0$1/", // add zeros to the day
                    preg_replace("|^(\d)/|", "0$1/",  // add zeros to the month
                        JDToJewish($jd))));
    }

    public static function nextShabbat($jd)
    {
        return $jd + (6 - jddayofweek($jd));  // 0 = rishon, ..., 6 = shabbat
    }

    public static function shabbatOfParshatBreshit($year)
    {
        $simchatTorah = jewishtojd(1, 22, $year);   // 22 Tishrey
        return self::nextShabbat($simchatTorah + 1);
    }

    public static function isLeapYear($year)
    {
        $yearAfterCycle = $year % 19;
        return
            $yearAfterCycle == 0 ||
            $yearAfterCycle == 3 ||
            $yearAfterCycle == 6 ||
            $yearAfterCycle == 8 ||
            $yearAfterCycle == 11 ||
            $yearAfterCycle == 14 ||
            $yearAfterCycle == 17;
    }

    /**
     * @param $jd a Julian day number.
     * @return true iff it's one of the days of Sukkot, Pesach (including Hol Hamoed), Shavuot, Yom Kippur or Rosh Hashana.
     */
    public static function isHoliday($jd)
    {
        list($month, $day, $year) = explode('/', jdtojewish($jd));
        return
            ($month == 1 && 1 <= $day && $day <= 2) ||    // Rosh Hashana
            ($month == 1 && $day == 10) ||              // Yom Kippur
            ($month == 1 && 15 <= $day && $day <= 22) ||  // Sukkot
            ($month == 8 && 15 <= $day && $day <= 21) ||  // Pesach
            ($month == 10 && $day == 6) ||              // Shavuot
            0;
    }


    public static function isCombinedWithNextParsha($parshaNumber, $workingShabbat, $isLeapYear, $sukkot, $pesach, $tishaBeav)
    {
        switch ($parshaNumber) {
            case 22:   // Vayakhel (combine with Pekudei?)
                // Check how many Saturdays exist from WorkingShabbat until the day before Passover. If there are less than 4 then combine Vayakhel with Pekudei (there are only 3 Saturdays left before Passover in years in which these portions are combined). Note that the algorithm goes only to the day before Passover so as not to include Passover in the count when Passover lands on Saturday, because Passover itself is a yom tov with its own special Torah portion. The WorkingShabbat is included in the count because it has to be a regular Shabbat.
                return ($pesach - $workingShabbat <= 21);
            case 27:   // Tazria (combine with Metzora?)
                return !$isLeapYear;
            case 29:   // Acharei Mot (combine with Kedoshim?)
                return !$isLeapYear;
            case 32:   // Behar (combine with Bechukotai?)
                // Outside Israel: Combine if NOT a leap year.
                return !$isLeapYear;
            // Inside Israel: Combine if Passover does not start on Shabbat AND NOT a leap year.
//				return (jddayofweek($pesach)!=6) && (!$isLeapYear);
            case 39:   // Chukat (combine with Balak?)
                // Outside Israel: Combine if Passover starts on Thursday (because in such a case the second day of Shavuot falls on Shabbat in the diaspora).
                return jddayofweek($pesach) == 4;
            // Inside Israel: Never combine.
//				return false;
            case 42:   // Mattot (combine with Masei?)
                return ($tishaBeav - $workingShabbat < 14);
            // Combine if there are less than 3 Saturdays from WorkingShabbat to Tisha B'Av (9th day of the month of Av), so that Devarim is the portion that will be read on the Shabbat before Tisha B'Av.

            case 51:  // Nitzavim (combine with Vayeilech?)
                // Determine the number of days until the coming Rosh HaShanah, then combine if >3 days.
                return ($sukkot - $workingShabbat > 17);

            default:
                return false;
        }
    }

    public static function parshaNumbers($jd)
    {
        $targetShabbat = self::nextShabbat($jd);
        list($month, $day, $year) = explode('/', jdtojewish($jd));
        $simchatTorah = jewishtojd(1, 22, $year);   // 22 Tishrey
        $relevantYear = ($jd < $simchatTorah ? $year - 1 : $year);

        $isLeapYear = self::isLeapYear($relevantYear);
        $simchatTorah = jewishtojd(1, 22, $relevantYear);   // 22 Tishrey
        $pesach = jewishtojd(8, 15, $relevantYear);   // 15 Nisan
        $tishaBeav = jewishtojd(12, 9, $relevantYear);
        $sukkot = jewishtojd(1, 15, $relevantYear + 1);   // 15 Tishrey of NEXT year
        $workingShabbat = self::nextShabbat($simchatTorah - 6);

        $parshaNumber = ($targetShabbat - $workingShabbat) / 7;
        if ($parshaNumber < 22) // this is one of the first 21 parshiot that occur prior to Vayakhel and are never combined
            return array($parshaNumber);

        $parshaNumber = 21;
        $workingShabbat += 21 * 7;


        while ($workingShabbat < $targetShabbat) {
            $workingShabbat += 7;
            if (self::isHoliday($workingShabbat)) {
                //print "<p>holiday=1: ".JDToSortableJewish($workingShabbat);
                continue;
            }
            $parshaNumber++;
            $isCombined = self::isCombinedWithNextParsha($parshaNumber, $workingShabbat, $isLeapYear, $sukkot, $pesach, $tishaBeav);
            if ($isCombined)
                $parshaNumber++;
        }

        if ($parshaNumber == 53 && $workingShabbat >= $sukkot)
            $parshaNumber = 54;

        if ($isCombined)
            return array($parshaNumber - 1, $parshaNumber);
        else
            return array($parshaNumber);
    }

    public static function getParshaString($date = null)
    {
        $date = now()->parse($date) ?? today();

        return 'פרשת ' . collect(self::parshaNumbers(unixtojd($date->timestamp)))
                ->map(function ($parsha) {
                    return static::$parshos[$parsha];
                })
                ->join(' - ');
    }

    public static $parshos = [
        1 => "בראשית",
        2 => "נח",
        3 => "לך לך",
        4 => "וירא",
        5 => "חיי שרה",
        6 => "תולדות",
        7 => "ויצא",
        8 => "וישלח",
        9 => "וישב",
        10 => "מקץ",
        11 => "ויגש",
        12 => "ויחי",
        13 => "שמות",
        14 => "וארא",
        15 => "בא",
        16 => "בשלח",
        17 => "יתרו",
        18 => "משפטים",
        19 => "תרומה",
        20 => "תצוה",
        21 => "כי תשא",
        22 => "ויקהל",
        23 => "פקודי",
        24 => "ויקרא",
        25 => "צו",
        26 => "שמיני",
        27 => "תזריע",
        28 => "מצרע",
        29 => "אחרי מות",
        30 => "קדשים",
        31 => "אמור",
        32 => "בהר",
        33 => "בחקתי",
        34 => "במדבר",
        35 => "נשא",
        36 => "בהעלתך",
        37 => "שלח",
        38 => "קורח",
        39 => "חקת",
        40 => "בלק",
        41 => "פינחס",
        42 => "מטות",
        43 => "מסעי",
        44 => "דברים",
        45 => "ואתחנן",
        46 => "עקב",
        47 => "ראה",
        48 => "שופטים",
        49 => "כי תצא",
        50 => "כי תבוא",
        51 => "נצבים",
        52 => "וילך",
        53 => "האזינו",
        54 => "וזאת הברכה",
    ];
}