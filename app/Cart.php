<?php

namespace App;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Lunar\Facades\CartSession;
use Lunar\Stripe\Facades\Stripe;


class Cart extends \Dystcz\LunarApi\Domain\Carts\Models\Cart
{
    public $with = ['lines'];

    protected $attributes = [
        'channel_id' => 1,
        'currency_id' => 1,
    ];

    public function incrementItem($purchasable, $increment)
    {
        $existing = app(
            config('lunar.cart.actions.get_existing_cart_line')
        )->execute(
            cart: $this,
            purchasable: $purchasable,
            meta: []
        );

        if ($existing) {
            if ($increment) {
                $existing->increment('quantity');
            } else {
                if ($existing->quantity == 1) {
                    $existing->delete();
                } else {
                    $existing->decrement('quantity');
                }
            }
        } else {
            $this->addOrUpdate($purchasable);
        }
    }

    public function addOrUpdate($purchasable, $quantity = 1)
    {
        $existing = app(
            config('lunar.cart.actions.get_existing_cart_line')
        )->execute(
            cart: $this,
            purchasable: $purchasable,
            meta: []
        );

        if ($existing) {
            $existing->update([
                'quantity' => $quantity,
            ]);

            // return $this;
        }

        $this->lines()->create([
            'purchasable_id' => $purchasable->id,
            'purchasable_type' => $purchasable->getMorphClass(),
            'quantity' => $quantity,
            'meta' => [],
        ]);

        // return $this;
    }

    public function removeItem($purchasable)
    {
        $existing = app(
            config('lunar.cart.actions.get_existing_cart_line')
        )->execute(
            cart: $this,
            purchasable: $purchasable,
            meta: []
        );

        if ($existing) {
            $existing->delete();
        }
    }

    public static function forgetCurrent()
    {
        $key = config('lunar.cart.session_key');
        session()->forget($key);
    }

    public static function getCurrent()
    {
        if ($id = session(config('lunar.cart.session_key'))) {
            return self::find($id);
        }

        $id = CartSession::current()->id;

        return self::find($id);
    }

    public static function getCurrentSignUp()
    {
        $key = 'lunar_signup_cart';

        if ($id = session($key)) {
            return self::find($id);
        }

        $cart = self::create([]);

        session([$key => $cart->id]);

        return $cart;
    }

    public static function forgetCurrentSignUp()
    {
        $key = 'lunar_signup_cart';
        session()->forget($key);
    }

    public function toArray()
    {
        return $this->lines;
    }

    public function lines(): HasMany
    {
        return $this->hasMany(CartLine::class, 'cart_id', 'id');
    }

    public static function frontEnd()
    {
        $cart = self::getCurrent();
        $cart->getEstimatedShipping([], setOverride: true);
        $cart->calculate();
        dd($cart);


        return [
            'id' => $cart->id,
            'products' => $cart
                ->lines->map(function ($line) {
                    $product = \App\Product::find($line->purchasable->product_id);
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'count' => $line->quantity,
                        'price' => $line->purchasable->prices->first()->price->decimal(),
                        'img' => $product->img,
                    ];
                }),
            'tax' => $cart->taxTotal->decimal(),
            'subTotal' => $cart->subTotal->decimal(),
            'total' => $cart->total->decimal(),
            'discountTotal' => $cart->discountTotal->decimal(),
            'coupon_code' => $cart->coupon_code,
            'shipping' => $cart->shippingSubTotal->decimal(),
            'postcode' => $cart->shippingAddress?->postcode,
            'isFullyDiscounted' => $cart->discountTotal->decimal() === $cart->subTotal->decimal() && $cart->total->decimal() == 0,
        ];
    }

    public function linesProduct()
    {
        return $this
            ->morphedByMany(\Lunar\Models\ProductVariant::class,
                'purchasable',
                'lunar_cart_lines',
            )
            ->withPivot(['quantity']);

    }

    public static function signupFrontEnd()
    {
        $cart = self::getCurrentSignUp();
        $cart->getEstimatedShipping([], setOverride: true);
        $cart = $cart->calculate();

        return [
            'id' => $cart->id,
            'cycle' => $cart
                ->lines
                ->filter(function ($line) {
                    return collect(getCycleIds())->contains($line->purchasable->product_id);
                })
                ->map(function ($line) {
                    $product = \App\Product::find($line->purchasable->product_id);
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'count' => $line->quantity,
                        'price' => $line->purchasable->prices->first()->price->decimal(),
                        'img' => $product->img,
                    ];
                })
                ->first(),
            'products' => $cart
                ->lines
                ->reject(function ($line) {
                    return collect(getCycleIds())->contains($line->purchasable->product_id);
                })
                ->map(function ($line) {
                    $product = \App\Product::find($line->purchasable->product_id);
                    return [
                        'id' => $product->id,
                        'count' => $line->quantity,
                        'price' => $line->purchasable->prices->first()->price->decimal(),
                        'img' => $product->img,
                    ];
                })->values(),
            'tax' => $cart->taxTotal->decimal(),
            'subTotal' => $cart->subTotal->decimal(),
            'total' => $cart->total->decimal(),
            'discountTotal' => $cart->discountTotal->decimal(),
            'coupon_code' => $cart->coupon_code,
            'shipping' => $cart->shippingSubTotal->decimal(),
            'postcode' => $cart->shippingAddress?->postcode,
            'isFullyDiscounted' => $cart->discountTotal->decimal() === $cart->subTotal->decimal() && $cart->total->decimal() == 0,
        ];
    }
}
