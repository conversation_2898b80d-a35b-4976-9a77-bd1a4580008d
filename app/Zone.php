<?php

namespace App;

use App\Http\Controllers\RouteForMeController;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use setasign\Fpdi\Fpdi;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Route4Me\Route4Me;
use Route4Me\OptimizationProblem;
use Route4Me\OptimizationProblemParams;
use Route4Me\RouteParameters;
use Route4Me\Address;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class Zone extends Model implements HasMedia
{
    use InteractsWithMedia, HasFactory;

    protected $guarded = [];

    protected $casts = [
        'zips' => 'array',
        'meta' => 'array',
        'zip_codes' => 'array',
        'original_set' => 'array',
        'free_shipping' => 'boolean'
    ];


    public function hasFreeShippingHandler($amount = 0): bool
    {
        return ($this->free_shipping && $this->free_shipping_price < $amount);
    }

    public function subscriptionRoutes()
    {
        return $this->hasMany(SubscriptionRoute::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function previousDates()
    {
        return $this->dates(true);
    }

    public function namedPublications()
    {
        return $this->hasMany(NamedPublication::class);
    }

    public function unShippedShipments()
    {
        return $this->hasMany(Shipment::class)
            ->where('shipped_at', null);
    }

    public function dates($previous = false)
    {
        return collect(range(0, 17))->mapWithKeys(function ($index) use ($previous) {
            if (!$previous) {
                $amount = $index + ($this->afterCutoff() ? 1 : 0);
                $startOfWeek = today()
                    ->addWeek($amount)
                    ->startOfWeek()
                    ->toDateString();
            } else {
                $amount = $index + ($this->afterCutoff() ? 0 : 1);
                $startOfWeek = today()
                    ->subWeek($amount)
                    ->startOfWeek()
                    ->toDateString();
            }

            return [
                $startOfWeek => \App\HebrewDate::getParshaString($startOfWeek) . " - $startOfWeek"
            ];
        })->filter(function ($date, $index) {
            return !\App\AbsentWeek::firstWhere(['week' => $index]);
        });
    }

    public function upcomingStartDate()
    {
        return today()->parse(array_key_first($this->dates()->all()));
    }

    public function afterCutoff()
    {
        if (!$this->cutoff_time) {
            return false;
        }

        return now()->isAfter($this->carbonCutoff());
    }

    public function carbonCutoff()
    {
        [$hour, $minute] = explode(':', $this->cutoff_time);

        return now()
            ->startOfWeek()
            ->addDay(($this->cutoff_day + 6) % 7)
            ->addHour($hour)
            ->addMinute($minute);
    }

    public function currentSubscriptions()
    {
        return Subscription::current()
            ->where(['zone_id' => $this->id])
            ->get();
    }


    public function location($zip)
    {
        $array = array_filter(
            explode(',', $this->zip_codes[$zip]),
            'trim'
        );

        return [
            'city' => data_get($array, 0),
            'state' => data_get($array, 1),
        ];
    }

    public static function getZip($zip)
    {
        return self::whereJsonContains('zips', (string)$zip)
            ->orwhereJsonContains('zips', substr($zip, 0, 3))
            ->first();
    }

    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    public function cycles()
    {
        return $this->belongsToMany(Cycle::class)
            ->where('active', true)
            ->where('price', '>', 0)
            ->as('price')
            ->withPivot('price', 'percent');
    }

    public function setPercentByCycle($cycle)
    {
        $retail = $cycle->publications * $this->publication_price;
        $subscripton = $this->priceByCycle($cycle);

        return round(100 * ($retail - $subscripton) / $retail);
    }

    public function percentByCycle($cycle)
    {
        return data_get(
            $this->belongsToMany(Cycle::class)
                ->as('price')
                ->withPivot('price', 'percent')
                ->find($cycle),
            'price.percent'
        );
    }

    public function priceByCycle($cycle, $promo = null)
    {
        $price = data_get(
            $this->belongsToMany(Cycle::class)
                ->as('price')
                ->withPivot('price', 'percent')
                ->find($cycle),
            'price.price'
        );

        return $promo ? $promo->getPrice($price) : $price;
    }

    public function getPercentAttribute()
    {
        return Cycle::where('active', true)->get()->mapWithKeys(function ($cycle) {
            return [
                $cycle->name => $this->percentByCycle($cycle),
            ];
        })->all();
    }

    public function getLegacyPercentAttribute()
    {
        return Cycle::where('active', false)->get()->mapWithKeys(function ($cycle) {
            return [
                $cycle->name => $this->percentByCycle($cycle),
            ];
        })->all();
    }

    public function getPricingAttribute()
    {
        return Cycle::where('active', true)->get()->mapWithKeys(function ($cycle) {
            return [
                $cycle->name => $this->priceByCycle($cycle),
            ];
        })->all();
    }

    public function getLegacyPricingAttribute()
    {
        return Cycle::where('active', false)->get()->mapWithKeys(function ($cycle) {
            return [
                $cycle->name => $this->priceByCycle($cycle),
            ];
        })->all();
    }

    public function setPercentAttribute()
    {
    }

    public function setLegacyPercentAttribute()
    {
    }

    public function setPricingAttribute()
    {
    }

    public function setLegacyPricingAttribute()
    {
    }

    public static function boot()
    {
        parent::boot();

        static::saved(function ($zone) {
            $data = collect(json_decode(request()->pricing))
                ->merge(collect(json_decode(request()->legacy_pricing)))
                ->mapWithKeys(function ($price, $cycle_text) use ($zone) {
                    $cycle = Cycle::firstOrCreate(['name' => $cycle_text]);

                    $percent = data_get(
                        json_decode($cycle->active ? request()->percent : request()->legacy_percent),
                        $cycle_text
                    );
                    return [$cycle->id => [
                        'price' => (float)$price,
                        'percent' => is_numeric($percent) ? (float)$percent : null,
                    ]];
                });
            $zone->cycles()->sync($data);

            Subscription::whereIn('postal_code', $zone->zips)->where('zone_id', '!=', $zone->id)->get()->each->touch();
        });
        self::saving(function ($zone) {
            $zone->meta = array_merge($zone->meta ?? [], [
                'end_address' => RouteForMeController::geocodeAddress($zone->end_address),
                'start_address' => RouteForMeController::geocodeAddress($zone->start_address),
            ]);
            $zone->zips = collect($zone->zip_codes)->keys()->map(function ($key) {
                return (string)$key;
            })->values();
        });
    }

    public function setZipCodesAttribute($value)
    {
        $this->attributes['zip_codes'] = json_encode($value);
    }

    public function exports()
    {
        return $this->hasMany(Export::class);
    }

    public function createRoute4Me($shipped = false, $variant = false)
    {
       Route4Me::setApiKey('********************************');

        $addresses = [];

        if ($this->start_address) {
            $addresses[] = Address::fromArray(collect(data_get($this->meta, 'start_address'))->only('lat', 'lng', 'address')->toArray() + ['alias' => 'Start Address']);
        }

        $route = \App\SubscriptionRoute::create(['zone_id' => $this->id]);

        // $this->namedPublications()
        //     ->where('name', 'ניסן תשפ"ד')
        //     ->get()

        $shipmentQuery = $this->unShippedShipments()
            ->with('subscription', 'order.customer')
            ->where(function ($query) use ($variant) {
                if($variant) {
                    $query->where('purchasable_id', $variant);
                }
            });

        $shipmentEntries = $shipmentQuery->get();

        $customers = collect();

        $shipmentEntries
            ->sortBy(function ($shipment) {
                return data_get($shipment->getAddressArrayAttribute(), 'postcode') . data_get($shipment->getAddressArrayAttribute(), 'line_one');
            })
            ->each(function ($shipment) use (&$addresses, &$route, &$customers) {
                $subscription = $shipment->subscription;
                $address = $shipment->getVerifiedAddress() + [
                        'alias' => $shipment->getShippingNameAttribute(),
                        // 'order_no' => $shipment->id,
                        'custom_fields' => [
                            // 'Gift Note' => now()->parse($subscription->created_at)->isAfter(today()->subweeks(2)) ? $subscription->note : '',
                            // 'Gift From Name' => now()->parse($subscription->created_at)->isAfter(today()->subweeks(2)) ? optional($subscription->customer)->name : '',
                        'Address Line 2' => data_get($shipment->getAddressArrayAttribute(), 'line_two'),
                        'ShipmentId' => $shipment->id,
                    ]
                ];

                $customers->push($shipment->order?->customer);
                // $route->subscriptions()->attach([$subscription->id => collect($address)->only('lat', 'lng', 'address')->toArray()]);
                $addresses[] = Address::fromArray($address);
            });


        if ($this->end_address) {
            $addresses[] = Address::fromArray(collect(data_get($this->meta, 'end_address'))->only('lat', 'lng', 'address')->toArray() + ['alias' => 'End Address']);
        }


        $parameters = RouteParameters::fromArray(array(
            'route_name' => $name = now() . ' - VK - subscriptions - ' . $this->name,
            'optimization_engine' => 1,
            'optimization_quality' => 3,
            'optimize' => 'Time',
            'disable_optimization' => false,
            // 'algorithm_type' => \Route4Me\Enum\AlgorithmType::ADVANCED_CVRP_TW,
            'algorithm_type' => 1,
        ));

        $optimizationParams = new OptimizationProblemParams;
        $optimizationParams->setParameters($parameters);
        $optimizationParams->setAddresses($addresses);

        // try {
        $problem = OptimizationProblem::optimize($optimizationParams);
        $route_id = data_get(collect($problem), 'routes.0.route_id');

        $addresses = data_get(collect($problem), 'addresses');

        $shipmentIds = collect($addresses)->filter()->pluck('custom_fields_str_json')
            ->map(function ($custom_fields) {
                return data_get(json_decode($custom_fields), 'ShipmentId');
            })
            ->filter()
            ->values();

        $subscriptionSubscriptionRoutes = $route->subscriptionSubscriptionRoutes;
        collect($addresses)->each(function ($address) use (&$subscriptionSubscriptionRoutes) {
            optional($subscriptionSubscriptionRoutes->firstWhere('subscription_id', data_get($address, 'order_no')))->update(['route_destination_id' => data_get($address, 'route_destination_id')]);
        });

        $route->update([
            'route_id' => $route_id,
            'name' => $name,
            'meta->sort' => $shipmentIds,
        ]);
        // } catch (\Exception $ex) {
        //     $route->update(['route_id' => 'Failed', 'name' => $name]);
        //     Log::channel('slack')->critical('The route for ' . $this->name . ' was not created.');
        // }

        if ($shipped) {
            $this->unShippedShipments->each->update([
                'shipped_at' => now()
            ]);
        }


        foreach($customers->unique('id') as $customer){
            app()->make(NotificationService::class)->trackingInformation($customer);
        }
    }

    public function changesToMonthlyExport($current)
    {
        $current = collect($current);

        $original = collect($this->original_set);

        $changes = $current->map(function ($item) use ($original) {

            $subscription_id = data_get($item, 'id');

            $original = $original->where('id', $subscription_id)->first();

            if (!$original) {
                return array_merge($item, ['status' => 'ADDED']);
            }

            if (!!collect($item)->diffAssoc(collect($original))->all() || !!collect($item)->diffKeys(collect($original))->all()) {
                return array_merge($item, ['status' => 'MODIFIED']);
            }
        })
            ->filter()
            ->values();


        $changes = $changes->merge(

            $original->map(
                function ($item) use ($current) {

                    $subscription_id = data_get($item, 'id');

                    $currentItem = $current->where('id', $subscription_id)->first();

                    if (!$currentItem) {
                        return array_merge($item, ['status' => 'REMOVED']);
                    }
                }
            )
                ->filter()
                ->values()
        );

        return $changes;
    }

    public function createLocalLabels($sent)
    {

        $pdf = new Fpdi;
        $folder_name = time();

        \File::makeDirectory(storage_path() . "/" . $folder_name);

        $this->unShippedShipments()
            ->with('subscription.zone')

            //
            ->orwhere('name', 'אייר תשפ"ד')
            ->where('zone_id', 2)
            ->get()
            ->sortBy(function ($shipment) {
                return $shipment->subscription->postal_code . data_get(explode(' ', $shipment->subscription->address_line_1), '1');
            })->each(function ($shipment) use ($folder_name, $pdf) {
                $path = storage_path() . "/{$folder_name}/shipment-{$shipment->id}.pdf";
                $data = [
                    'shipment' => $shipment,
                    'subscription' => $shipment->subscription,
                    'address' => $shipment->subscription->getVerifiedAddress(),
                    'zone' => $shipment->subscription->zone->name,

                    // 
                    // 'sort' => time(),
                    // 'gift' => \DB::table('named_publications')->where([
                    //     'subscription_id' => $shipment->subscription->id,
                    //     'subscription_month_id' => 10,
                    // ])->doesntExist()

                ];

                \PDF::loadView('label', $data)
                    ->setPaper('a6')
                    ->save($path);

                $pdf->AddPage('P', [101.6, 152.4]);
                $pdf->setSourceFile($path);
                $tplId = $pdf->importPage(1);
                $pdf->useTemplate($tplId, 0, 0, 101.6, 152.4);
            });

        \File::deleteDirectory(storage_path() . "/" . $folder_name);

        $pdfFilePath = storage_path("labels.pdf");
        $pdf->Output('F', $pdfFilePath);

        if ($sent) {
            $this
                ->unShippedShipments
                ->each
                ->update([
                    'shipped_at' => now()
                ]);
        }
        return $pdfFilePath;
    }
}
