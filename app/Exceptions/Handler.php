<?php

namespace App\Exceptions;

use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;


class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * @param Throwable $e
     * @return void
     * @throws Throwable
     */
    public function report(Throwable $e)
    {
        parent::report($e);
    }

    /**
     * @param $request
     * @param Throwable $exception
     * @return JsonResponse|Response
     * @throws Throwable
     */
    public function render($request, Throwable $exception)
    {
        // Handling "Insufficient funds" error
        if (str_contains($exception->getMessage(), 'Insufficient funds')) {
            return response()->json(['error' => 'Insufficient funds'], 402);
        }

        // Handling PromoException
        if ($exception instanceof PromoException) {
            return response()->json(['message' => $exception->getMessage()], 400);
        }

        return parent::render($request, $exception);
    }

}
