<?php

namespace App;

use App\Traits\NotesTrait;
use Illuminate\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User;

class Customer extends User
{
    use NotesTrait;
    use HasFactory;
    use Authenticatable;

    public $guarded = [];

    protected $hidden = ['api_token', 'stripe_token'];

    public function getFrontEndAttribute()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'subscribe' => $this->isSubscribed(),
            'preferred_contact_method' => $this->preferred_contact_method
        ];
    }

    public function isSubscribed()
    {
        return optional(\App\Subscriber::firstWhere('email', $this->email))->state == 'Active';
    }

    public static function getByEmail($email, $data = null)
    {
        $data = collect($data)->only('name', 'email', 'phone')->toArray();
        return static::firstOrCreate(['email' => $email], $data);
    }

    public static function getByPhone($phone, $data = null)
    {
        $data = collect($data)->only('name', 'email', 'phone')->toArray();
        return static::firstOrCreate(['phone' => parseCell($phone)], $data);
    }

    public static function syncByData($data)
    {
        $data = collect($data)->only('name', 'email', 'phone')->toArray();

        if(!$data['email'] && !$data['phone']) {
            abort(422, "Email or phone number is required");
        }

        if($data['email']) {
            return self::getByEmail($data['email'], $data);
        }

        return self::getByPhone($data['phone'], $data);
    }

    public function getDontHaveEmailAttribute()
    {
        return $this->email ? false : true;
    }

    public function signUp($active)
    {
        if ($active) {
            Subscriber::firstOrCreate(
                ['email' => $this->email],
                [
                    'name' => $this->name,
                    'state' => 'Active',
                ]
            );
        }
    }

    public function orders()
    {
        return $this->hasMany(\App\Lunar\Order::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function cards()
    {
        return $this->hasMany(CreditCard::class);
    }

    public static function boot()
    {
        parent::boot();

        self::saving(function ($customer) {
            $customer->phone = parseCell($customer->phone);
        });
    }
}
