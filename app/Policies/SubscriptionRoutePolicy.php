<?php

namespace App\Policies;

use App\Subscription;
use App\SubscriptionRoute;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubscriptionRoutePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return true;
    }

    public function view(User $user, SubscriptionRoute $subscriptionRoute)
    {
        return true;
    }

    public function create(User $user)
    {
        return false;
    }

    public function update(User $user, SubscriptionRoute $subscriptionRoute)
    {
        return false;
    }

    public function delete(User $user, SubscriptionRoute $subscriptionRoute)
    {
        return false;
    }

    public function restore(User $user, SubscriptionRoute $subscriptionRoute)
    {
        return false;
    }

    public function forceDelete(User $user, SubscriptionRoute $subscriptionRoute)
    {
        return false;
    }

    public function attachAnySubscription(User $user, SubscriptionRoute $subscriptionRoute)
    {
        return false;
    }

    public function detachSubscription(User $user, SubscriptionRoute $subscriptionRoute, Subscription $subscription)
    {
        return false;
    }
}
