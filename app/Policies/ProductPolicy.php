<?php

namespace App\Policies;

use Illuminate\Http\Request;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Nova;

class ProductPolicy
{
    /**
     * Create a new policy instance.
     */
    public function __construct()
    {
        return true;
    }

    function create()
    {
        return Nova::whenServing(function (NovaRequest $request) {
            if (str_contains($request->path(), 'issues')) {
                return false;
            }
            return true;
        });
    }

    function view()
    {
        return true;
    }

    function update()
    {
        return true;
    }

    function delete()
    {
        return $this->create();
    }
}
