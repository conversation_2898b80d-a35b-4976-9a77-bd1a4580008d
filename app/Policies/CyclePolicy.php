<?php

namespace App\Policies;

use App\Cycle;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CyclePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return true;
    }

    public function view(User $user, Cycle $cycle)
    {
        return true;
    }

    public function create(User $user)
    {
        return true;
    }

    public function update(User $user, Cycle $cycle)
    {
        return true;
    }

    public function delete(User $user, Cycle $cycle)
    {
        return !$cycle->subscriptions->count() > 0;
    }

    public function restore(User $user, Cycle $cycle)
    {
        return true;
    }

    public function forceDelete(User $user, Cycle $cycle)
    {
        return true;
    }
}
