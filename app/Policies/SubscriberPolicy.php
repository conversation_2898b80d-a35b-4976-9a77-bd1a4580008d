<?php

namespace App\Policies;

use App\User;
use App\Subscriber;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubscriberPolicy
{
    use HandlesAuthorization;

    private function auth($user)
    {
        return true;
//        return $user->is_admin;
    }

    public function viewAny(User $user)
    {
        return $this->auth($user);
    }

    public function view(User $user, Subscriber $subscriber)
    {
        return $this->auth($user);
    }

    public function create(User $user)
    {
        return $this->auth($user);
    }

    public function update(User $user, Subscriber $subscriber)
    {
        return $this->auth($user);
    }

    public function delete(User $user, Subscriber $subscriber)
    {
        return false;
//        return $this->auth($user);
    }

    public function restore(User $user, Subscriber $subscriber)
    {
        return $this->auth($user);
    }

    public function forceDelete(User $user, Subscriber $subscriber)
    {
        return false;
//        return $this->auth($user);
    }
}
