<?php

namespace App\Policies;

use App\User;
use App\Campaign;
use Illuminate\Auth\Access\HandlesAuthorization;

class CampaignPolicy
{
    use HandlesAuthorization;

    private function canEdit($campaign)
    {
        return !$campaign->sent || $campaign->schedule > now() ? true : false;
    }

    public function viewAny(User $user)
    {
        return true;
    }


    public function view(User $user, Campaign $campaign)
    {
        return true;
    }

    public function create(User $user)
    {
        return true;
    }

    public function update(User $user, Campaign $campaign)
    {
        return $this->canEdit($campaign);
    }

    public function delete(User $user, Campaign $campaign)
    {
        return $this->canEdit($campaign);
    }

    public function restore(User $user, Campaign $campaign)
    {
        return $this->canEdit($campaign);
    }

    public function forceDelete(User $user, Campaign $campaign)
    {
        return $this->canEdit($campaign);
    }
}
