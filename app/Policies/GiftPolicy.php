<?php

namespace App\Policies;

use App\Gift;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class GiftPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return true;
    }

    public function view(User $user, Gift $gift)
    {
        return true;
    }

    public function create(User $user)
    {
        return true;
    }

    public function update(User $user, Gift $gift)
    {
        return true;
    }

    public function delete(User $user, Gift $gift)
    {
        return !$gift->subscriptions->count() > 0;
    }

    public function restore(User $user, Gift $gift)
    {
        return true;
    }

    public function forceDelete(User $user, Gift $gift)
    {
        return true;
    }
}
