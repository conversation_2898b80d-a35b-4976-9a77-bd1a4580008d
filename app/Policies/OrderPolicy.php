<?php

namespace App\Policies;

use App\Lunar\Order;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class OrderPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return true;
    }

    public function view(User $user, $order)
    {
        return true;
    }

    public function create(User $user)
    {
        return true;
        return false;
    }

    public function update(User $user, $order)
    {
        return true;
        return false;
    }

    public function delete(User $user, $order)
    {
        return false;
    }

    public function restore(User $user, $order)
    {
        return true;
    }

    public function forceDelete(User $user, $order)
    {
        return true;
    }

    public function addPayment()
    {
        return false;
    }
}
