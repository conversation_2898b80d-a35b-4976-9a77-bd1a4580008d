<?php

namespace App;

use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Lunar\Base\Traits\LunarUser;

class User extends Authenticatable
{
    use Notifiable, HasFactory, LunarUser;

    protected $guarded = [];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
    ];


    public function articles()
    {
        return $this->hasMany(Article::class);
    }

    public function getFrontEndAttribute()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'path' => $this->path,
        ];
    }

    public function getPathAttribute()
    {
        return "/authors/" . slug($this->name) . "/" . $this->id;
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function sendPasswordResetNotification($token): void
    {
        app()->make(NotificationService::class)->resetPassword($this, $token);
    }
}
