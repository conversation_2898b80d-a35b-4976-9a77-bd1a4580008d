<?php

namespace App\Exports;

use App\Shipment;
use App\Subscription;
use App\Zone;
use App\NamedPublication;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class MonthlyExport implements FromCollection, WithHeadings
{
    protected $subscription_month_id;
    protected $subscriptions;

    public function __construct($subscription_month_id, $zone_id)
    {
        $this->subscription_month_id = $subscription_month_id;
        $this->zone_id = $zone_id;
    }

    public function collection()
    {
        $named_publications = Shipment::query()
            ->where('subscription_month_id', $this->subscription_month_id)
            ->with('subscription.zone')
            ->get();

        Log::info('Subscription Month ID:', ['subscription_month_id' => $this->subscription_month_id]);
        Log::info('Zone ID:', ['zone_id' => $this->zone_id]);

        $sortedPublications = $named_publications
            ->where('subscription.zone_id', $this->zone_id)
            ->sortBy(function ($publication) {
                return data_get($publication, 'subscription.postal_code') . data_get(explode(' ', data_get($publication, 'subscription.address_line_1')), '1');
            })
            ->map(function ($model) {
                $allowedFields = ["id", "name", "address_line_1", "address_line_2", "city", "state", "postal_code", "note"];
                $data = Arr::only($model->toArray(), $allowedFields);
                $subscriptionData = Arr::only(optional($model->subscription)->toArray(), $allowedFields);
                return array_merge($data, $subscriptionData);
            });

        $zone = Zone::query()->find($this->zone_id);
        $provider = optional($zone)->provider;

        if ($provider && $provider->only_changes) {
            $changes = $zone->changesToMonthlyExport($sortedPublications);
            $zone->original_set = $sortedPublications;
            $zone->saveQuietly();

            Log::info('Changes to Monthly Export:', ['changes' => $changes->toArray()]);

            return $changes;
        }

        return $sortedPublications;
    }


    public function headings(): array
    {
        $columns = [];
        $zone = Zone::query()->find($this->zone_id);

        if ($zone && $zone->provider) {
            switch ($zone->provider->combine) {
                case 'city_state_zip':
                    $columns = Subscription::$combine_fields;
                    break;
                case 'all':
                    $columns = ['id', 'name', 'address', 'note'];
                    break;
                default:
                    $columns = Subscription::$fields;
                    break;
            }

            if (!!$zone->provider->only_changes) {
                $columns = array_merge($columns, ['status']);
            }
        } else {
            // handle the case where zone or provider is null
            Log::error("Zone not found for ID: {$this->zone_id}");
            $columns = Subscription::$fields;  // default columns
        }

        return $columns;
    }
}
