<?php

namespace App\Exports;

use App\Subscription;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExpiredSubscriptionExport implements FromCollection, WithHeadings
{
    public $from;
    public $to;

    function __construct($from = null, $to = null)
    {
        $this->from = $from;
        $this->to = $to;
    }

    public function collection()
    {
        return Subscription::whereBetween('finish', [$this->from, $this->to])
            ->where('renew', 0)
            ->where('issues', '<=', 1)
            ->get()
            ->map(function ($subscription) {
                return [
                    $subscription->id,
                    $subscription->name,
                    $subscription->address_line_1,
                    $subscription->address_line_2,
                    $subscription->city,
                    $subscription->state,
                    $subscription->postal_code,
                    $subscription->apt_number,
                    $subscription->building_door_code,
                    $subscription->start,
                    $subscription->issues,
                    // $subscription->renew,
                ];
            });
    }

    public function headings(): array
    {
        return [
            'ID',
            'NAME',
            'ADDRESS LINE 1',
            'ADDRESS LINE 2',
            'CITY',
            'STATE',
            'POSTAL CODE',
            'APT NUMBER',
            'Building door code',
            'START',
            'ISSUES',
            // 'AUTO RENEW',
        ];
    }
}
