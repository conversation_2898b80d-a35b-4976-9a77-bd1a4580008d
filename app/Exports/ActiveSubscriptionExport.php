<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use App\Subscription;

class ActiveSubscriptionExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    protected $subscriptionIds;

    public function __construct($subscriptionIds = [])
    {
        $this->subscriptionIds = $subscriptionIds;
    }

    public function collection()
    {
        $query = Subscription::with(['customer'])->query();

        if (!empty($this->subscriptionIds)) {
            $query->whereIn('id', $this->subscriptionIds);
        }

        return $query->get()->map(function ($subscription) {
            return [
                $subscription->id,
                $subscription->name,
                $subscription->customer->phone,
                $subscription->customer->email,
                $subscription->address_line_1,
                $subscription->address_line_2,
                $subscription->city,
                $subscription->state,
                $subscription->postal_code,
                $subscription->apt_number,
                $subscription->building_door_code,
                $subscription->publications_sent
            ];
        });
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Phone',
            'Email',
            'Address Line 1',
            'Address Line 2',
            'City',
            'State',
            'ZIP',
            'Apt Number',
            'Building door code',
            'publications_sent'
        ];
    }
}
