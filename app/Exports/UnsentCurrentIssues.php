<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use App\Subscription;
use Illuminate\Support\Facades\Log;

class UnsentCurrentIssues implements FromCollection, WithHeadings, ShouldAutoSize
{
    protected $updatedSubscriptionIds;

    public function __construct(array $updatedSubscriptionIds = [])
    {
        $this->updatedSubscriptionIds = $updatedSubscriptionIds;
    }

    public function collection()
    {
        if (empty($this->updatedSubscriptionIds)) {
            Log::warning('No updated Subscription IDs provided');
            return collect([]);
        }

        $subscriptions = Subscription::query()
            ->whereIn('id', $this->updatedSubscriptionIds)
            ->get();

        return $subscriptions->map(function ($subscription) {
            return [
                $subscription->name,
                $subscription->address_line_1,
                $subscription->address_line_2,
                $subscription->city,
                $subscription->state,
                $subscription->postal_code,
                $subscription->apt_number,
                $subscription->building_door_code,
                $subscription->issues,
                $subscription->publications_sent,
                $subscription->id,
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Name',
            'Address Line 1',
            'Address Line 2',
            'City',
            'State',
            'ZIP',
            'Apt Number',
            'Building door code',
            'issues Remaining',
            'publication sent'
        ];
    }
}
