<?php

namespace App\Exports;

use App\Note;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class SubscriptionNotesExport implements FromCollection, WithHeadings
{
    public function __construct($from, $to)
    {
        $this->from = $from;
        $this->to = $to;
    }

    public function collection()
    {
        return Note::query()
            ->whereBetween('created_at', [$this->from, $this->to])
            ->get()
            ->map(function ($note) {
                $customer = $note->model;
                return [
                    $note->content,
                    $customer->name,
                    $customer->phone,
                    $customer->email,
                ];
            });
    }

    public function headings(): array
    {
        return [
            'CONTENT',
            'NAME',
            'PHONE',
            'EMAIL',
        ];
    }
}
