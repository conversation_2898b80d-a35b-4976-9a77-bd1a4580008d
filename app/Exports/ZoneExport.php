<?php

namespace App\Exports;

use App\Subscription;
use App\Zone;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ZoneExport implements FromCollection, WithHeadings
{
    public function __construct($id)
    {
        $this->id = $id;
    }

    public function collection()
    {
        return Zone::query()
            ->find($this->id)
            ->currentSubscriptions()
            ->map
            ->toExcel();
    }

    public function headings(): array
    {
        if (!$provider = Zone::query()->find($this->id)->provider) {
            return Subscription::$fields;
        }

        switch ($provider->combine) {
            case 'city_state_zip':
                $fields = Subscription::$combine_fields;
                break;
            case 'all':
                $fields = ['id', 'name', 'address'];
                break;
            default:
                $fields = Subscription::$fields;
                break;
        }

        if ($provider->include_phone) {
            $fields[] = 'phone';
        }

        return $fields;
    }
}
