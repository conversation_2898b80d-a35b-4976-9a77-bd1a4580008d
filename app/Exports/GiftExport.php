<?php

namespace App\Exports;

use App\Gift;
use App\Subscription;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class GiftExport implements FromCollection, WithHeadings
{
    public function __construct($zone_id, $gift_id, $delivered)
    {
        $this->zone_id = $zone_id;
        $this->gift_id = $gift_id;
        $this->delivered = $delivered;
    }

    public function collection()
    {
        $fields = Subscription::$fields;

        // $this->start = now()->parse($this->start);
        // $this->end = $this->end ? now()->parse($this->end) : now()->parse($this->start)->endOfWeek();

        $gift = Gift::find($this->gift_id);

        $subscriptions = Subscription::query()
            ->active()
            ->where('gift_id', $this->gift_id)
            ->whereNotIn('id', $gift->subscription_ids ?? [])
            ->where(function ($query) {
                return $this->zone_id ? $query->where('zone_id', $this->zone_id) : $query;
            })
            ->get();

        if ($this->delivered) {
            $gift->update([
                'subscription_ids' => $subscriptions
                    ->pluck('id')
                    ->merge($gift->subscription_ids)
                    ->unique()
            ]);
        }

        return $subscriptions
            ->map(function ($subscription) use ($fields, $gift) {
                return $subscription->only($fields) + [
                        'gift' => $gift->name
                    ] + $subscription->getExportFields()->toArray();
            });
    }

    public function headings(): array
    {
        return array_merge(
            Subscription::$fields,
            ['gift'],
            Gift::customFields()->toArray(),
        );
    }
}
