<?php

namespace App\Exports;

use App\Subscription;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class GiftNoteExport implements FromCollection, WithHeadings
{
    public function __construct($start, $end, $zone_id)
    {
        $this->start = $start;
        $this->end = $end;
        $this->zone_id = $zone_id;
    }

    public function collection()
    {
        $fields = Subscription::$fields;

        $this->start = now()->parse($this->start);
        $this->end = $this->end ? now()->parse($this->end) : now()->parse($this->start)->endOfMonth();

        return Subscription::query()
            ->active()
            ->with('customer')
            ->whereBetween('start', [$this->start, $this->end])
            ->whereNotNull('note')
            ->where(function ($query) {
                return $this->zone_id ? $query->where('zone_id', $this->zone_id) : $query;
            })
            ->get()
            ->map(function ($subscription) use ($fields) {
                return $subscription->only($fields) + [
                        'note' => $subscription->note,
                        'from' => $subscription->customer->name,
                    ];
            });
    }

    public function headings(): array
    {
        return array_merge(Subscription::$fields, ['note', 'from']);
    }
}
