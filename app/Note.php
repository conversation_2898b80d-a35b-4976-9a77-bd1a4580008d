<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Note extends Model
{
    protected $guarded = [];

    public function model()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function getFrontEndAttribute()
    {
        return [
            'id' => $this->id,
            'by' => $this->by,
            'reason' => $this->reason,
            'content' => $this->content,
            'can_modify' => $this->canModify(),
            'at' => $this->created_at->diffForHumans(),
        ];
    }

    public function getByAttribute()
    {
        return optional($this->user)->name ?? 'BOT';
    }

    public function canModify()
    {
        return $this->user_id == auth()->id();
    }
}
