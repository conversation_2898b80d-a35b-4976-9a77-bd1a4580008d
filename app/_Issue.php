<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class _Issue extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $guarded = [];

    public function articles()
    {
        return $this->hasMany(Article::class);
    }

    public function getFrontEndAttribute()
    {
        $images = $this->media()
            ->whereIn('collection_name', Section::pluck('index'))
            ->get();

        return [
            'id' => $this->id,
            'parsha' => $this->name,
            'sections' => Section::all()
                ->map(function ($section) use ($images) {
                    return [
                        ...$section->only('id', 'name', 'index'),
                        'image' => $images->firstWhere('collection_name', $section->index)->getUrl('components'),
                        'articles' => $this
                            ->articles
                            ->where('section_id', $section->id)
                            ->map(function ($article) {
                                return $article->frontEnd;
                            })
                            ->values(),
                    ];
                }),
        ];
    }

    public static function getCurrentFrontEnd()
    {
        $issue = self::with('articles.media')
            ->latest()
            ->where('active', true)
            ->where(function ($query) {
                return $query->where('publish_date', null)
                    ->orWhere('publish_date', '>=', now());
            })
            ->first();

        return $issue->frontEnd;
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        $this->addMediaConversion('components')
            ->width(600);
    }

    public function registerMediaCollections(): void
    {
        Section::all()
            ->each(function ($section) {
                $this->addMediaCollection($section->index)
                    ->acceptsFile(function () {
                        return true;
                    });
            });
    }
}
