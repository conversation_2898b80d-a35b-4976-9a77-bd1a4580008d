<?php

namespace App\Models\Collections;

use App\CreditCard;
use App\Lunar\OrderLine;
use App\Nova\Order as NovaOrder;
use App\Subscription;
use Laravel\Nova\Actions\ActionResponse;

class Order extends \App\Lunar\Order
{
    public function toSearchableArray(): array
    {
        return array_merge([
            'id' => (string) $this->id,
        ]);
    }
    public function getPaymentTypeAttribute()
    {
        return $this->meta->payment_type ?? 'credit';
    }

    public static function boot(): void
    {
        parent::boot();

        static::creating(function () {
            NovaOrder::creating();
        });
    }

    public function getShippingWithoutTaxAttribute()
    {
        return $this->shippingLines->sum(function ($line) {
                return $line->total->value;
            }) / 100;
    }

    public function productDigitalMany()
    {
        return $this->hasMany(OrderLine::class, 'order_id')
            ->where('type', 'digital');
    }

    public function productPhysicalMany()
    {
        return $this->hasMany(OrderLine::class, 'order_id')
            ->where('type', 'physical');
    }


    public function productVariants()
    {
        return $this
            ->morphedByMany(\Lunar\Models\ProductVariant::class,
                'purchasable',
                'lunar_order_lines',
            )
            ->withPivot(['type', 'description', 'quantity', 'identifier', 'tax_breakdown', 'tax_total', 'unit_price', 'unit_quantity', 'sub_total', 'discount_total', 'total']);
    }

    public function getShippingAddressLineOneAttribute()
    {
        return $this->shippingAddress?->line_one;
    }

    public function getShippingAddressLineTwoAttribute()
    {
        return $this->shippingAddress?->line_two;
    }

    public function getShippingAddressNameAttribute()
    {
        return $this->shippingAddress?->first_name;
    }

    public function getShippingAddressCityAttribute()
    {
        return $this->shippingAddress?->city;
    }

    public function getShippingAddressStateAttribute()
    {
        return $this->shippingAddress?->state;
    }

    public function getShippingAddressPostcodeAttribute()
    {
        return $this->shippingAddress?->postcode;
    }

    public function creditCard()
    {
        return $this->belongsTo(CreditCard::class, 'meta->creditCardData->creditCardId');
    }

    protected function cancelSubscriptions()
    {
        $this->subscriptions()
            ->where('canceled', false);
    }

}