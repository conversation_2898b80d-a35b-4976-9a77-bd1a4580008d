<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'options' => 'array'
    ];

    public static function getValue($key)
    {
        return optional(static::where('key', $key)->first())->value;
    }

    public function getId($key)
    {
        return optional($this->where('key', $key)->first())->id;
    }

    public function getObject($key)
    {
        return optional($this->where('key', $key)->first());
    }

    public function displayValueForSelect()
    {
        if (!$this->value) {
            return '';
        }
        return data_get($this->options, $this->value);
    }

    public static function getOptions($key)
    {
        $array = [];
        collect(explode(',', optional(self::firstWhere('key', $key))->value))->each(function ($l) use (&$array) {
            $array[$l] = $l;
        });
        return $array;
    }
}
