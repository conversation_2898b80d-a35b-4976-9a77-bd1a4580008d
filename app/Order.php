<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    public $guarded = [];

    public $casts = [
        'subscription_data' => 'array',
    ];

    public function customer(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }


    
    public function getFrontEndAttribute()
    {
        $subscription = $this->subscription;
        return [
            'id' => $subscription->id,
            'amount' => $this->amount,
            'created_at' => $this->created_at,
            'type' => data_get($this->subscription_data, 'payment.type'),
            'last_four' => data_get($this->subscription_data, 'payment.last_four'),
            'free_gift' => optional($subscription->gift)->picture,
            'gift_information' => $subscription->note ? [
                'note' => $subscription->note,
                'name' => optional($this->customer)->name,
            ] : null,
            'subscription' => [
                'id' => $subscription->id,
                'start' => $subscription->start,
                'payment_type' => $subscription->payment_type,
                'cycle' => $subscription->cycle->only(['name', 'percent']),
                'gift_fields' => $subscription->gift_fields,
                'renew' => $subscription->renew
                    ? $subscription->finish->addDay()
                    : null,
            ],
            'delivery' => $subscription->only(['name', 'address_line_1', 'address_line_2', 'city', 'state']) + ['zip' => $subscription->postal_code],
            'promo' => optional($this->promo)->only(['name', 'amount', 'type']),
        ];
    }

    public function promo()
    {
        return $this->belongsTo(Promo::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    public function getTotalPaidAttribute()
    {
        return $this->payments()->sum('amount');
    }

    public static function boot()
    {
        parent::boot();

        self::creating(function ($order) {
            $subscription = Subscription::find($order->subscription_id);
            $cycle = Cycle::find($subscription->cycle_id);
            // $payment = Payment::firstWhere(['order_id' => $order->id]);
            $gift = Gift::find($subscription->gift_id);
            $credit_card = CreditCard::find($subscription->credit_card_id);

            $payment = $credit_card ? $credit_card->only('id', 'last_four', 'type') : request()->payment_type;


            $order->subscription_data = [
                'date' => $subscription->created_at->toDateString(),
                'start' => $subscription->start,
                'finish' => $subscription->finish,
                'cycle' => $cycle->only('id', 'name', 'months', 'percent'),
                'gift' => optional($gift)->only('id', 'name'),
                'note' => $subscription->note,
                'customer' => $order->customer->only('name', 'phone', 'emial', 'email'),
                'payment' => $payment,
                'delivery' => [
                    'name' => $subscription->name,
                    'address_line_1' => $subscription->address_line_1,
                    'address_line_2' => $subscription->address_line_2,
                    'city' => $subscription->city,
                    'state' => $subscription->state,
                    'postal_code' => $subscription->postal_code,
                ],
            ];
        });
    }
}
