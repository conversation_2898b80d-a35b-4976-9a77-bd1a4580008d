<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Provider extends Model
{
    protected $guarded = [];

    protected $casts = [
        'additional_emails' => 'array',
    ];

    public static $combine_options = [
        'dont' => 'Dont Combine',
        'all' => 'All',
        'city_state_zip' => 'City State & Zip',
    ];

    public function zones()
    {
        return $this->hasMany(Zone::class);
    }
}
