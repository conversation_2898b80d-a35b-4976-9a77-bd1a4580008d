<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\CreditCardController;

class Payment extends Model
{
    protected $guarded = [];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function creditCardPayment()
    {
        return $this->belongsTo(CreditCardPayment::class);
    }

    public function creditCard()
    {
        return $this->belongsTo(CreditCard::class);
    }

    public function getRefundedAttribute()
    {
        return optional($this->creditCardPayment)->refund;
    }

    public function refund($amount)
    {
        if ($this->payment_type == 'Credit Card') {
            CreditCardController::refund($this, $amount);
        }
    }

    public function cancel()
    {
        if ($this->payment_type == 'Credit Card') {
            CreditCardController::cancel($this);
        }
    }


    public static function boot()
    {
        parent::boot();

        return;
        self::saved(function ($payment) {
            $request = request();
            $order = $payment->order;
            if ($request->payment_type == 'Credit Card') {
                $token = CreditCardController::getToken($request->merge([
                    'card' => str_replace([' ', '/'], '', $request->card),
                    'exp' => str_replace([' ', '/'], '', $request->expiration),
                    'order_id' => $order->id
                ]));

                $credit_card_payment = \App\CreditCardPayment::create([
                    'token' => $token,
                    'type' => '',
                    'security_code' => $request->security,
                    'last_four' => substr(str_replace([' ', '/'], '', $request->card), -4),
                    'payer_info' => $enrollment->donor,
                    'amount' => $request->amount ?? $enrollment->plan->amount,
                ]);

                $payment->withoutEvents(function () use ($payment, $credit_card_payment) {
                    $payment->update([
                        'credit_card_payment_id' => $credit_card_payment->id
                    ]);
                });
            }
        });
    }


    public function setCardAttribute($value)
    {
    }

    public function getCardAttribute()
    {
        return data_get($this->creditCardPayment, 'last_four');
    }

    public function setExpirationAttribute($value)
    {
    }

    public function getExpirationAttribute()
    {
        return data_get($this->creditCardPayment, 'payer_info.expires');
    }

    public function setSecurityAttribute($value)
    {
    }

    public function getSecurityAttribute()
    {
        return data_get($this->creditCardPayment, 'security_code');
    }
}
