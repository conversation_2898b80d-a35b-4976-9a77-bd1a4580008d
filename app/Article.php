<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Article extends Model implements HasMedia
{
    use HasFactory;

    use InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'publish_date' => 'date',
    ];

    public function getFrontEndAttribute()
    {
        return $this
                ->loadMissing([
                    'media', 'author', 'issue', 'section', 'categories'
                ])
                ->only([
                    'id', 'title', 'caption', 'content', 'publish_date', 'path'
                ]) +
            [
                'issue' => $this->issue->number,
                'parsha' => $this->issue->name,
                'column' => $this->column?->name,
                'image' => $this->media->first()?->getUrl('components'),
                'images' => $this->media->map->getUrl('components'),
                'section' => $this->section->name,
                'author' => $this->author->frontEnd,
                'categories' => $this->categories->map(function ($category) {
                    return [
                        'name' => $category->name,
                        'path' => $category->path,
                    ];
                }),
            ];
    }

    public function getPathAttribute()
    {
        return "/articles/" . slug($this->title) . "/" . $this->id;
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function column()
    {
        return $this->belongsTo(Column::class);
    }

    public function section()
    {
        return $this->belongsTo(Section::class);
    }

    public function issue()
    {
        return $this->belongsTo(Issue::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class);
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        $this->addMediaConversion('components')
            ->width(600)
            ->performOnCollections('images');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
            ->acceptsFile(function () {
                return true;
            });
    }
}
