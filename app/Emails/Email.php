<?php

namespace App\Emails;

use App\Customer;
use App\User;
use CS_REST_Transactional_SmartEmail;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

abstract class Email
{
    /**
     * @var array
     */
    protected array $data = [];

    /**
     * @var array
     */
    protected array $attachments = [];

    /**
     * @var string|null
     */
    protected ?string $template = null;

    protected Customer|User $user;

    protected array $senders = [];

    /**
     * @param $data
     * @return $this
     */
    public function withData($data): static
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @param $attachments
     * @return $this
     */
    public function withAttachments($attachments): static
    {
        $this->attachments = $attachments;
        return $this;
    }

    /**
     * @param $user
     * @return $this
     */
    public function withUser($user): static
    {
        $this->user = $user;
        $this->data = array_merge(compact('user'), $this->data);
        return $this;
    }

    /**
     * @param $senders
     * @return $this
     */
    public function setSenders($senders): static
    {
        if(is_array($senders)){
            foreach($senders as $sender){
                $this->senders[] = $sender;
            }
        }elseif(is_string($senders)){
            $this->senders[] = $senders;
        }

        return $this;
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function sendTo(): JsonResponse
    {
        Log::channel('email')->info('Email run class: ' . get_called_class());
        if(!count($this->senders)) {
            Log::channel('email')->error('Email is not set');
            return response()->json(['message' => 'Email is not set'], 400);
        }

        if (app()->environment(['dev', 'local', 'development', 'localhost'])) {
            if (empty($this->template)) {
                Log::channel('email')->error('Email local template is not set');
                return response()->json(['message' => 'Email local template is not set'], 400);
            }

            $render = view($this->template, $this->data)->render();

            try {
                Mail::html($render, function ($message) {
                    foreach ($this->senders as $sender) {
                        $message->to($sender);
                    }

                    foreach ($this->attachments as $attachment) {
                        $message->attachData($attachment['Content'], $attachment['Name'], [
                            'mime' => $attachment['Type'],
                        ]);
                    }
                });

                Log::channel('email')->info('Email sent local successfully.');
                return response()->json(['message' => 'Email sent local successfully']);
            }catch (Exception $e) {
                Log::channel('email')->error('Email sending local failed: ' . $e->getMessage());
                return response()->json(['message' => 'Email sending local failed'], 400);
            }
        }else{
            $mailer = $this->newTransaction();

            foreach($this->senders as $sender){
                $payload = [
                    'To' => $sender,
                    'Data' => (object)$this->data,
                ];

                if (!empty($this->attachments)) {
                    $payload['Attachments'] = $this->attachments;
                }

                $response = $mailer->send($payload, 'Yes');

                if(data_get($response, 'http_status_code') >= 300){
                    Log::channel('email')->warning('Email sending failed: ' . json_encode($response));
                    slack(json_encode($response));
                    return response()->json(['message' => 'Email sending failed'], 400);
                }
            }

            Log::channel('email')->info('Email sent successfully.');
            return response()->json(['message' => 'Email sent successfully']);
        }
    }

    /**
     * @return CS_REST_Transactional_SmartEmail
     * @throws Exception
     */
    public function newTransaction(): CS_REST_Transactional_SmartEmail
    {
        try {
            Log::channel('email')->info('Starting a new transaction.');

            $emailId = $this->getEmailId();
            $apiKey = config('campaignmonitor.api_key');

            if (!$apiKey) {
                Log::channel('email')->warning('CAMPAIGNMONITOR_API_KEY is not set.');
            }

            return new CS_REST_Transactional_SmartEmail(
                $emailId,
                ['api_key' => $apiKey]
            );
        } catch (Exception $e) {
            Log::channel('email')->error('An error occurred while creating a new transaction: ' . $e->getMessage());
            throw $e;
        }
    }

    abstract protected function getEmailId();
}
