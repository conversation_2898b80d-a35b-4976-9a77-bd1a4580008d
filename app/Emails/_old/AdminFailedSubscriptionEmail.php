<?php

namespace App\Emails\_old;

use App\Emails\Email;

class AdminFailedSubscriptionEmail extends Email
{
    // completely used
    public function getEmailId()
    {
        return 'a221829c-327e-4913-bc5b-3b9a560bdc41';
    }

    public function variables()
    {
//        [
//            'name',
//            'price',
//            'cycle' => [
//                'name',
//                'publications'
//            ],
//            'credit_card' =>[
//                'type',
//                'last_four',
//            ],
//            'delivery' => [
//                'name',
//                'address_line_1',
//                'city',
//                'state',
//                'postal_code',
//            ],
//            'subscription' => [
//                'id'
//            ],
//        ];
//        return;
    }
}
