<?php

namespace App\Emails;

class GenerateRouteEmail extends Email
{
    //actual
    public ?string $template = 'emails.templates.generate-route';

    public function getEmailId()
    {
        return 'd66b9313-7901-47dc-945a-9e2c95183079'; // set id email
    }

    public function variables(): array
    {
        return [
            'customer' => ['name' => 'NAME'],
            'subscriptionMonth' => [
                'name'
            ]
        ];
    }
}