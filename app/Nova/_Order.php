<?php

namespace App\Nova;

use App\Customer;
use App\Nova\Repeater\SubscribeCreatingItem;
use App\Services\Checkout\CheckoutBackend;
use App\Zone;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Laravel\Nova\Fields\Badge;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Fields\MorphedByMany;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Stack;
use Laravel\Nova\Http\Requests\NovaRequest;
use App\Rules\HasZipRule;
use App\Nova\Repeater\ProductItem;
use App\Nova\Repeater\SubscribeItem;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Repeater;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Panel;
use Laravel\Nova\URL;
use Lunar\Base\ValueObjects\Cart\TaxBreakdown;

class _Order extends Resource
{
    public static $model = \App\Models\Collections\Order::class;
    public static $title = 'id';
    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Panel::make('Customer Information', $this->customerFields()),
            Panel::make('Payment Information', $this->paymentFields()),

            ...$this->showInIndex(),

            Panel::make('Items Information', [

                Repeater::make('Products', 'productPhysicalMany')
                    ->asHasMany()
                    ->repeatables([
                        ProductItem::make()
                    ])
                    ->stacked()
                    ->fullWidth()
                    ->fillUsing(function ($request, $model) {
                        if ($request->editMode == 'create') {
                            return;
                        }
                        $this->fillRepeaterProduct($request, $model);
                    }),
                Repeater::make('Subscriptions', 'subscriptions')
                    ->asHasMany()
                    ->repeatables([
                        SubscribeItem::make()
                    ])
                    ->stacked()
                    ->fullWidth()
                    ->fillUsing(function ($request, $model) {
                        $this->fillRepeaterSubscribes($request, $model);
                    })
                    ->hideWhenCreating(),
                Repeater::make('Subscriptions', 'subscriptions')
                    ->asHasMany()
                    ->repeatables([
                        SubscribeCreatingItem::make()
                    ])
                    ->stacked()
                    ->fullWidth()
                    ->hideWhenUpdating(),

            ]),
            Panel::make('Delivery Information', $this->deliveryFields()),


            HasMany::make('Payments')->showOnDetail(),
            HasMany::make('Shipments')->showOnDetail(),


            $this->getProductsField(),
            HasMany::make('Subscriptions', 'subscriptions')->showOnDetail(),
        ];
    }

    protected function showInIndex(): array
    {
        return [
            Text::make('Name', 'shippingAddress.first_name')
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Badge::make('Status')
                ->map([
                    'payment-declined' => 'danger',
                    'payment-received' => 'success',
                    'awaiting-payment' => 'warning',
                    'canceled' => 'danger',
                ])
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Currency::make('Sub Total')
                ->resolveUsing(function ($value) {
                    return $value?->value / 100;
                })
                ->displayUsing(function ($value) {
                    return $value?->formatted();
                })
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Currency::make('Tax Total')
                ->resolveUsing(function ($value) {
                    return $value?->value / 100;
                })
                ->displayUsing(function ($value) {
                    return $value?->formatted();
                })
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Currency::make('Total')
                ->resolveUsing(function ($value) {
                    return $value?->value / 100;
                })
                ->displayUsing(function ($value) {
                    return $value?->formatted();
                })
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Text::make('Address')
                ->resolveUsing(function () {
                    return $this->shippingAddress?->address;
                })
                ->onlyOnForms()
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Stack::make('Address', [
                Text::make('Address', 'shippingAddress.first_name'),
                Text::make('Address', 'address_string'),
            ])->hideWhenUpdating()->hideWhenCreating(),
            Text::make('Subscription Type', 'id')
                ->displayUsing(function () {
                    return $this->subscriptions()->pluck('subscription_type')->join('<br/>');
                })
                ->asHtml()
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            DateTime::make('Created At')
                ->sortable()
                ->hideWhenUpdating()
                ->hideWhenCreating(),
        ];
    }

    protected function customerFields(): array
    {
        return [
            BelongsTo::make('Customer', 'customer')
                ->showCreateRelationButton()
                ->searchable(),
        ];
    }

    protected function paymentFields(): array
    {
        return [
            Select::make('Payment Type', 'payment_type')
                ->options(function () {
                    return [
                        'check' => 'Check',
                        'cash' => 'Cash',
                        'credit' => 'Credit Card'
                    ];
                })
                ->hideFromDetail()
                ->hideFromIndex()
                ->creationRules('required'),
            BelongsTo::make('Credit Card', 'creditCard')
                ->showCreateRelationButton()
                ->searchable()
                ->fillUsing(function () {
                    //
                })
                ->creationRules('required_if:payment_type,credit')
                ->dependsOnCreating(
                    ['payment_type'], function ($field, $request, $formData) {
                    $field->{$formData->payment_type === 'credit' ? 'show' : 'hide'}();
                }
                )
                ->hideFromDetail()
                ->hideFromIndex(),
        ];
    }

    protected function deliveryFields(): array
    {
        return [
            Text::make('Name', 'shippingAddress_name')
                ->creationRules('required_with:productPhysicalMany')
                ->updateRules('required')
                ->hideFromIndex()
                ->hideFromDetail()
                ->fillUsing(function ($request, $model) {
                    if ($request->editMode == 'create') {
                        return;
                    }
                    $model->shippingAddress->first_name = $request->shippingAddress_name;
                    $model->shippingAddress->last_name = $request->shippingAddress_name;
                    $model->shippingAddress->save();
                }),
            Text::make('Address', 'shippingAddress_line_one')
                ->creationRules('required_with:productPhysicalMany')
                ->updateRules('required')
                ->hideFromIndex()
                ->hideFromDetail()
                ->fillUsing(function ($request, $model) {
                    if ($request->editMode == 'create') {
                        return;
                    }
                    $model->shippingAddress->line_one = $request->shippingAddress_line_one;
                    $model->shippingAddress->save();
                }),
            Text::make('Address line 2', 'shippingAddress_line_two')
                ->hideFromIndex()
                ->hideFromDetail()
                ->fillUsing(function ($request, $model) {
                    if ($request->editMode == 'create') {
                        return;
                    }
                    $model->shippingAddress->line_two = $request->shippingAddress_line_two;
                    $model->shippingAddress->save();
                }),
            Text::make('City', 'shippingAddress_city')
                ->creationRules('required_with:productPhysicalMany')
                ->updateRules('required')
                ->hideFromIndex()
                ->hideFromDetail()
                ->fillUsing(function ($request, $model) {
                    if ($request->editMode == 'create') {
                        return;
                    }
                    $model->shippingAddress->city = $request->shippingAddress_city;
                    $model->shippingAddress->save();
                }),
            Text::make('State', 'shippingAddress_state')
                ->creationRules('required_with:productPhysicalMany')
                ->updateRules('required')
                ->hideFromIndex()
                ->hideFromDetail()
                ->fillUsing(function ($request, $model) {
                    if ($request->editMode == 'create') {
                        return;
                    }
                    $model->shippingAddress->state = $request->shippingAddress_state;
                    $model->shippingAddress->save();
                }),
            Text::make('Zip code', 'shippingAddress_postcode')
                ->creationRules('required_with:productPhysicalMany')
                ->updateRules('required')
                ->hideFromIndex()
                ->hideFromDetail()
                ->fillUsing(function ($request, $model) {
                    if ($request->editMode == 'create') {
                        return;
                    }
                    $model->shippingAddress->postcode = $request->shippingAddress_postcode;
                    $model->shippingAddress->save();
                }),
        ];
    }

    private function getProductsField(): MorphedByMany
    {
        return
            MorphedByMany::make('Products', 'productVariants', 'App\Nova\SubResources\Order\OrderPhysical')
                ->allowDuplicateRelations()
                ->fields(function () {
                    return [
                        Text::make('Type')
                            ->hideWhenUpdating()
                            ->dependsOn(['productVariants'], function ($field, $request, $formData) {
                                $entry = $this->getProductVariant($formData->productVariants);
                                if ($entry) {
                                    $field->setValue($entry->shippable ? 'physical' : 'digital');
                                    return;
                                }
                                $field->setValue(null);
                            }),
                        Text::make('Description')
                            ->hideWhenUpdating()
                            ->dependsOn(['productVariants'], function ($field, $request, $formData) {
                                $entry = $this->getProductVariant($formData->productVariants);
                                if ($entry) {
                                    $field->setValue($entry->product->attribute_data->get('name'));
                                    return;
                                }
                                $field->setValue(null);
                            }),
                        Text::make('Identifier')
                            ->hideFromIndex()
                            ->hideWhenUpdating()
                            ->dependsOn(['productVariants'], function ($field, $request, $formData) {
                                $entry = $this->getProductVariant($formData->productVariants);
                                if ($entry) {
                                    $field->setValue($entry->sku);
                                    return;
                                }
                                $field->setValue(null);
                            }),
                        Number::make('Unit price'),
                        Number::make('Quantity'),
                        Number::make('Sub total'),
                        Number::make('Discount total'),
                        Hidden::make('Tax breakdown')
                            ->default(function () {
                                return '[]';
                            }),
                        Number::make('Tax total'),
                        Number::make('Total'),
                    ];
                })
                ->showOnDetail();
    }

    public static function afterUpdate(NovaRequest $request, $model)
    {
        $model->sub_total = $model->lines->sum('sub_total.value') * 100;
        $model->total = $model->lines->sum('total.value') * 100;
        $model->save();
    }

    private function getProductVariant($variantId)
    {
        return \Lunar\Models\ProductVariant::with('product')->find($variantId);
    }

    public static function creating()
    {
        $data = request();
        DB::commit();
        DB::beginTransaction();

        $address = [
            "name" => $data->input("shippingAddress_first_name"),
            "address_line_1" => $data->input("shippingAddress_line_one"),
            "address_line_2" => $data->input("shippingAddress_line_two"),
            "city" => $data->input("shippingAddress_city"),
            "state" => $data->input("shippingAddress_state"),
            "zip" => $data->input("shippingAddress_postcode"),
        ];
        $customer = Customer::query()->find($data->input('customer'));

        $productsData = collect($data->productPhysicalMany)->map(function ($item) {
            return (object)[
                'id' => $item['fields']['purchasable_id'],
                'count' => $item['fields']['quantity'],
            ];
        });
        $subscriptionsData = collect($data->subscriptions)->map(function ($item) {
            return (object)[
                'id' => $item['fields']['subscription_type'],
                'name' => $item['fields']['name'],
                "address_line_1" => $item['fields']["address_line_1"],
                "address_line_2" => $item['fields']["address_line_2"],
                "city" => $item['fields']["city"],
                "state" => $item['fields']["state"],
                "zip" => $item['fields']["postal_code"],
            ];
        });


        $products = \Lunar\Models\ProductVariant::query()->whereIn('id', $productsData->pluck('id'))
            ->get()
            ->map(function ($item) use ($productsData) {
                $data = $productsData->firstWhere('id', $item['id']);

                $item->count = $data->count;

                return $item;
            });

        $subscriptions = \App\Product::query()
            ->where('product_type_id', \App\Lunar\Cycle::$productType)
            ->whereIn('attribute_data->name->value', $subscriptionsData->pluck('id'))
            ->get()
            ->map(function ($item) use ($subscriptionsData, &$address) {
                $data = $subscriptionsData->firstWhere('id', $item->name);
                $item->delivery = (object)[
                    'name' => $data->name,
                    "address_line_1" => $data->address_line_1,
                    "address_line_2" => $data->address_line_2,
                    "city" => $data->city,
                    "state" => $data->state,
                    "zip" => $data->zip,
                ];

                if (!$address['name']) {
                    $address = [
                        'name' => $data->name,
                        "address_line_1" => $data->address_line_1,
                        "address_line_2" => $data->address_line_2,
                        "city" => $data->city,
                        "state" => $data->state,
                        "zip" => $data->zip,
                    ];
                }

                return $item;
            });

        $checkout = new CheckoutBackend;
        $checkout->setCustomer($customer);
        $checkout->setTypePayment($data->input("payment_type"));
        if ($data->input("payment_type") == 'credit') {
            $checkout->setCardId($data['creditCard']);
        }
        $checkout->setAddress($address);

        if ($products->count()) {
            $checkout->setProducts($products);
        }

        if ($subscriptions->count()) {
            $checkout->setSubscribes($subscriptions);
        }

        $response = $checkout->store();

        if ($response->status != 200) {
            response()->json($response, $response->status)->send();

            exit;
        }

        DB::commit();
        response()->json([
            'id' => null,
            'resource' => null,
            'redirect' => URL::make('/resources/' . static::uriKey()),
        ], 201)->send();

        exit;
    }


    protected function fillRepeaterProduct($request, $model): void
    {
        $model->physicalLines()->delete();

        foreach ($request->productPhysicalMany ?? [] as $item) {
            $variant = \Lunar\Models\ProductVariant::query()->find($item['fields']['purchasable_id']);

            $price = $variant->prices->first()->price->decimal();
            $qty = (int)$item['fields']['quantity'];

            $model->physicalLines()->create([
                'type' => $variant->getType(),
                'option' => $variant->getOptions(),
                'description' => $variant->getDescription(),
                'tax_breakdown' => new TaxBreakdown(),
                'tax_total' => 0,
                'quantity' => $item['fields']['quantity'],
                'identifier' => $variant->getIdentifier(),
                'unit_price' => $price,
                'unit_quantity' => $variant->getUnitQuantity(),
                'sub_total' => $price * $qty,
                'discount_total' => 0,
                'total' => $price * $qty,
                'purchasable_type' => 'Lunar\Models\ProductVariant',
                'purchasable_id' => $item['fields']['purchasable_id']
            ]);
        }
    }

    protected function fillRepeaterSubscribes($request, $model): void
    {
        $ids = array_filter(array_map(function ($item) {
            return $item['fields']['id'];
        }, $request->subscriptions));

        $model->lines()
            ->where('type', 'digital')
            ->where('purchasable_type', 'Lunar\Models\ProductVariant')
            ->whereNotIn('meta->subscription->id', $ids)
            ->delete();

        foreach ($request->subscriptions as $subscription) {
            $cycle = \App\Lunar\Cycle::getProductByName($subscription['fields']['subscription_type']);
            $price = $cycle->variant->prices->first()->price->decimal();
            $subscriptionMonth = \App\SubscriptionMonth::find($subscription['fields']['subscription_month_id']);

            $months = $subscription['fields']['subscription_type'] === 'yearly' ? 12 : 1;
            $start = HebArrayToEnglishDate([
                'year' => $subscriptionMonth->year,
                'month' => $subscriptionMonth->month,
                'day' => 1,
            ]);
            $finish = $subscription['fields']['subscription_type'] === 'yearly' ? Carbon::parse($start)->addYear() : Carbon::parse($start)->addMonth();
            if ($zone = Zone::getZip($subscription['fields']['postal_code'])) {
                $zone_id = $zone->id;
            } else {
                $zone_id = 0;
            }

            if ($subscription['fields']['id']) {
                $entry = $model->digitalLines()
                    ->where('meta->subscription->id', $subscription['fields']['id'])
                    ->first();

                $entrySubscribe = \App\Subscription::query()->find($subscription['fields']['id']);

                $entrySubscribe->update([
                    'address_line_1' => $subscription['fields']['address_line_1'],
                    'address_line_2' => $subscription['fields']['address_line_2'],
                    'city' => $subscription['fields']['city'],
                    'state' => $subscription['fields']['state'],
                    'postal_code' => $subscription['fields']['postal_code'],
                    'name' => $subscription['fields']['name'],
                    'issues' => $months,
                    'start' => $start,
                    'finish' => $finish,
                    'subscription_month_name' => $subscriptionMonth->name,
                    'subscription_month_id' => $subscriptionMonth->id,
                    'renew' => true,
                    'payment_type' => 'Credit Card',
                    'subscription_type' => $subscription['fields']['subscription_type'],
                    'cycle_id' => DB::table('cycles')->where('months', $months)->first()?->id,
                    'zone_id' => $zone_id,
                    'credit_card_id' => data_get($model->meta->toArray(), 'creditCardData.creditCardId'),
                    'customer_id' => $request->customer,
                ]);

                $entry->update([
                    'description' => $cycle->variant->getDescription(),
                    'tax_breakdown' => new TaxBreakdown(),
                    'tax_total' => 0,
                    'quantity' => 1,
                    'identifier' => $cycle->variant->getIdentifier(),
                    'unit_price' => $price,
                    'unit_quantity' => $cycle->variant->getUnitQuantity(),
                    'sub_total' => $price,
                    'discount_total' => 0,
                    'total' => $price,
                    'purchasable_type' => 'Lunar\Models\ProductVariant',
                    'purchasable_id' => $cycle->variant->id,
                    'meta->subscription' => $entrySubscribe
                ]);
            } else {
                $subEntry = \App\Subscription::create([
                    'order_id' => $entry->id,
                    'address_line_1' => $subscription['fields']['address_line_1'],
                    'address_line_2' => $subscription['fields']['address_line_2'],
                    'city' => $subscription['fields']['city'],
                    'state' => $subscription['fields']['state'],
                    'postal_code' => $subscription['fields']['postal_code'],
                    'name' => $subscription['fields']['name'],
                    'issues' => $months,
                    'start' => $start,
                    'finish' => $finish,
                    'subscription_month_name' => $subscriptionMonth->name,
                    'subscription_month_id' => $subscriptionMonth->id,
                    'renew' => true,
                    'payment_type' => 'Credit Card',
                    'subscription_type' => $subscription['fields']['subscription_type'],
                    'cycle_id' => DB::table('cycles')->where('months', $months)->first()?->id,
                    'zone_id' => $zone_id,
                    'credit_card_id' => data_get($model->meta->toArray(), 'creditCardData.creditCardId'),
                    'customer_id' => $request->customer,
                ]);

                $entry = $model->digitalLines()
                    ->create([
                        'description' => $cycle->variant->getDescription(),
                        'tax_breakdown' => new TaxBreakdown(),
                        'tax_total' => 0,
                        'quantity' => 1,
                        'identifier' => $cycle->variant->variant->getIdentifier(),
                        'unit_price' => $price,
                        'unit_quantity' => $cycle->variant->getUnitQuantity(),
                        'sub_total' => $price,
                        'discount_total' => 0,
                        'total' => $price,
                        'purchasable_type' => 'Lunar\Models\ProductVariant',
                        'purchasable_id' => $cycle->variant->id,
                        'meta->subscription' => $subEntry
                    ]);
            }
        }
    }
}
