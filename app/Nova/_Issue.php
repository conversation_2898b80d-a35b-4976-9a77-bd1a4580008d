<?php

namespace App\Nova;

use Capitalc\Checkbox\Checkbox;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class _Issue extends Resource
{
    public static $displayInNavigation = false;

    public static $group = 'Articles';
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Issue::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'number';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'number',
    ];

    /**
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Name'),
            Number::make('Number'),
            ...collect(\App\Section::all())
                ->map(function ($section) {
                    return Images::make($section->name, $section->index)
                        ->singleImageRules('nullable');
                })->toArray(),
            Checkbox::make('Active')->withMeta(['value' => $this->active ?? true])->nullable(),
            DateTime::make('Publish Date'),
            HasMany::make('Articles'),
        ];
    }

    /**
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
