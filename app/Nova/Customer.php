<?php

namespace App\Nova;

use Capitalc\Notes\Notes;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Panel;
use <PERSON><PERSON>\Nova\Fields\Select;

class Customer extends Resource
{
    public static $group = 'Subscriptions';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Customer';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'phone',
        'email',
    ];

    public function subtitle()
    {
        return "$this->email - $this->phone";
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name', 'name')
                ->rules('required'),

            Text::make('Phone')
                ->rules('required'),

            Text::make('Email')
                ->nullable()
                ->rules('nullable', 'email', 'unique:customers,email,{{resourceId}}')

                ->dependsOn(["dont_have_email"], function ($panel, $request, $formData) {
                    $panel->{!$formData->dont_have_email ? 'show' : 'hide'}();
                    $formData->email = null;
                }),
            Boolean::make('I don\'t have email', 'dont_have_email')
                ->onlyOnForms()
                ->fillusing(function () {

                }),

            Boolean::make('Sign up for emails', 'email_signup')
            	->onlyOnForms()
                ->hideWhenUpdating() 
                ->fillusing(function () {
                    
                }),

            Select::make('Contact Method', 'preferred_contact_method')->rules('required')->options([
                'email' => 'Email',
                'text' => 'Text',
                'phone' => 'Phone'
            ])->default('email'),

            new Panel('Notes', [
                Notes::make('Notes')->options(setting()->getOptions('reasons'))
            ]),

            HasMany::make('Subscriptions'),
            HasMany::make('Orders', 'orders'),
            HasMany::make('Credit Card', 'cards'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }

    public static function afterCreate($request, $model)
    {
        $model->signUp($request->email_signup);
    }
}
