<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\Select;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\Text;

class Subscriber extends Resource
{
    public static $model = 'App\Subscriber';

    public static $title = 'name';

    public static $group = 'Campaigns';

    public static $search = [
        'email',
    ];

    public static function label()
    {
        return 'Email Subscribers';
    }

    public function fields(NovaRequest $request)
    {
        return [

            Text::make('Email')
                ->sortable()
                ->rules('required', 'email', 'max:254')
                ->creationRules('unique:subscribers,email')
                ->updateRules('unique:subscribers,email,{{resourceId}}'),

            Select::make('state')
                ->options([
                    'Subscribe' => 'Subscribe',
                    'Unsubscribed' => 'Unsubscribed',
                    'Deleted' => 'Deleted',
                ]),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
