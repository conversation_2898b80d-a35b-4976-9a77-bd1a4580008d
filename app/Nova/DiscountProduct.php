<?php

namespace App\Nova;

use App\Rules\Nova\ProductDetailsRule;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Image;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\KeyValue;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Badge;
use Laravel\Nova\Http\Requests\NovaRequest;
use Illuminate\Database\Eloquent\Model;

class DiscountProduct extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Product>
     */
    public static $model = \Lunar\Models\Product::class;

    public static $group = 'Store';

    public static $showpollingtoggle = true;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'attribute_data.name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'attribute_data->name->value',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Image::make('Images')
                ->preview(function () {
                    return $this->resource->img;
                })
                ->thumbnail(function () {
                    return $this->resource->img;
                })
            ,
            Text::make('Name', 'attribute_data.name'),

        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
    public static function usesScout()
    {
        return false;
    }
}
