<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use App\Nova\Actions\Refund;
use <PERSON>vel\Nova\Fields\Currency;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use Alex<PERSON>zel\DependencyContainer\DependencyContainer;

class Payment extends Resource
{
    public static $group = 'Subscriptions';
    public static $displayInNavigation = false;
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Payment::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            BelongsTo::make('Order')->hideFromIndex(),

            Select::make('Payment Type', 'payment_type')->options([
                'Check' => 'Check',
                'Cash' => 'Cash',
                'Credit Card' => 'Credit Card'
            ]),

            DependencyContainer::make([
                Text::make('Card', 'card'),
                Text::make('Expiration', 'expiration')->help('4 digits')->rules('max:4'),
                Text::make('Security', 'security'),
            ])->dependsOn('payment_type', 'Credit Card')->onlyOnForms(),
            Text::make('Ref Num', 'creditCardPayment.refnum'),

            Currency::make('Amount'),
            Currency::make('Refunded')->exceptOnForms(),
            Text::make('Card Ending', 'creditCard.last_four')
            // ->resolveUsing(function ($item) {
            //     // dd($item);
            // })
            ,
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            (new Refund)
                ->canRun(function () {
                    return true;
                }),
            // (new Cancel)
            //     ->canRun(function () {
            //         return true;
            //     })
        ];
    }
}
