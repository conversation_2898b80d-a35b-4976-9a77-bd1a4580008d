<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\File;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ImportPromos extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields)
    {
//        Excel::import(new \App\Imports\UsersImport, $fields->file);
        return Action::message('It worked!');
    }


    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            File::make('File')
                ->rules('required'),
        ];
    }

}
