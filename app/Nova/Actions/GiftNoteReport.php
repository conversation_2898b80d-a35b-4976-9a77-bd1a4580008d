<?php

namespace App\Nova\Actions;

use App\Zone;
use Laravel\Nova\Fields\Date;
use Illuminate\Bus\Queueable;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Actions\Action;
use Illuminate\Support\Collection;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON>wenzel\DependencyContainer\DependencyContainer;

class GiftNoteReport extends Action
{
    use InteractsWithQueue, Queueable;


    public function handle(ActionFields $fields, Collection $models)
    {
        return Action::openInNewTab("/nova-api/download-gift-note-report?start=$fields->start&end=$fields->end&zone_id=$fields->zone_id");
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $zone = Zone::find(request()->resourceId);

        return [
            Select::make('Range')->options([
                'weekly' => 'Weekly',
                'custom' => 'Custom',
            ])->default('weekly'),

            DependencyContainer::make([
                Select::make('Start')
                    ->options(optional($zone)->previousDates())
                    ->default(today()->startOfWeek()->toDateString()),
            ])->dependsOn('range', 'weekly'),

            DependencyContainer::make([
                Date::make('Start')->rules('required'),
                Date::make('End')->rules('required'),
            ])->dependsOn('range', 'custom'),

            Hidden::make('Zone', 'zone_id')->withMeta([
                'value' => optional($zone)->id
            ]),
        ];
    }
}
