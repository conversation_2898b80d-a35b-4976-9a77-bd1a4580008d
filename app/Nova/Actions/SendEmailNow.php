<?php

namespace App\Nova\Actions;

use App\Http\Controllers\EmailsController;
use Bashy\CampaignMonitor\Facades\CampaignMonitor;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class SendEmailNow extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $campaign = EmailsController::getCampaign($models->first()->id);

        $data = ['ConfirmationEmail' => '<EMAIL>'];
        if ($campaign['campaign']->toggle_schedule && $campaign['campaign']->schedule) {
            $data['SendDate'] = $campaign['campaign']->schedule->format('Y-m-d H:i');
        } else {
            $data['SendDate'] = 'immediately';
        }

        $results = CampaignMonitor::Campaigns($campaign['id'])
            ->send($data);

        $campaign['campaign']->update(['sent' => true]);

        return Action::message('Campaign was successfully sent.');
    }


    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
