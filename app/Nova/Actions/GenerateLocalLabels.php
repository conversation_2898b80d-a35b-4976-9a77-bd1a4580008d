<?php

namespace App\Nova\Actions;

use App\Zone;
use Capitalc\Checkbox\Checkbox;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;

class GenerateLocalLabels extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $zone = Zone::find($fields->zone);

        $zone->createLocalLabels($fields->send);

        return Action::openInNewTab("/nova-api/download-labels?fileName=" . $zone->name);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Zone')
                ->options(Zone::pluck('name', 'id'))
                ->required(),

            Checkbox::make('<PERSON>', 'send')->default(1),

        ];
    }
}
