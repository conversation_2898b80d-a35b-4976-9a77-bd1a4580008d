<?php

namespace App\Nova\Actions;

use App\CreditCard;
use App\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON><PERSON><PERSON>shev\NovaMoneyField\Money;

class ApplyPayment extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;

    public function __construct($model)
    {
        $this->model = $model;
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        $order = $models->first();
        $credit_card = CreditCard::find(request()->credit_card_id);
        if (request()->payment_type == 'Credit Card') {
            if ($credit_card) {
                $credit_card_payment = $credit_card->charge(request()->amount);
                Payment::create([
                    'amount' => request()->amount,
                    'order_id' => $order->id,
                    'credit_card_payment_id' => $credit_card_payment->id,
                    'credit_card_id' => $credit_card->id,
                    'payment_type' => 'Credit Card',
                ]);
            }
        } else {
            Payment::create([
                'amount' => request()->amount,
                'order_id' => $order->id,
                'credit_card_payment_id' => null,
                'credit_card_id' => null,
                'payment_type' => request()->payment_type,
            ]);
        }
    }
    //13473885118 17185094633

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        if ($this->model) {
            return [
                Select::make('Payment Type')->hideFromIndex()->rules('required')->options([
                    'Check' => 'Check',
                    'Cash' => 'Cash',
                    'Credit Card' => 'Credit Card'
                ]),

                //DependencyContainer::make([])->dependsOn('payment_type', 'Credit Card'),

                // CreditCardField::make('')
                //     // ->dependsOn('customer')
                //     ->withMeta([
                //         'name' => 'Credit Card',
                //         'customer_id' => $this->model->customer_id,
                //     ])
                //     ->hideFromIndex(),

                Money::make('Amount')
            ];
        } else {
            return [];
        }
    }
}
