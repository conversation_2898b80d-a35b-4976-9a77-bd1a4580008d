<?php

namespace App\Nova\Actions;

use App\Http\Controllers\EmailsController;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class UnscheduleCampaign extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        EmailsController::deleteCampaign($models->first()->id);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
