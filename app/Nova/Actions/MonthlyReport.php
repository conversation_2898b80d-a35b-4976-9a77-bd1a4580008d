<?php

namespace App\Nova\Actions;

use App\Jobs\GenerateMonthlyZoneReport;
use App\SubscriptionMonth;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Actions\Action;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;
use Illuminate\Queue\InteractsWithQueue;

class MonthlyReport extends Action
{
    use InteractsWithQueue, Queueable;

    public $confirmButtonText = 'Download';

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        Log::info('MonthlyReport Action handle method triggered');

        $zoneId = $models->first()->id;

        dispatch(new GenerateMonthlyZoneReport($fields->subscription_month_id, $zoneId));

        return Action::message('The monthly report is being generated.');
    }


    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $subscriptionMonths = SubscriptionMonth::pluck('name', 'id');

        return [
            Select::make('Subscription Month', 'subscription_month_id')->options($subscriptionMonths),
        ];
    }
}
