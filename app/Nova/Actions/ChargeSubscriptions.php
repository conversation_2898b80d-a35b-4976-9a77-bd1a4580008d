<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use App\Http\Controllers\SubscriptionController;

class ChargeSubscriptions extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        // If no subscriptions are passed, return a message
        if ($models->isEmpty()) {
            return Action::message('No subscriptions were selected for renewal.');
        }

        $controller = app(SubscriptionController::class);

        // Counter for successful renewals
        $successCount = 0;

        // Iterate through the subscriptions and renew each one
        foreach ($models as $subscription) {
            try {
                $controller->renew($subscription->id);
                $successCount++;
            } catch (\Exception $e) {
                // You can log the error here if needed
                Log::error("Failed to renew subscription with id: {$subscription->id}. Error: " . $e->getMessage());
            }
        }

        // Handle different message scenarios
        if ($successCount === $models->count()) {
            return Action::message("All {$models->count()} subscriptions charged successfully!");
        } elseif ($successCount === 0) {
            return Action::message("There was a problem with {$subscription->name}'s subscription. Id: {$subscription->id}");
        } else {
            return Action::message("{$successCount} out of {$models->count()} subscriptions charged successfully!");
        }
    }


    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
