<?php

namespace App\Nova\Actions;

use App\Http\Controllers\SubscriptionController;
use App\SubscriptionMonth;
use App\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\ActionFields;

class CreateAllShipments extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        // $this->chargeSubscriptions();

        $month = SubscriptionMonth::find($fields->month);

        $subscriptions = Subscription::with('order', 'customer')
            ->where('issues', '>', 0)
            ->current()
            ->where('subscription_month_id', '<=', $month->id)
            ->get();

        $subscriptions->each->createShipment($month);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Month')
                ->options(\App\SubscriptionMonth::pluck('name', 'id'))
                ->required()
                ->withMeta(['value' => \App\SubscriptionMonth::getNext()->id]),
        ];
    }

    public function chargeSubscriptions()
    {
        $subscriptions = \App\Subscription::where('issues', 0)
            ->where('renew', true)
            ->pluck('id');

        $controller = app(SubscriptionController::class);

        $subscriptions->each(function ($id) use ($controller) {
            $controller->renew($id);
        });
    }
}
