<?php

namespace App\Nova\Actions;

use App\SubscriptionMonth;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class GenerateMoreMonths extends Action
{
    public $standalone = true;

    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        $next = nextRoshChodesh();
        collect()
            ->times($fields->amount)
            ->each(function () use (&$next) {
                SubscriptionMonth::getNext($next);
                $next = nextRoshChodesh($next);
            });
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Number::make('Amount of months from now', 'amount'),
        ];
    }
}
