<?php

namespace App\Nova\Actions;

use App\NamedPublication;
use App\Shipment;
use App\SubscriptionMonth;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use setasign\Fpdi\Fpdi;

class DownloadAllLabels extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $namedPublication = Shipment::query();

        if ($fields->month) {
            $namedPublication->where('subscription_month_id', $fields->month);
        }

        if (!$fields->shipped) {
            $namedPublication->where('shipped_at', null);
        }
        $namedPublication->whereNotNull('label_path');

        $pdf = new Fpdi;
        $files = $namedPublication->get()->pluck('label_path');
        if (!$files->count()) {
            return Action::message('No Downloads available.');
        }

        foreach ($files as $file) {
            $pdf->AddPage('P', [101.6, 152.4]);
            $pdf->setSourceFile($file);
            $tplId = $pdf->importPage(1);
            $pdf->useTemplate($tplId, 0, 0, 101.6, 152.4);
        }

        $fileName = time();
        $pdfFilePath = storage_path("app/public/" . $fileName . ".pdf");
        $pdf->Output('F', $pdfFilePath);

        if ($fields->ship_now) {
            $namedPublication->update(['shipped_at' => now()]);
        }

        return Action::download(url('storage/' . $fileName . '.pdf'), $fileName . '.pdf');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Month')
                ->options(SubscriptionMonth::pluck('name', 'id'))
                ->nullable()
                ->withMeta(['value' => SubscriptionMonth::getNext()->id]),

            Boolean::make('Mark As Shipped', 'ship_now')
                ->withMeta(['value' => true]),

            Boolean::make('Include Already Shipped', 'shipped'),
        ];
    }
}
