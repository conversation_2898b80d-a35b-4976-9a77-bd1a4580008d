<?php

namespace App\Nova\Actions;

use App\Shipment;
use App\Zone;
use Capitalc\Checkbox\Checkbox;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Fields\ActionFields;

class GenerateAllLabels extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $shipments = Shipment::where('shipped_at', null);
        if (!$fields->zones) {
            $shipments->whereNotIn('zone_id', Zone::where('route_4_me')->pluck('id'));
        }
        $shipments
            ->get()
            ->each
            ->createLabel();
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Checkbox::make('With Zones', 'zones')->withMeta(['value' => false])->nullable(),
        ];
    }
}
