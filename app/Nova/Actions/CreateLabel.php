<?php

namespace App\Nova\Actions;

use App\Subscription;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Date;
use Laravel\Nova\Http\Requests\NovaRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use App\ShipStationConfiguration;
use App\Services\CreateLabelForShipment;
use App\Services\ShipStationOrderService;

class CreateLabel extends Action
{
    public function fields(NovaRequest $request)
    {
        return [
            Date::make('Shipment Date', 'shipment_date')->rules('required'),
        ];
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        $errorMessages = [];
        $successCount = 0;
        try {
            $config = ShipStationConfiguration::findOrFail(1);
        } catch (ModelNotFoundException) {
            return Action::danger('ShipStation configuration not found.');
        }
        $batchId = null;

        if ($models->count() > 1) {
            $batchId = Carbon::now()->format('Y-m-d_H-i-s');
        }

        $orderService = resolve(ShipStationOrderService::class);
        foreach ($models as $model) {
            $subscription = Subscription::where('id', '=', $model->subscription_id)->first();
            try {
                $addressData = [
                    'name' => $subscription->name,
                    'street1' => $subscription->address_line_1,
                    'street2' => $subscription->address_line_2,
                    'city' => $subscription->city,
                    'state' => $subscription->state,
                    'postalCode' => $subscription->postal_code,
                    'country' => 'US',
                    'phone' => $subscription->customer->phone,
                    'residential' => true,
                ];

                if (is_null($model->order_id)) {
                    Log::info('Order ID is missing, creating a new order.');

                    $shipStationOrderData = [
                        'orderNumber' => $model->id,
                        'orderDate' => $model->created_at->toIso8601String(),
                        'orderStatus' => 'awaiting_shipment',
                        'billTo' => $addressData,
                        'shipTo' => $addressData
                    ];

                    $orderObject = $orderService->createOrder($shipStationOrderData);

                    if (!$orderObject) {
                        throw new \Exception('Failed to create ShipStation order.');
                    }

                    $orderId = $orderObject['orderId'] ?? null;
                    if (!$orderId) {
                        throw new \Exception('Order ID is missing in the created order.');
                    }

                    $model->order_id = $orderId;
                    $model->save();

                    Log::info("Order ID {$orderId} saved in the model.");
                }

                $addressDataShipFrom = [
                    'name' => 'Vinderkind',
                    'street1' => '1303 53rd street',
                    'street2' => '1303 53rd street',
                    'city' => 'brooklyn',
                    'state' => 'NY',
                    'postalCode' => '11219',
                    'country' => 'US',
                    'phone' => '3473519597',
                ];

                $labelData = [
                    "orderId" => $model->order_id,
                    "carrierCode" => $config->carrier_code,
                    "serviceCode" => $config->service_code,
                    "confirmation" => "delivery",
                    "shipDate" => $fields->shipment_date,
                    "packageCode" => $config->package_code,
                    "weight" => [
                        "value" => $config->weight_value,
                        "units" => $config->weight_units
                    ],
                    "testLabel" => env('SHIPSTATION_TEST'),
                    'shipFrom' => $addressDataShipFrom,
                    'shipTo' => $addressData
                ];

                //Log::info('Label data:', ['labelData' => $labelData]);

                $createLabelForShipment = resolve(CreateLabelForShipment::class);
                if ($batchId !== null) {
                    $labelResult = $createLabelForShipment->createLabelForShipment($labelData, $batchId);
                } else {
                    $labelResult = $createLabelForShipment->createLabelForShipment($labelData);
                }

                if ($labelResult) {
                    $successCount++;
                }
            } catch (\Exception $e) {
                $modelIdentifier = $model->id; // Or any other identifier for the model
                $errorMessage = "An error occurred with ID {$modelIdentifier}: {$e->getMessage()}";
                Log::error($errorMessage);
                $errorMessages[] = $errorMessage;
                continue; // Continue with the next model in the loop
            }
        }

        if ($batchId) {
            $zipFilePath = $createLabelForShipment->generateBatchZip($batchId);

            if ($zipFilePath) {
                Log::info("ZIP file generated at path: {$zipFilePath}");
            } else {
                Log::warning("Failed to generate ZIP file for batch ID: {$batchId}");
            }
        }

        if (!empty($errorMessages)) {
            return Action::danger(implode(' | ', $errorMessages));
        }

        // If there are no errors, return a success message with the count of successful label creations
        return Action::message("Labels created successfully for {$successCount} shipments!");
    }
}
