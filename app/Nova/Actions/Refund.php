<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Actions\Action;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Fields\ActionFields;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Refund extends Action
{
    use InteractsWithQueue, Queueable, SerializesModels;

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each(function ($model) use ($fields) {
            if ($model->creditCardPayment) {
                // $creditCard = $model->creditCardPayment->creditCard;
                $amount = data_get($fields, 'amount') ?? $model->amount;

                $model->creditCardPayment->refund($amount*100);
            }
        });
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Number::make('Refund Amount', 'amount')
                ->help('Leave empty to refund full amount'),
        ];
    }
}
