<?php

namespace App\Nova\Actions\Discount;

use App\Http\Requests\Nova\ComaFieldRequest;
use App\Lunar\DiscountTypes\AmountOff;
use App\Lunar\DiscountTypes\BuyXGetY;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\MultiSelect;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Lunar\Models\Channel;

class ImportAction extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields)
    {
        $coupons = $this->promoFormatHandler($fields->import_coupons);
        $products = \Lunar\Models\Product::query()
            ->with('variants')
            ->whereIn('id',  $fields->import_products)
            ->get();



        $itemsProduct = [];
        foreach($products as $product){
            $itemsProduct[] = [
                'purchasable_type' => \Lunar\Models\Product::class,
                'purchasable_id' => $product->id,
                'type' => 'limitation',
            ];

            foreach ($product->variants as $variant) {
                $itemsProduct[] = [
                    'purchasable_type' => \Lunar\Models\ProductVariant::class,
                    'purchasable_id' => $variant->id,
                    'type' => 'limitation',
                ];
            }
        }

        $defaultDiscountData = (object)[
            'name' => 'Imported Discount ',
            'starts_at' => now(),
            'ends_at' => NULL,
            'enabled' => true,
            'type' => AmountOff::class
        ];
        $data = [
            'min_prices' =>[
                'USD' => $fields->import_min_prices * 100
            ],
        ];

        if($fields->import_type === 'percentoff'){
            $data['fixed_value'] = false;
            $data['percentage'] = $fields->import_value;
        }
        if ($fields->import_type === 'fixed') {
            $data['fixed_value'] = true;
            $data['fixed_values'] = [
                'USD' => $fields->import_value
            ];
        }

        foreach($coupons as $coupon) {
            $data['coupon'] = $coupon;
            if(\Lunar\Models\Discount::query()->where('coupon', $coupon)->exists()){
                continue;
            }
            $discountEntry = \Lunar\Models\Discount::query()
                ->create([
                    'name' => $defaultDiscountData->name . ' ' . $coupon,
                    'handle' => Str::slug($defaultDiscountData->name . ' ' . $coupon, '_'),
                    'coupon' => $coupon,
                    'type' => $defaultDiscountData->type,
                    'starts_at' => $defaultDiscountData->starts_at,
                    'ends_at' => $defaultDiscountData->ends_at,
                    'data' => $data,
                    'enabled' => $defaultDiscountData->enabled,
                ]);

            $channels = Channel::all()->mapWithKeys(function ($channel) use ($discountEntry) {
                return [
                    $channel->id => [
                        'enabled' => $discountEntry->enabled,
                        'starts_at' => $discountEntry->starts_at,
                        'ends_at' => $discountEntry->ends_at,
                    ],
                ];
            });

            $discountEntry->channels()->sync($channels);
            $discountEntry->purchasableLimitations()->createMany($itemsProduct);
        }
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Text::make('Coupon codes', 'import_coupons')
                ->help('Promo code for applying discount, separated by commas')
                ->rules('required', new ComaFieldRequest()),
            Select::make('Type', 'import_type')->rules('required')
                ->fullWidth()
                ->options([
                    'percentoff' => 'percentoff',
                    'fixed' => 'fixed',
                ])->rules('required'),
            Number::make('Min prices', 'import_min_prices')
                ->rules('required'),
            Number::make('Percent or Fixed value', 'import_value')
                ->rules('required'),
            MultiSelect::make('Product', 'import_products')
                ->options(function () {
                    return \Lunar\Models\Product::all()->pluck('attribute_data.name', 'id')->toArray();
                })
                ->rules('required'),
        ];
    }

    protected function promoFormatHandler($promoText): array
    {
        $promoArray = explode(',', $promoText);

        $promoArray = array_map(function ($value) {
            $value = trim($value);
            $value = Str::slug($value, '_');
            return Str::upper($value);
        }, $promoArray);

        $promoArray = array_filter($promoArray, function ($value) {
            return !empty($value);
        });

        return array_unique($promoArray);
    }
}
