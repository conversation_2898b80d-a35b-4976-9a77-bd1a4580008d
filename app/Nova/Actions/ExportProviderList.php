<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use <PERSON>vel\Nova\Actions\Action;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Illuminate\Queue\InteractsWithQueue;
use App\Jobs\ProviderExportJob;

class ExportProviderList extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        $model = $models->first();

        // Dispatch the new job to handle export logic.
        ProviderExportJob::dispatch($model->id);

        return Action::message('Provider Export Job has been dispatched!');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
