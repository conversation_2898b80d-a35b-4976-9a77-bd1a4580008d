<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use setasign\Fpdi\Fpdi;

class DownloadRoutePdf extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $route = $models->first();

        $route->updateSortDataFromR4M();

        // dd(
        //     \App\NamedPublication::with('subscription.zone')
        //         ->find(data_get($route, 'meta.sort'))
        //         ->sortBy(function ($shipment) use ($route) {
        //             return array_search($shipment->id, data_get($route, 'meta.sort'));
        //         })
        // );

        $pdf = new Fpdi;
        $folder_name = time();

        \File::makeDirectory(storage_path() . "/" . $folder_name);

        $shipments = \App\Shipment::with('subscription.zone')
            ->find(data_get($route, 'meta.sort'));

        $shipments->sortBy(function ($shipment) use ($route) {
            return array_search($shipment->id, data_get($route, 'meta.sort'));
        })
            ->each(function ($shipment) use ($folder_name, $pdf, $route) {
                $path = storage_path() . "/{$folder_name}/shipment-{$shipment->id}.pdf";
                $data = [
                    'shipment' => $shipment,
                    'subscription' => $shipment->subscription,
                    'address' => $shipment->subscription->getVerifiedAddress(),
                    'zone' => $shipment->subscription->zone->name,
                    'sort' => array_search($shipment->id, data_get($route, 'meta.sort')) + 1 . '',

                    // 'gift' => \DB::table('named_publications')->where([
                    //     'subscription_id' => $shipment->subscription->id,
                    //     'subscription_month_id' => 10,
                    // ])->doesntExist()
                ];

                \PDF::loadView('label', $data)
                    ->setPaper('a6')
                    ->save($path);

                $pdf->AddPage('P', [101.6, 152.4]);
                $pdf->setSourceFile($path);
                $tplId = $pdf->importPage(1);
                $pdf->useTemplate($tplId, 0, 0, 101.6, 152.4);
                \File::delete($path);
            });

        \File::deleteDirectory(storage_path() . "/" . $folder_name);

        $fileName = $route->name;
        $pdfFilePath = storage_path("app/public/" . $fileName . ".pdf");
        $pdf->Output('F', $pdfFilePath);


        return Action::download(url('storage/' . $fileName . '.pdf'), $fileName . '.pdf');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
