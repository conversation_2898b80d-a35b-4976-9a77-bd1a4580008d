<?php

namespace App\Nova\Actions\Subscription;

use App\Jobs\CalculateUnsentCurrentIssuesJob;
use App\Services\ShipStationConfigurationService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Http\Requests\NovaRequest;

class DownloadUnsentCurrentIssues extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields)
    {
        $shipStationService = app(ShipStationConfigurationService::class);

        if (!empty($fields->shipment_date)) {
            $missingFields = $shipStationService->checkMissingFields();

            if (is_string($missingFields)) {
                return Action::danger($missingFields);
            }

            if (!empty($missingFields)) {
                $missingFieldsStr = implode(', ', $missingFields);
                return Action::danger("The following fields are missing: $missingFieldsStr");
            }
        }

        CalculateUnsentCurrentIssuesJob::dispatch($fields);

        return Action::message('The report is being generated. You will be notified upon completion.');
    }


    public function fields(NovaRequest $request)
    {
        return [
            Boolean::make('Substract issues', 'decrease_issues_increase_publications')
                ->help('Choose this option to decrease the number of issues by 1, for active subscriptions and increase the number of publications sent by 1 before downloading the file.'),

            Date::make('Shipment Date', 'shipment_date')
                ->help('Choose the shipment date for the active subscriptions.'),
        ];
    }
}
