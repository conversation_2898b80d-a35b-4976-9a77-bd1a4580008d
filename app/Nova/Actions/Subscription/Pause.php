<?php

namespace App\Nova\Actions\Subscription;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Date;
use Laravel\Nova\Fields\Number;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Pause extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each->pauseFor($fields->start, $fields->weeks);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Date::make('Start'),

            Number::make('Weeks')
                ->rules('required'),
        ];
    }
}
