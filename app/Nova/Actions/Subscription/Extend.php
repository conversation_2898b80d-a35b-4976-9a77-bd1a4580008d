<?php

namespace App\Nova\Actions\Subscription;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Extend extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each->extendFor($fields->months, $fields->reason);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Number::make('Months')
                ->rules('required'),
            Select::make('Reason')->options(setting()->getOptions('reasons'))->help('Add more reasons by <a href="/admin/resources/settings/' . setting()->getId('reasons') . '/edit">Settings</a>')
        ];
    }
}
