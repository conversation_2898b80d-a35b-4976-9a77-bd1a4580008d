<?php

namespace App\Nova\Actions\Subscription;

use App\Jobs\Subscription\ResumeJob;
use App\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Http\Requests\NovaRequest;

class Renew extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $product = Product::find($fields->cycle);
        $subscription = $models->first();
        ResumeJob::dispatch($subscription->id, $product, $fields->free, 'auto-renew-admin');
    }

    /**
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Cycle')->options(
                Product::cycles()->get()->mapWithKeys(function ($cycle) {
                    return [
                        $cycle->id => $cycle->name,
                    ];
                })
            ),
            Boolean::make('Renew For Free', 'free')
        ];
    }
}
