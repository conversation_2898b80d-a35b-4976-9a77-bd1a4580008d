<?php

namespace App\Nova\Actions\Subscription;

use App\Cycle;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class ChangeCycle extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $subscriptions)
    {
        return;
        $new_cycle = Cycle::query()->select(['months'])->find($fields->cycle);

        $subscriptions->each(function($subscription) use ($fields, $new_cycle) {
            if($subscription->cycle != $fields->cycle) {
                $subscription->update([
                    'cycle_id' => $fields->cycle,
                    'finish' => $subscription->finish->addMonths($new_cycle->months - $subscription->cycle->months),
                ]);
            }
        });

    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Cycle')
                ->help('Changing the cycle will automatically charge/refund the payment accordingly.')
                ->rules('required')
                ->options(Cycle::pluck('name', 'id'))
        ];
    }
}
