<?php

namespace App\Nova\Actions\Subscription;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Unpause extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each->unPause();
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
