<?php

namespace App\Nova\Actions\Subscription;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class CancelAction extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Cancel Subscription';

    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each(fn($model) => $model->cancel());
    }

    public function fields(NovaRequest $request)
    {
        return [];
    }
}
