<?php

namespace App\Nova\Actions\Subscription;

use App\Services\RefundService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Http\Requests\NovaRequest;

class RefundPartialAction extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        $amounts = 0;
        $models->each(function($model) use ($fields, &$amounts) {
            $service = app(RefundService::class);
            $service->setSubscription($model->id);
            $amounts += $service->partials((float)$fields->price);
        });

        return ActionResponse::message('Refunded: $' . number_format($amounts / 100, 2));
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Number::make('price')->min(1)->max(100000)->step(0.01),
        ];
    }
}
