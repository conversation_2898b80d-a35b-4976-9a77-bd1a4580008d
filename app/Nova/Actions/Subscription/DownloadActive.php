<?php

namespace App\Nova\Actions\Subscription;

use App\Jobs\CalculatePublicationsJob;
use App\Services\ShipStationConfigurationService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Http\Requests\NovaRequest;

class DownloadActive extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields)
    {
        $shipStationService = app(ShipStationConfigurationService::class);

        if (!empty($fields->shipment_date)) {
            $missingFields = $shipStationService->checkMissingFields();

            if (is_string($missingFields)) {
                return Action::danger($missingFields);
            }

            if (!empty($missingFields)) {
                $missingFieldsStr = implode(', ', $missingFields);
                return Action::danger("The following fields are missing: $missingFieldsStr");
            }
        }

        CalculatePublicationsJob::dispatch($fields);

        return Action::message('The report is being generated. You will be notified upon completion.');
    }


    public function fields(NovaRequest $request)
    {
        return [
            Boolean::make('Increment Publications', 'increment_publications')
                ->help('Choose this option to increment the publication count for active subscriptions before downloading the file.'),
            Date::make('Shipment Date', 'shipment_date')
                ->help('Choose the shipment date for the active subscriptions.'),
        ];
    }
}
