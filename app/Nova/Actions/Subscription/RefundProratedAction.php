<?php

namespace App\Nova\Actions\Subscription;

use App\Services\RefundService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;

class RefundProratedAction extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        $amounts = 0;
        $models->each(function($model) use (&$amounts) {
            $service = app(RefundService::class);
            $service->setSubscription($model->id);
            $amounts += $service->prorated();
        });


        return ActionResponse::message('Refunded: $' . number_format($amounts / 100, 2));
    }

    public function fields(NovaRequest $request): array
    {
        return [];
    }
}
