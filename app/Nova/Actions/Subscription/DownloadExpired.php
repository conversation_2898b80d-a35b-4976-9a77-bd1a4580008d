<?php

namespace App\Nova\Actions\Subscription;

use App\Export;
use App\Exports\ExpiredSubscriptionExport;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Http\Requests\NovaRequest;
use Maatwebsite\Excel\Facades\Excel;

class DownloadExpired extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields, Collection $models)
    {
//        $exportService = app()->make(ExportService::class);

        $fileName = 'Expired_subscriptions';
        $fileExtension = '.csv';
        $filePath = "public/exports/{$fileName}{$fileExtension}";

        $baseName = $fileName;
        $extension = $fileExtension;

        $version = 0;
        $name = 'Expired subscriptions';
        $dbName = $name;

        while (Storage::exists($filePath)) {
            $version++;
            $filePath = "public/exports/{$baseName} ({$version}){$extension}";
            $dbName = "{$name} ({$version})";
        }

        Excel::store(new ExpiredSubscriptionExport($fields->from, $fields->to), $filePath);


        $export = Export::create([
            'name' => $dbName,
            'file_path' => $filePath
        ]);

        // $export = $exportService->createVersionedExport('Expired subscriptions', $filePath, ExpiredSubscriptionExport::class);

        $publicPath = str_replace('public/', 'storage/', data_get($export, 'file_path'));

        $finalFileName = basename(data_get($export, 'file_path'));

        return Action::download(url($publicPath), $finalFileName);
    }


    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Date::make('From')->rules('required'),
            Date::make('To')->rules('required'),
        ];
    }
}
