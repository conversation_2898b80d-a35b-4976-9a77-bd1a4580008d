<?php

namespace App\Nova\Actions\Subscription;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Date;
use Laravel\Nova\Http\Requests\NovaRequest;

class DownloadNotes extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        return Action::openInNewTab('/nova-api/download-subscription-notes/' . $fields->from . '/' . $fields->to);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Date::make('From')->rules('required'),
            Date::make('To')->rules('required'),
        ];
    }
}
