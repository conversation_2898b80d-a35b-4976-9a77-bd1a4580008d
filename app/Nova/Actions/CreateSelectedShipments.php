<?php

namespace App\Nova\Actions;

use App\Http\Controllers\SubscriptionController;
use App\Subscription;
use App\SubscriptionMonth;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\ActionFields;

class CreateSelectedShipments extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        // $this->chargeSubscriptions($models->pluck('id'));

        $models->each->createShipment(SubscriptionMonth::find($fields->month), $fields->mark_as_shipped);
//        return;
//
//        $subscriptions->each(function ($subscription) use ($month, $fields) {
//            $namedPublication =  \App\NamedPublication::create([
//                'subscription_id' => $subscription->id,
//                'subscription_month_id' => $month->id,
//                'name' => $month->name,
//                // 'amount' => $subscription->order->amount, // Get amount from the related order
//                'subscription_type' => $subscription->subscription_type
//            ]);
//
//            $name = str_contains($subscription->name,' ')
//                ? $subscription->name
//                :  'Family '. $subscription->name;
//
//            $addressData = array_merge(
//                $subscription->getVerifiedAddress(),
//                [
//                    'name' =>  $name ,
//                    'phone' =>  $subscription->customer->phone,
//                    'residential' => false,
//                ]
//            );
//
//            $config = \App\ShipStationConfiguration::find(1);
//
//            $shipStationOrderData = [
//                'orderNumber' => $namedPublication->id,
//                'orderDate' => $namedPublication->created_at->toIso8601String(),
//                'orderStatus' => 'awaiting_shipment',
//                'billTo' => $addressData,
//                'shipTo' => $addressData,
//
//                "weight" => [
//                    // "value" => "8.0",
//                    "value" => "28.0",
//                    "units" => "ounces",
//                ],
//            ];
//            if($addressData['country'] != 'US') {
//                $shipStationOrderData["internationalOptions"] = [
//                    'contents' => 'merchandise',
//                    "customsItems" => [[
//                        // 'description' => 'Book',
//                        'description' => 'Speakers',
//                        'quantity' => 1,
//                        // 'value' => 20,
//                        'value' => 50,
//                        // 'harmonizedTariffCode' => 4901,
//                        'harmonizedTariffCode' => 851821,
//                        'countryOfOrigin' => "US",
//                    ]],
//                    'nonDelivery' => 'return_to_sender'
//                ];
//                $shipStationOrderData["serviceCode"] = $config->service_code_intl;
//            }
//
//            $result = \App\Services\ShipStationOrderService::createOrder($shipStationOrderData);
//
//            $namedPublication->update([
//                'order_id' => $result['orderId'] ?? null,
//            ]);
//
//            $addressDataShipFrom = [
//                'name' => 'Vinderkind',
//                'street1' => '1303 53rd street',
//                'street2' => '1303 53rd street',
//                'city' => 'brooklyn',
//                'state' => 'NY',
//                'postalCode' => '11219',
//                'country' => 'US',
//                'phone' => '3473519597',
//                'residential' => true,
//            ];
//
//            $labelData = [
//                "orderId" => $namedPublication->order_id,
//                "carrierCode" => $config->carrier_code,
//                "serviceCode" => $addressData['country'] != 'US'
//                    ?  $config->service_code
//                    : $config->service_code_intl,
//                "confirmation" => "delivery",
//                "shipDate" => today()->toDateString(),
//                "packageCode" => $config->package_code,
//                "weight" => [
//                    "value" => $config->weight_value,
//                    "units" => $config->weight_units
//                ],
//                "testLabel" => env('SHIPSTATION_TEST'),
//                'shipFrom' => $addressDataShipFrom,
//                'shipTo' => $addressData
//            ];
//
//            $labelData = [
//                "orderId" => $result['orderId'],
//                "carrierCode" => $config->carrier_code,
//                "serviceCode" => $config->service_code,
//                "confirmation" => "delivery",
//                "shipDate" => today()->addDay(),
//                "packageCode" => $config->package_code,
//                "weight" => [
//                    "value" => $config->weight_value,
//                    "units" => $config->weight_units
//                ],
//                "testLabel" => env('SHIPSTATION_TEST')
//            ];
//
//            if($addressData['country'] != 'US') {
//                $labelData["internationalOptions"] = [
//                    'contents' => 'merchandise',
//                    "customsItems" => [[
//                        'description' => 'Book',
//                        'quantity' => 1,
//                        'value' => 20,
//                        'harmonizedTariffCode' => 4901,
//                        'countryOfOrigin' => "US",
//                    ]],
//                    'nonDelivery' => 'return_to_sender'
//                ];
//                $labelData["serviceCode"] = $config->service_code_intl;
//                $labelData["carrierCode"] = $config->carrier_code;
//            }
//
//            // Create label for the order
//            $labelResult =  resolve(\App\Services\CreateLabelForOrderService::class)->createLabelForOrder($labelData, 1, $namedPublication->id);
//
//            $trackingData = [
//                'user_name' => $subscription->name,
//                'tracking_name' => $labelResult['trackingNumber'],
//                'issue' => $nextPublicationMonth->name,
//                'tracking_url' => 'https://tools.usps.com/go/TrackConfirmAction_input?strOrigTrackNum=' . $labelResult['trackingNumber'],
//            ];
//            (new \App\Emails\TrackingInformation)
//                 ->withData($trackingData)
//                 ->sendTo($customer);
//
//            $subscription->incrementPublicationCount();
//            $subscription->DecreaseIssuesCount();
//        });

    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        SubscriptionMonth::getNext();
        SubscriptionMonth::getCurrent();

        return [
            Select::make('Month')
                ->options(SubscriptionMonth::pluck('name', 'id'))
                ->required()
                ->withMeta(['value' => 
                    hebDateArray()['day'] >= setting()->getValue('hebrew_day_of_month_to_create_shipments')
                        ? SubscriptionMonth::getNext()->id
                        : SubscriptionMonth::getCurrent()->id
                ]),
            
            Boolean::make('Mark As Shipped')
            // ->withMeta(['value' => false])
            // ->nullable()
            ,
        ];
    }

    public function chargeSubscriptions($ids)
    {
        $subscriptions = Subscription::where('issues', 0)
            ->whereIn('id', $ids)
            ->where('renew', true)
            ->pluck('id');

        $controller = app(SubscriptionController::class);

        $subscriptions->each(function ($id) use ($controller) {
            $controller->renew($id);
        });
    }
}
