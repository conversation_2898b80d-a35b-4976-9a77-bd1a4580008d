<?php

namespace App\Nova\Actions;

use App\Gift;
use App\Zone;
use Illuminate\Bus\Queueable;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Actions\Action;
use Illuminate\Support\Collection;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Http\Requests\NovaRequest;
use Illuminate\Queue\InteractsWithQueue;

class GiftReport extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        return Action::openInNewTab("/nova-api/download-gift-report?zone_id=$fields->zone_id&gift_id=$fields->gift_id&delivered=$fields->delivered");
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $zone = Zone::find(request()->resourceId);

        return [
            // Select::make('Range')->options([
            //     'weekly' => 'Weekly',
            //     'custom' => 'Custom',
            // ])->default('weekly'),

            // DependencyContainer::make([
            //     Select::make('Start')
            //         ->options(optional($zone)->previousDates())
            //         ->default(today()->startOfWeek()->toDateString()),
            // ])->dependsOn('range', 'weekly'),

            // DependencyContainer::make([
            //     Date::make('Start')->rules('required'),
            //     Date::make('End')->rules('required'),
            // ])->dependsOn('range', 'custom'),

            Hidden::make('Zone', 'zone_id')->withMeta([
                'value' => optional($zone)->id
            ]),
            Select::make('Gift', 'gift_id')
                ->rules('required')
                ->options(Gift::pluck('name', 'id')),

            Boolean::make('Mark As Delivered', 'delivered')
                ->rules('required')
                ->default(true)
        ];
    }
}
