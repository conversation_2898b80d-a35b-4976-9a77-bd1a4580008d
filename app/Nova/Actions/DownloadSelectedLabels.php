<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Boolean;

class DownloadSelectedLabels extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        $files = $models->pluck('label_path');
        $pdf = new \setasign\Fpdi\Fpdi;

        foreach ($files as $file) {
            $pdf->AddPage('P', [101.6, 152.4]);
            $pdf->setSourceFile($file);
            $tplId = $pdf->importPage(1);
            $pdf->useTemplate($tplId, 0, 0, 101.6, 152.4);
        }

        $fileName = time();

        $pdfFilePath = storage_path("app/public/" . $fileName . ".pdf");

        $pdf->Output('F', $pdfFilePath);

        if ($fields->ship_now) {
            $models->each->update(['shipped_at' => now()]);
        }

        return Action::download(url('storage/' . $fileName . '.pdf'), $fileName . '.pdf');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Boolean::make('Mark As Shipped', 'ship_now')
                ->withMeta(['value' => true]),
        ];
    }
}
