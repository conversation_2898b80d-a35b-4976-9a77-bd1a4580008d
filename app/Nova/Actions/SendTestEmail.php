<?php

namespace App\Nova\Actions;

use App\Http\Controllers\EmailsController;
use Bashy\CampaignMonitor\Facades\CampaignMonitor;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class SendTestEmail extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $campaign = EmailsController::getCampaign($models->first()->id);

        CampaignMonitor::Campaigns($campaign['id'])
            ->send_preview(array(
                $fields->email,
            ), '<EMAIL>');

        return;
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Text::make('Email')
        ];
    }
}
