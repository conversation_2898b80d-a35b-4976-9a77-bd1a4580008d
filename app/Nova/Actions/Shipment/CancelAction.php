<?php

namespace App\Nova\Actions\Shipment;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class CancelAction extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Shipment Cancel';

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        $errors = [];

        foreach ($models as $model) {
            if($model->tracking_number){
                $errors[] = $model->id;
                continue;
            }

            if(!$model->canceled_at) {
                $model->canceled_at = now();
                $model->save();
            }

            $order = $model->order;
            $shipmentsByOrder = $order->shipments;

            if(!$shipmentsByOrder->whereNull('canceled_at')->count()){
                $order->update(['status' => 'canceled']);
            }
        }

        if(count($errors)){
            $errorMessage = implode(', ', $errors);
            return ActionResponse::danger("Shipping tracking number already exists: $errorMessage");
        }

        return ActionResponse::message('Shipping has been cancelled!');
    }

    public function fields(NovaRequest $request)
    {
        return [];
    }
}
