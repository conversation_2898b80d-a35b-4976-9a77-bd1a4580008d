<?php

namespace App\Nova\Actions\Shipment;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class SetShippingStatus extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each->update([
            'shipped_at' => $fields->shipped ? now() : null,
        ]);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Shipped')
                ->options([
                    true => 'Mark As Shipped',
                    false => 'Mark As Not Shipped',
                ])
                ->required()
        ];
    }
}
