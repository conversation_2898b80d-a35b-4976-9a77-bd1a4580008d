<?php

namespace App\Nova\Actions;

use App\Zone;
use Capitalc\Checkbox\Checkbox;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class GenerateRoute4me extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        Zone::find($fields->zone)->createRoute4Me($fields->shipped, $fields->product);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $products = \Lunar\Models\ProductVariant::with('product')
        ->find(
            \App\Shipment::whereNull('shipped_at')
                ->distinct()->get('purchasable_id')
        )
        ->mapWithKeys(function ($variant) {
            return [
                $variant->id => $variant->product->translateAttribute('name'),
            ];
        });
        return [
            Select::make('Zone')
                ->options(Zone::pluck('name', 'id'))
                ->required(),

            Select::make('Product')
            	->nullable()
                ->options($products),


            Boolean::make('Mark as Shipped', 'shipped'),

//            Checkbox::make('Mark As Sent', 'send')->default(1),

        ];
    }
}
