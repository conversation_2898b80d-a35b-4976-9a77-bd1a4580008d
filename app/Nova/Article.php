<?php

namespace App\Nova;

use Capitalc\Checkbox\Checkbox;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Froala\NovaFroalaField\Froala;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use NovaAttachMany\AttachMany;

class Article extends Resource
{
    /**
     * Indicates if the resource should be displayed in the sidebar.
     *
     * @var bool
     */
    public static $displayInNavigation = false;
    public static $group = 'Articles';
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Article::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Title')->required(),

            Text::make('Caption')->hideFromIndex(),

            Images::make('Images')
                ->singleImageRules('nullable'),
            Froala::make('Content')
                ->options([
                    'key' => env('FROALA_KEY'),
                    'pastePlain' => true,
                    'direction' => 'rtl',
                    'defaultTextAlignment' => 'justify',
                    'useClasses' => false,
                    'quickInsertTags' => [],
                    'toolbarButtons' => [
                        ['bold', 'italic', 'underline'],
                        ['alignRight', 'alignCenter', 'alignLeft', 'alignJustify'],
                        ['insertHR'],
                        ['clearFormatting'],
                        ['html'],
                    ],
                ]),
            // Text::make('Seo Title'),
            // Textarea::make('Seo Description'),
            Number::make('Read Time'),

            Number::make('Views')
                ->readonly(),
            Checkbox::make('Active')->withMeta(['value' => $this->active ?? true])->nullable(),
            Checkbox::make('Featured')->nullable(),
            DateTime::make('Publish Date'),
            BelongsTo::make('User', 'author'),
            BelongsTo::make('Issue'),
            BelongsTo::make('Section'),
            BelongsTo::make('Column')->nullable(),

            AttachMany::make('Categories'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
