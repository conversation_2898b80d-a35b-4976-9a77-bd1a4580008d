<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use NovaAttachMany\AttachMany;

class Cycle extends Resource
{
    /**
     * Indicates if the resource should be displayed in the sidebar.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    public static $group = 'Configurations';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Cycle';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
    ];

    /**
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->rules('required', 'unique:cycles,name,{{resourceId}}')->detailLink(),

            Select::make('Cycle', 'months')->options([
                '1' => '1 month cycle',
                '12' => '12 month cycle',
            ])->default('1')->displayUsingLabels()->rules('required'),

            //Number::make('Amount of Publications', 'publications')->rules('required'),

            Boolean::make('Active')
                ->withMeta(['value' => $this->active ?? 1]),

            Boolean::make('Hide Auto Renew'),
            AttachMany::make('Gifts')->height('250px'),
            BelongsToMany::make('Gifts'),
        ];
    }


    /**
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}