<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\Text;
use App\SubscriptionMonth as SubscriptionsMonth;
use Laravel\Nova\Fields\HasMany;

class SubscriptionMonth extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = SubscriptionsMonth::class;

    public static $group = 'Settings';
    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Text::make('Name')->rules('required'),

            // Select::make('Status')
            //     ->options(function () use ($request) {
            //         $options = [
            //             'current' => 'Current',
            //             'next' => 'Next',
            //         ];
            //         if ($request->isUpdateOrUpdateAttachedRequest()) {
            //             $options['none'] = 'None';
            //         }
            //         return $options;
            //     })
            //     ->rules('required')
            //     ->displayUsingLabels()
            //     ->withMeta(['value' => $this->status ?? ''])
            //     ->fillUsing(function ($request, $model, $attribute) {
            //         $model->is_current = $request[$attribute] === 'current';
            //         $model->is_next = $request[$attribute] === 'next';
            //         if ($request[$attribute] === 'none') {
            //             $model->is_current = false;
            //             $model->is_next = false;
            //         }
            //     }),

            Text::make('Description')->nullable(),

            Text::make('Total Publications', function () {
                return \App\Shipment::where('subscription_month_id', $this->id)
                    ->count();
            }),

            Text::make('Total Monthly Publications', function () {
                return \App\Shipment::where([
                    ['subscription_month_id', '=', $this->id],
                    ['name', '=', 'monthly']
                ])->count();
            })->onlyOnDetail(),

            Text::make('Total Yearly Publications', function () {
                return \App\Shipment::where([
                    ['subscription_month_id', '=', $this->id],
                    ['name', '=', 'yearly']
                ])->count();
            })->onlyOnDetail(),

            Text::make('Total Earnings', function () {
                // Calculate Total Monthly Earnings
                $totalMonthlyEarnings = \App\Shipment::where([
                    ['subscription_month_id', '=', $this->id],
                    ['name', '=', 'monthly']
                ])->sum('amount');

                // Calculate Total Yearly Earnings (Monthly Equivalent)
                $totalYearlyEarnings = \App\Shipment::where([
                    ['subscription_month_id', '=', $this->id],
                    ['name', '=', 'yearly']
                ])->sum('amount');
                $totalYearlyEarningsMonthlyEquivalent = round($totalYearlyEarnings / 12, 2);

                return '$' . number_format($totalMonthlyEarnings + $totalYearlyEarningsMonthlyEquivalent, 2);
            }),


            Text::make('Total Monthly Earnings', function () {
                return '$' . \App\Shipment::where([
                        ['subscription_month_id', '=', $this->id],
                        ['name', '=', 'monthly']
                    ])->sum('amount');
            })->onlyOnDetail(),

            Text::make('Total Yearly Earnings (Monthly Equivalent)', function () {
                $total = \App\Shipment::where([
                    ['subscription_month_id', '=', $this->id],
                    ['name', '=', 'yearly']
                ])->sum('amount');

                return '$' . round($total / 12, 2);
            })->onlyOnDetail(),

            Text::make('Total Shipment Cost', function () {
                return '$' . number_format(\App\Shipment::where('subscription_month_id', $this->id)
                        ->sum('shipment_cost'), 2);
            }),

            HasMany::make('Subscriptions associated with this publication', 'Shipment', PublicationEarningsReport::class),
        ];
    }

    public function actions(Request $request)
    {
        return [
            new Actions\GenerateMoreMonths,
        ];
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

}
