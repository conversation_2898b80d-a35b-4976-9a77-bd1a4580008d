<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Hidden;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use Lunar\Facades\Payments;
use Wds\StripeCardField\StripeCardField;

class CreditCard extends Resource
{
    public static $displayInNavigation = false;
    public static $clickAction = 'ignore';
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\CreditCard::class;

    public function authorizedToUpdate(Request $request): bool
    {
        return false;
    }

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    public function title()
    {
        return ucfirst($this->type) . ' ending in ' . $this->last_four . " ({$this->customer?->id} {$this->customer?->name})";
    }
    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'last_four'
    ];


    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            BelongsTo::make('Customer')->searchable(),


            StripeCardField::make('Credit Card')
                ->fillUsing(function ($request, $model, $attribute, $requestAttribute) {
                    if ($request->editMode == 'create') {
                        if ($request->exists($requestAttribute)) {
                            $req = $request[$requestAttribute];

                            $customer = \App\Customer::find($request->customer);

                            $paymentDriver = Payments::driver('stripe');

                            $responseInitialize = $paymentDriver->attachCard(
                                model: $customer,
                                payment: $req
                            );

                            foreach ($responseInitialize as $field => $value) {
                                $model->{$field} = $value;
                            }
                        }
                    }
                })
                ->onlyOnForms()
                ->showOnCreating(function(){ return config('lunar.payments.default') === 'stripe'; })
                ->showOnUpdating(function(){ return $this->resource->payment_method === 'stripe'; })
                ->creationRules(function(){ return config('lunar.payments.default') === 'stripe' ? ['required']: []; })
                ->updateRules(function(){ return $this->resource->payment_method === 'stripe' ? ['required'] : []; }),

            Text::make('Card Number', 'last_four')
                ->rules('required')
                ->hideWhenUpdating()
                ->onlyOnForms()
                ->showOnCreating(function(){ return config('lunar.payments.default') === 'usaepay'; })
                ->creationRules(function(){ return config('lunar.payments.default') === 'usaepay' ? ['required']: []; }),

            Select::make('Type')->options([
                'visa' => 'Visa',
                'mastercard' => 'Mastercard',
                'amex' => 'Amex',
                'discover' => 'Discover',
            ])
                ->showOnCreating(function(){ return config('lunar.payments.default') === 'usaepay'; })
                ->showOnUpdating(function(){ return $this->resource->payment_method === 'usaepay'; })
                ->creationRules(function(){ return config('lunar.payments.default') === 'usaepay' ? ['required']: []; })
                ->updateRules(function(){ return $this->resource->payment_method === 'usaepay' ? ['required'] : []; }),

            Text::make('Last Four')->hideWhenCreating()
                ->showOnUpdating(function(){ return $this->resource->payment_method === 'usaepay'; })
                ->updateRules(function(){ return $this->resource->payment_method === 'usaepay' ? ['required'] : []; }),
            Text::make('Expires')
                ->help('4 digits only, ex: 0124, not 01/2024')
                ->showOnCreating(function(){ return config('lunar.payments.default') === 'usaepay'; })
                ->showOnUpdating(function(){ return $this->resource->payment_method === 'usaepay'; })
                ->creationRules(function(){ return config('lunar.payments.default') === 'usaepay' ? ['required']: []; })
                ->updateRules(function(){ return $this->resource->payment_method === 'usaepay' ? ['required'] : []; }),
            Text::make('Security Code')
                ->showOnCreating(function(){ return config('lunar.payments.default') === 'usaepay'; })
                ->showOnUpdating(function(){ return $this->resource->payment_method === 'usaepay'; })
                ->creationRules(function(){ return config('lunar.payments.default') === 'usaepay' ? ['required']: []; })
                ->updateRules(function(){ return $this->resource->payment_method === 'usaepay' ? ['required'] : []; }),
            Text::make('Zip')
                ->showOnCreating(function(){ return config('lunar.payments.default') === 'usaepay'; })
                ->showOnUpdating(function(){ return $this->resource->payment_method === 'usaepay'; })
                ->creationRules(function(){ return config('lunar.payments.default') === 'usaepay' ? ['required']: []; })
                ->updateRules(function(){ return $this->resource->payment_method === 'usaepay' ? ['required'] : []; }),



            Select::make('Type')
                ->options([
                    'visa' => 'Visa',
                    'mastercard' => 'Mastercard',
                    'amex' => 'Amex',
                    'discover' => 'Discover',
                ])
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Text::make('Last Four')
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Text::make('Expires')
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Text::make('Security Code')
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Text::make('Zip')
                ->hideWhenUpdating()
                ->hideWhenCreating(),


            Hidden::make('Custom')->hideWhenUpdating()->onlyOnForms()->default(true),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }

    public static function afterCreate($request, $model): void
    {
        if(config('lunar.payments.default') == 'usaepay'){
            $paymentDriver = Payments::driver('usaepay');
            $token = $paymentDriver->getToken([
                'cardNumber' => preg_replace('/[^0-9]/', '', $model->last_four),
                'exp' => preg_replace('/[^0-9]/', '', $model->expires),
                'cvc' => $model->security_code,
                'zip' => $model->zip,
            ], $model->customer, false);
            $model->token = $token['token'];
            $model->last_four = $token['last_four'];
        }

        $model->payment_method = config('lunar.payments.default');
        $model->save();
    }
}
