<?php

namespace App\Nova;

use App\Nova\Actions\SendEmailNow;
use App\Nova\Actions\SendTestEmail;
use App\Nova\Actions\UnscheduleCampaign;
use <PERSON>vel\Nova\Panel;
use Capitalc\Hidden\Hidden;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Date;
use Capitalc\Subject\Subject;
use Laravel\Nova\Fields\Badge;
use Laravel\Nova\Fields\Number;
use Capitalc\Checkbox\Checkbox;
use Laravel\Nova\Fields\DateTime;
use Capitalc\Iframe\Iframe;
use Froala\NovaFroalaField\Froala;
use <PERSON><PERSON><PERSON>\DuplicateField\DuplicateField;
use Whitecube\NovaFlexibleContent\Flexible;
use Capitalc\HiddenWithSpace\HiddenWithSpace;
use Ebess\AdvancedNovaMediaLibrary\Fields\Media;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Alexwenzel\DependencyContainer\DependencyContainer;
use Capitalc\AdvancedNovaMediaLibrary\Fields\Images as ParentPicture;
use <PERSON>vel\Nova\Fields\Boolean;

class Campaign extends Resource
{
    public static $trafficCop = false;

    public static $model = 'App\Campaign';

    public static $group = 'Campaigns';

    public static $title = 'nova';

    public static $search = [
        'number',
        'subject',
    ];

    public function fields(NovaRequest $request)
    {
        return [
            //dont query all campaigns for index page
            Number::make('Campaign ID', 'number')
                ->sortable()
                ->detailLink()
                ->exceptOnForms(),

            Number::make('Campaign ID', 'number')
                ->onlyOnForms()
                ->withMeta(["value" => $this->number ?? \App\Campaign::max('number') + 1])
                ->rules('required', 'numeric', 'unique:campaigns,number,{{resourceId}}'),

            Date::make('Date')
                ->onlyOnForms()
                ->help('Use to generate a subject line.')
                ->rules('nullable'),

            new Panel('Content', [
                Flexible::make('Components')
                    ->addLayout('Header Logo', 'header-logo', [
                        HiddenWithSpace::make('This will display the Logo'),
                    ])
                    ->addLayout('Header Title', 'header-title', [
                        Text::make('Title'),
                    ])
                    ->addLayout('Image', 'image', [
                        ParentPicture::make('Images')
                            ->onlyOnForms()
                            // ->croppingConfigs(['ratio' => 4 / 3])
                            ->withmeta(['extraAttributes' => [
                                'model' => $this->resource,
                                'multiple' => false,
                            ]]),
                    ])
                    ->addLayout('Image With Description', 'image-with-description', [
                        ParentPicture::make('Images')
                            ->onlyOnForms()
                            // ->croppingConfigs(['ratio' => 4 / 3])
                            ->withmeta(['extraAttributes' => [
                                'model' => $this->resource,
                                'multiple' => false,
                            ]]),
                        Text::make('Title'),
                        Froala::make('Description')
                            ->options(config('froala')),
                    ])
                    ->addLayout('די וואך', 'dee-voch', [
                        ParentPicture::make('Images')
                            ->onlyOnForms()
                            ->withmeta(['extraAttributes' => [
                                'model' => $this->resource,
                                'multiple' => false,
                            ]]),
                        Text::make('Title')
                            ->readonly()
                            ->withMeta([
                                'value' => 'די וואך',
                            ]),
                        Hidden::make('image-hook')
                            ->withMeta([
                                'value' => 'dee-voch',
                            ]),
                        Froala::make('Description')
                            ->options(config('froala')),
                    ])
                    ->addLayout('שריפט', 'shrift', [
                        ParentPicture::make('Images')
                            ->onlyOnForms()
                            ->withmeta(['extraAttributes' => [
                                'model' => $this->resource,
                                'multiple' => false,
                            ]]),
                        Text::make('Title')
                            ->readonly()
                            ->withMeta([
                                'value' => 'שריפט',
                            ]),
                        Hidden::make('image-hook')
                            ->withMeta([
                                'value' => 'shrift',
                            ]),
                        Froala::make('Description')
                            ->options(config('froala')),
                    ])
                    ->addLayout('קינדער שריפט', 'kinder-shrift', [
                        ParentPicture::make('Images')
                            ->onlyOnForms()
                            ->withmeta(['extraAttributes' => [
                                'model' => $this->resource,
                                'multiple' => false,
                            ]]),
                        Text::make('Title')
                            ->readonly()
                            ->withMeta([
                                'value' => 'קינדער שריפט',
                            ]),
                        Hidden::make('image-hook')
                            ->withMeta([
                                'value' => 'kinder-shrift',
                            ]),
                        Froala::make('Description')
                            ->options(config('froala')),
                    ])
                    ->addLayout('Image With Button', 'image-with-button', [
                        ParentPicture::make('Images')
                            ->onlyOnForms()
                            // ->croppingConfigs(['ratio' => 4 / 3])
                            ->withmeta(['extraAttributes' => [
                                'model' => $this->resource,
                                'multiple' => false,
                            ]]),
                        Text::make('Title'),
                        Text::make('Link'),
                        Text::make('Button Text'),
                    ])
                    ->addLayout('Text', 'text', [
                        Text::make('Title'),
                    ])->onlyOnForms()
            ]),
            Subject::make('Subject')
                ->rules('required', 'nullable'),

            Checkbox::make('Schedule', 'toggle_schedule')
                ->onlyOnForms(),

            DependencyContainer::make([
                DateTime::make('Send On', 'schedule')
                    ->rules('after:now', 'nullable'),
            ])->dependsOn('toggle_schedule', true),
            Hidden::make('sent')
                ->hideFromIndex(),
            Number::make('opens')
                ->exceptOnForms(),
            Number::make('clicks')
                ->exceptOnForms(),

            Boolean::make('Use Images For Cover', 'use_images')
                ->default(true)
                ->hideFromIndex(),

            Badge::make('Status')->types([
                'Draft' => 'bg-gray-500',
                'Scheduled' => 'bg-orange-400',
                'Sent' => 'bg-green-400',
            ]),

            Iframe::make('Page')->withMeta([
                'url' => rtrim(env('APP_URL'), '/') . optional($this->resource)->path
            ])->onlyOnDetail(),

            DuplicateField::make('Duplicate')
                ->withMeta([
                    'resource' => 'campaigns',
                    'model' => 'App\Campaign',
                    'id' => $this->id,
                    'relations' => []
                ]),


            new Panel('images', [
                Images::make('images')
                    ->customPropertiesFields([
                        Text::make('Image Title'),
                    ])
                    ->onlyOnForms()
                    // ->croppingConfigs(['ratio' => 4 / 3])
                    ->thumbnail('thumb')
                    ->singleImageRules('max:10240|mimes:jpeg,jpg,png,gif')
                    ->conversionOnView('thumb'),

                Images::make('banner')
                    ->onlyOnForms()
                    // ->croppingConfigs(['ratio' => 4 / 3])
                    ->thumbnail('thumb')
                    ->singleImageRules('max:10240')
                    ->conversionOnView('thumb'),

                Media::make('clips')
                    ->onlyOnForms()
                    ->conversionOnView('thumb'),
            ]),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            new SendTestEmail,
            new SendEmailNow,
            (new UnscheduleCampaign)
                ->canSee(function () {
                    return optional($this->resource)->toggle_schedule;
                })->canRun(function () {
                    return true;
                }),
            (new Actions\UpdateCampaign)
                ->canSee(function () {
                    return optional($this->resource)->id
                        ? $this->resource->sent
                        : true;
                })
                ->canRun(function () {
                    return true;
                })
                ->showOnTableRow()
        ];
    }
}
