<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;

class PublicationEarningsReport extends Resource
{

    public static $group = 'Other';
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\NamedPublication';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public static function availableForNavigation(Request $request)
    {
        return false;
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            ID::make(__('Subscription id'), 'subscription_id'),
            Text::make('Name'),

            // Displaying the address_line_1 from the Subscription model
            Text::make('Address Line 1', function () {
                return optional($this->subscription)->address_line_1;
            }),

            Text::make('Cycle', function () {
                return $this->subscription_type === 'monthly' ? 1 : 12;
            }),
            Text::make('Amount', 'amount'),

            Text::make('Amount Per Cycle', function () {
                $cycle = $this->subscription_type === 'monthly' ? 1 : 12;
                $amount = $this->amount ?? 0;
                return round($amount / $cycle, 2);
            }),
        ];
    }


    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
