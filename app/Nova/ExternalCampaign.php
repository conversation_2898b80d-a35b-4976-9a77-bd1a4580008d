<?php

namespace App\Nova;

use Capitalc\AdvancedNovaMediaLibrary\Fields\Images as ParentPicture;
use Capitalc\Checkbox\Checkbox;
use Capitalc\Iframe\Iframe;
use <PERSON><PERSON><PERSON>\DependencyContainer\DependencyContainer;
use Froala\NovaFroalaField\Froala;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Whitecube\NovaFlexibleContent\Flexible;

class ExternalCampaign extends Resource
{
    public static $group = 'External Campaigns';
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\ExternalCampaign::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Subject')
                ->rules('required', 'nullable'),

            Checkbox::make('Schedule', 'toggle_schedule')
                ->onlyOnForms(),

            DependencyContainer::make([
                DateTime::make('Send On', 'schedule')
                    ->rules('after:now', 'nullable'),
            ])->dependsOn('toggle_schedule', true),

            Hidden::make('Sent')
                ->hideFromIndex(),
            Number::make('Opens')
                ->exceptOnForms(),
            Number::make('Clicks')
                ->exceptOnForms(),
            Iframe::make('Page')->withMeta([
                'url' => rtrim(env('APP_URL'), '/') . optional($this->resource)->path
            ])->onlyOnDetail(),
            BelongsTo::make('Company'),
            ...$this->content(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }

    public function content()
    {
        return [
            Flexible::make('Components')
                ->addLayout('Header Title', 'header-title', [
                    Text::make('Title'),
                ])
                ->addLayout('Image', 'image', [
                    ParentPicture::make('Images')
                        ->onlyOnForms()
                        // ->croppingConfigs(['ratio' => 4 / 3])
                        ->withmeta(['extraAttributes' => [
                            'model' => $this->resource,
                            'multiple' => false,
                        ]]),
                ])
                ->addLayout('Image With Description', 'image-with-description', [
                    ParentPicture::make('Images')
                        ->onlyOnForms()
                        // ->croppingConfigs(['ratio' => 4 / 3])
                        ->withmeta(['extraAttributes' => [
                            'model' => $this->resource,
                            'multiple' => false,
                        ]]),
                    Text::make('Title'),
                    Froala::make('Description')
                        ->options(config('froala')),
                ])
                ->addLayout('Image With Button', 'image-with-button', [
                    ParentPicture::make('Images')
                        ->onlyOnForms()
                        // ->croppingConfigs(['ratio' => 4 / 3])
                        ->withmeta(['extraAttributes' => [
                            'model' => $this->resource,
                            'multiple' => false,
                        ]]),
                    Text::make('Title'),
                    Text::make('Link'),
                    Text::make('Button Text'),
                ])
                ->addLayout('Text', 'text', [
                    Text::make('Title'),
                ])->onlyOnForms()
        ];
    }
}
