<?php

namespace App\Nova\SubResources\Order;

use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Controllers\AttachableController;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class OrderDigital extends OrderProducts
{
    /**
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {

        return [
            ID::make()->sortable(),
            Text::make('Name', 'product.attribute_data.name'),
            Text::make('Length', 'length_value'),
            Text::make('Width', 'width_value'),
            Text::make('Height', 'height_value'),
            Text::make('Weight', 'weight_value'),
        ];
    }

    /**
     * @param NovaRequest $request
     * @param $query
     * @return Builder
     */
    public static function relatableQuery(NovaRequest $request, $query): Builder
    {
        if ($request->route()->getController() instanceof AttachableController) {
            return $query->where(function ($builder) use ($query, $request) {
                    $builder->orWhere('shippable', 0);
            });
        }

        return parent::relatableQuery($request, $query);
    }
}
