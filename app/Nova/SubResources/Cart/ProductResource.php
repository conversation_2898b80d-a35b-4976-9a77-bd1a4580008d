<?php

namespace App\Nova\SubResources\Cart;

use App\Nova\Resource;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Image;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Controllers\AttachableController;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class ProductResource extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\ProductVariant>
     */
    public static $model = \App\ProductVariant::class;

    public static $displayInNavigation = false;


    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'product.attribute_data.name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Image::make('Images')
                ->preview(function () {
                    return $this->resource->product->img;
                })
                ->thumbnail(function () {
                    return $this->resource->product->img;
                })
            ,
            Text::make('Name', 'product.attribute_data.name'),
            Text::make('Length', 'length_value'),
            Text::make('Width', 'width_value'),
            Text::make('Height', 'height_value'),
            Text::make('Weight', 'weight_value'),

            Text::make('Quantity', 'quantity')
                ->displayUsing(function(){
                    return isset($this->pivot) ? $this->pivot->quantity : '-';
                }),
        ];
    }

    /**
     * @param NovaRequest $request
     * @param $query
     * @return Builder
     */
    public static function relatableQuery(NovaRequest $request, $query): Builder
    {
        if ($request->route()->getController() instanceof AttachableController) {
            return $query->where(function ($builder) {
                $builder->orWhere('shippable', 1);
            });
        }

        return parent::relatableQuery($request, $query);
    }

    /**
     * Get the cards available for the request.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
