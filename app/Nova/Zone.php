<?php

namespace App\Nova;

use App\Nova\Actions\ExportProviderList;
use Capitalc\Checkbox\Checkbox;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\FormData;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\KeyValue;
use Laravel\Nova\Fields\BelongsTo;

class Zone extends Resource
{
    /**
     * Indicates if the resource should be displayed in the sidebar.
     *
     * @var bool
     */
    // public static $displayInNavigation = true;

    public static $group = 'Configurations';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Zone';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'zips'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->rules('required'),

            // Textarea::make('Zips'),

            KeyValue::make('Zip Codes')
                ->rules('required')
                ->actionText('Add Zip Code')
                ->keyLabel('Zip Code')
                ->valueLabel('City, State'),

            BelongsTo::make('Provider')
                ->nullable()->viewable(false),

            Boolean::make('Free shipping', 'free_shipping'),
            Number::make('Min total free shipping', 'free_shipping_price')
                ->hide()
                ->dependsOn(
                    ['free_shipping'],
                    function (Text $field, NovaRequest $request, FormData $formData) {
                        $field->{$formData->free_shipping ? 'show' : 'hide'}();
                    }
                ),

            Text::make('Start Address')->hideFromIndex(),
            Text::make('Verified Start Address', 'meta.start_address.address')->readonly()->hideFromIndex(),
            Text::make('End Address')->hideFromIndex(),
            Text::make('Verified End Address', 'meta.end_address.address')->readonly()->hideFromIndex(),

            HasMany::make('Exports'),

            HasMany::make('Routes', 'subscriptionRoutes', '\App\Nova\SubscriptionRoute'),

            Checkbox::make('Route 4 Me'),
            Number::make('Percentage', 'percentage')->min(0)->step(0.01)->max(100)
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            new ExportProviderList,

            (new Actions\MonthlyReport)
                ->onlyOnDetail(),

            /*  (new Actions\GiftReport)
                 ->onlyOnDetail(),

             (new Actions\GiftNoteReport)
                 ->onlyOnDetail(),

             (new Actions\CreateRoute)
                 ->onlyOnDetail(), */
        ];
    }
}
