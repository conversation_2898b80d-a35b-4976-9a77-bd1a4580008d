<?php

namespace App\Nova\Metrics;

use App\Order;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Trend;

class SalesByMonth extends Trend
{
    public function calculate(NovaRequest $request)
    {
        return $this->sumByMonths($request, Order::class, 'amount');
    }

    public function ranges()
    {
        return [
            12 => __('12 Months'),
            24 => __('24 Months'),
        ];
    }

    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    public function uriKey()
    {
        return 'sales-by-month';
    }
}
