<?php

namespace App\Nova\Metrics;

use App\Subscription;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Trend;

class Subscriptions extends Trend
{
    public function calculate(NovaRequest $request)
    {
        return $this->countByDays($request, Subscription::class);
    }

    public function ranges()
    {
        return [
            30 => __('30 Days'),
            60 => __('60 Days'),
            90 => __('90 Days'),
        ];
    }

    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    public function uriKey()
    {
        return 'subscriptions';
    }
}
