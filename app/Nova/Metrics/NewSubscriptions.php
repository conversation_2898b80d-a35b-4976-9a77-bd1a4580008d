<?php

namespace App\Nova\Metrics;

use App\Subscription;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;

class NewSubscriptions extends Value
{
    public function calculate(NovaRequest $request)
    {
        return $this->count($request, Subscription::whereDoesntHave('shipments'));
    }

    public function ranges()
    {
        return [
            30 => __('30 Days'),
            60 => __('60 Days'),
            365 => __('365 Days'),
            'TODAY' => __('Today'),
            'MTD' => __('Month To Date'),
            'QTD' => __('Quarter To Date'),
            'YTD' => __('Year To Date'),
        ];
    }

    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    public function uriKey()
    {
        return 'new-subscriptions';
    }
}
