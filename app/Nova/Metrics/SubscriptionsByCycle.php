<?php

namespace App\Nova\Metrics;

use App\Cycle;
use App\Subscription;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class SubscriptionsByCycle extends Partition
{
    public function calculate(NovaRequest $request)
    {
        $cycles = Cycle::all();

        $results = $this->count($request, Subscription::class, 'cycle_id')
            ->label(function ($value) use ($cycles) {
                return optional($cycles->find($value))->name;
            });

        //sort by most 
        arsort($results->value);

        return $results;
    }

    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'subscriptions-by-cycle';
    }
}
