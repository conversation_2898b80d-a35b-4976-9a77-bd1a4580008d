<?php

namespace App\Nova\Metrics;

use App\Subscription;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;
use <PERSON>vel\Nova\Metrics\Partition;

class ActiveSubscriptions extends Partition
{
    public function calculate(NovaRequest $request)
    {
        return $this->result([
            'Active' => \App\Subscription::active()->notpaused()->count(),
            'Paused' => \App\Subscription::active()->paused()->count(),
        ]);
    }

    public function uriKey()
    {
        return 'active-subscriptions';
    }
}
