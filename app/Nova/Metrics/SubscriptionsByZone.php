<?php

namespace App\Nova\Metrics;

use App\Subscription;
use App\Zone;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class SubscriptionsByZone extends Partition
{
    public function calculate(NovaRequest $request)
    {
        $zones = Zone::get(['name', 'id']);

        $results = Subscription::active()
            ->where('zone_id', '>', 0)
            ->pluck('zone_id')
            ->groupBy(function ($subscription) {
                return $subscription;
            })->map(function ($subscriptions) use ($zones) {
                return [
                    'label' => optional($zones->find($subscriptions->first()))->name,
                    'value' => $subscriptions->count(),
                ];
            })
            ->sortByDesc('value')
            ->values();

        // $zones = \App\Zone::all();

        // $results = $this->count($request, \App\Subscription::class, 'zone_id')
        //     ->label(function ($value) use ($zones) {
        //         return optional($zones->find($value))->name;
        //     });

        //sort by most 
        // arsort($results->value);

        return ['value' => $results];
    }

    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    public function uriKey()
    {
        return 'subscriptions-by-zone';
    }
}
