<?php

namespace App\Nova\Metrics;

use App\Subscription;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;

class ExpiringSubscription extends Value
{
    public $icon = 'calendar';


    public function calculate(NovaRequest $request)
    {
        return $this->count($request, Subscription::query()
            ->where('renew', 0)
            ->where('subscription_type', 'yearly')
            ->where('canceled', 0)
            ->where('issues', 1));
    }

    public function ranges()
    {
        return [
            30 => __('30 Days'),
            60 => __('60 Days'),
            365 => __('365 Days'),
            'TODAY' => __('Today'),
            'MTD' => __('Month To Date'),
            'QTD' => __('Quarter To Date'),
            'YTD' => __('Year To Date'),
        ];
    }

    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }
    public function uriKey()
    {
        return 'expiring-subscriptions';
    }
}
