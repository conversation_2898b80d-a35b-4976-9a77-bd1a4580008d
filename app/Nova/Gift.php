<?php

namespace App\Nova;

use <PERSON><PERSON><PERSON>\DependencyContainer\DependencyContainer;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON>vel\Nova\Fields\Textarea;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Number;
use Whitecube\NovaFlexibleContent\Flexible;

class Gift extends Resource
{
    /**
     * Indicates if the resource should be displayed in the sidebar.
     *
     * @var bool
     */
    // public static $displayInNavigation = false;

    public static $group = 'Configurations';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Gift';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->detailLink(),
            Currency::make('Price'),

            Flexible::make('Fields')
                ->addLayout('Field', 'field', [
                    Text::make('Title'),
                    Text::make('Placeholder'),
                    Boolean::make('Required'),
                    Boolean::make('Show Hebrew Keyboard', 'hebrew'),
                    Boolean::make('Active'),
                ])->button('Add Field'),
            Textarea::make('Message'),

            BelongsToMany::make('Cycles'),

            Boolean::make('Active')
                ->withMeta(['value' => $this->active ?? 1]),

            Images::make('Picture'),

            Boolean::make('Extended Subscription Gift', 'extension'),
            DependencyContainer::make([
                Number::make('Weeks To Extend', 'weeks')
            ])->dependsOn('extension', true),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
