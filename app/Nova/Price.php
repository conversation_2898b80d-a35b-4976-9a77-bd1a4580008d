<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Price as PriceModel;
use <PERSON><PERSON>\Nova\Fields\Text;

class Price extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = PriceModel::class;
    public static $displayInNavigation = false;

    public static $group = 'Settings';

    public static function label()
    {
        return 'Price';
    }

    public static function authorizedToCreate(Request $request)
    {
        // get the count of each type in the DB
        $yearlyCount = PriceModel::where('type', 'yearly')->count();
        $monthlyCount = PriceModel::where('type', 'monthly')->count();

        // if we have both types in the DB, return false
        if ($yearlyCount > 0 && $monthlyCount > 0) {
            return false;
        }

        return parent::authorizedToCreate($request);
    }

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $existingTypes = PriceModel::pluck('type')->toArray();

        $types = ['yearly', 'monthly'];
        $newTypes = array_diff($types, $existingTypes);

        $fields = [
            ID::make(__('ID'), 'id')->sortable(),
            Number::make('Amount')
                ->rules('required')
                ->min(1)
                ->step(0.01)
                ->sortable(),
        ];

        if (count($newTypes) == 0) {
            $fields[] = Text::make('Type')->withMeta(['extraAttributes' => [
                'readonly' => true
            ]])->default('Both types already exist. You cannot add more.')
                ->rules('required');
        } else if (count($newTypes) == 1) {
            $fields[] = Text::make('Type')->withMeta(['extraAttributes' => [
                'readonly' => true
            ]])->default(array_values($newTypes)[0])
                ->rules('required');
        } else {
            $fields[] = Select::make('Type')
                ->options([
                    'yearly' => 'Yearly',
                    'monthly' => 'Monthly',
                ])->rules('required');
        }
        return $fields;
    }


    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
