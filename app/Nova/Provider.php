<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Select;
use Outl1ne\NovaSimpleRepeatable\SimpleRepeatable;

class Provider extends Resource
{
    /**
     * Indicates if the resource should be displayed in the sidebar.
     *
     * @var bool
     */
    public static $displayInNavigation = true;

    public static $group = 'Configurations';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Provider';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'email',
        'additional_emails'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('required')->detailLink(),

            Text::make('Email')
                ->rules('email', 'required'),

            SimpleRepeatable::make('Additional Emails', 'additional_emails', [
                Text::make('Email')
                    ->rules('email', 'required')
            ]),

            Select::make('Combine Address', 'combine')
                ->rules('required')
                ->options(\App\Provider::$combine_options)
                ->displayUsingLabels(),

            Boolean::make('Include Phone Number In Export', 'include_phone')
                ->rules('required')
                ->help('This uses the phone number from the customer and not from the recipient'),

            Boolean::make('Only send changes In Export', 'only_changes'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            // new \App\Nova\Actions\ExportProviderList,
        ];
    }
}
