<?php

namespace App\Nova\Lenses;

use App\Nova\Filters\AddressVerified;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\LensRequest;
use Laravel\Nova\Lenses\Lens;

class AddressInformation extends Lens
{
    public static function query(LensRequest $request, $query)
    {
        return $request->withOrdering($request->withFilters(
            $query
        ));
    }

    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Boolean::make('Verified Address'),
            BelongsTo::make('Zone'),
            Text::make('Original Address', 'meta.original'),
            Text::make('Verified Address', 'meta.address'),
            // Text::make('Longtitute', 'meta.lng'),
            // Text::make('Latitute', 'meta.lat'),
        ];
    }

    public function cards(Request $request)
    {
        return [];
    }

    public function filters(Request $request)
    {
        return [
            new AddressVerified
        ];
    }

    public function actions(Request $request)
    {
        return parent::actions($request);
    }

    public function uriKey()
    {
        return 'address-information';
    }
}
