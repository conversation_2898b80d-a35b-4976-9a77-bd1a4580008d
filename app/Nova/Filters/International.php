<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;

class International extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        if ($value == 'national') {
            return $query->whereIn('country', ['US', null]);
        }
        if ($value == 'international') {
            return $query->whereNotNull('country')->orWhere('country', '!=', 'US');
        }
        return $query;
    }

    public function options(NovaRequest $request)
    {
        return [
            'National' => 'national',
            'International' => 'international',
        ];
    }
}
