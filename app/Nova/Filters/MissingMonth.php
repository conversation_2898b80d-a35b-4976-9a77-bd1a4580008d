<?php

namespace App\Nova\Filters;

use App\SubscriptionMonth;
use <PERSON>vel\Nova\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class MissingMonth extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->whereDoesntHave('shipments', function (Builder $query) use ($value) {
            $query->where('subscription_month_id', $value);
        });
    }

    public function options(NovaRequest $request)
    {
        return SubscriptionMonth::all()->pluck('id', 'name');
    }
}
