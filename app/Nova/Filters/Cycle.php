<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Cycle extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('cycle_id', $value);;
    }

    public function options(NovaRequest $request)
    {
        return \App\Cycle::all()->pluck('id', 'name');
    }
}
