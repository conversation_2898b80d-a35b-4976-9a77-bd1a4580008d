<?php

namespace App\Nova\Filters;

use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Status extends Filter
{
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        switch ($value) {
            case 'canceled':
                return $query->where('canceled', true);
            case 'paused':
                return $query->active()
                    ->paused();
            case 'upcoming':
                return $query->active()->where('start', '>', today());
            case 'inactive':
                return $query->inActive();
            case 'expired':
                return $query->expired();
            case 'active':
                return $query
                    ->active()
                    ->notpaused()
                    ;
            case 'expiring':
                return $query
                    ->where('renew', 0)
                    ->where('subscription_type', 'yearly')
                    ->where('canceled', 0)
                    ->where('issues', 1);

            default:
                return $query;
        }
    }

    public function options(NovaRequest $request)
    {
        // if($this->isPaused()) return 'Paused';
        // if($this->start > today()) return 'Upcoming';
        // if($this->finish < today()) return 'Inactive';
        // return 'Active';
        return [
            'Canceled' => 'canceled',
            'Paused' => 'paused',
            'Upcoming' => 'upcoming',
            'Inactive' => 'inactive',
            'Active' => 'active',
            'No Issues Left' => 'expired',
            'Expiring' => 'expiring'
        ];
    }
}
