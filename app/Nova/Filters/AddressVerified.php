<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class AddressVerified extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        if ($value) {
            return $query->where('verified_address', true);
        }
        return $query->where(function ($query) {
            return $query->where('verified_address', null)->orWhere('verified_address', false);
        });
    }

    public function options(NovaRequest $request)
    {
        return [
            'Verified' => 1,
            'Not Verified' => 0,
        ];
    }
}
