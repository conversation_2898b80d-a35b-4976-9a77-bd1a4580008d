<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;

class Finished extends Filter
{
    public $name = 'Active';
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('finish', !!$value ? '<=' : '>=', today());
    }

    public function options(NovaRequest $request)
    {
        return [
            'Active' => 0,
            'Inactive' => 1,
        ];
    }
}
