<?php

namespace App\Nova\Filters;

use <PERSON><PERSON>\Nova\Filters\BooleanFilter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class OrderResource extends BooleanFilter
{
    protected array $opt = [
        'Administrator' => 'admin',
        'Client' => 'client',
        'Auto-renew' => 'auto-renew',
    ];

    public function apply(NovaRequest $request, $query, $value)
    {
        $search = [];
        foreach($this->opt as $o){
            if($value[$o]){
                if($o == 'auto-renew'){
                    $search[] = $o;
                    $search[] = 'auto-renew-admin';
                    $search[] = 'auto-renew-client';
                    continue;
                }
                $search[] = $o;
            }
        }

        return $query->when(count($search), function ($query) use ($search) {
            $query->whereIn('resource', $search);
        });
    }

    public function options(NovaRequest $request): array
    {
        return $this->opt;
    }
}
