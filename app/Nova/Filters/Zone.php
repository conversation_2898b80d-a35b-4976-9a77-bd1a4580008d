<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;

class Zone extends Filter
{
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('zone_id', $value);
    }

    public function options(NovaRequest $request)
    {
        return \App\Zone::all()->pluck('id', 'name');
    }
}
