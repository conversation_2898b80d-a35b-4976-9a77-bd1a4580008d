<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;

class AutoRenew extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('renew', !!$value);
    }

    public function options(NovaRequest $request)
    {
        return [
            'Yes' => 1,
            'No' => 0,
        ];
    }
}
