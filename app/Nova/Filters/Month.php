<?php

namespace App\Nova\Filters;

use App\SubscriptionMonth;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Month extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('subscription_month_id', $value);;
    }

    public function options(NovaRequest $request)
    {
        return SubscriptionMonth::all()->pluck('id', 'name');
    }
}
