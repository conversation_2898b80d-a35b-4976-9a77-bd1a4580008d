<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;

class ShippingStatus extends Filter
{
    public $component = 'select-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        if ($value) {
            return $query->whereNotNull('shipped_at');
        }
        return $query->where('shipped_at', null);
    }

    public function options(NovaRequest $request)
    {
        return [
            'Not Shipped' => 0,
            'Shipped' => 1,
        ];
    }
}
