<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Promo;

class Promos extends Filter
{
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->whereHas('orders', function ($query) use ($value) {
            $query->where('promo_id', $value);
        });
    }

    public function options(NovaRequest $request)
    {
        return Promo::all()->pluck('id', 'name')->toArray();
    }
}