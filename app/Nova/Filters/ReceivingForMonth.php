<?php

namespace App\Nova\Filters;

use App\SubscriptionMonth;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ReceivingForMonth extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * The displayable name of the filter.
     *
     * @var string
     */
    public $name = 'Receiving For Month';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query
			->active()
			->where('subscription_month_id', '<=', $value)
			->where(function ($query) use ($value) {
				$query->whereNull('meta->skip')
					->orWhereJsonDoesntContain('meta->skip', (int) $value);
			});
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request)
    {
        return SubscriptionMonth::orderBy('year')
            ->orderBy('month')
            ->pluck('id', 'name');
    }
}
