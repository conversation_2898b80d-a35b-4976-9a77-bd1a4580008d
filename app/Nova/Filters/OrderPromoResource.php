<?php

namespace App\Nova\Filters;

use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Lunar\Models\Discount;

class OrderPromoResource extends Filter
{
    public const FILTER_ANY_PROMO_CODE = 'any_promocode';
    public const FILTER_NO_PROMO_CODE = 'no_promocode';

    public $name = "Promocodes";
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param NovaRequest $request
     * @param  Builder  $query
     * @param  mixed  $value
     * @return Builder
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        if(in_array($value, [self::FILTER_ANY_PROMO_CODE, self::FILTER_NO_PROMO_CODE])) {
            return $query->when($value === self::FILTER_ANY_PROMO_CODE, function (Builder $query) {
                $query->whereRaw('JSON_LENGTH(discount_breakdown) > 0');
            })->when($value === self::FILTER_NO_PROMO_CODE, function (Builder $query) {
                $query->whereRaw('JSON_LENGTH(discount_breakdown) = 0');
            });
        }

        return $query->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(discount_breakdown, '$[0].discount_id')) = ?", [$value]);
    }

    /**
     * Get the filter's available options.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function options(NovaRequest $request): array
    {
        $entries = Discount::query()->whereNotNull('coupon')->pluck( 'id', 'coupon');

        $const = [
            'Any promocode' => self::FILTER_ANY_PROMO_CODE,
            'No promocode' => self::FILTER_NO_PROMO_CODE
        ];

        return $entries->toArray() + $const;
    }
}
