<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\KeyValue;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Badge;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Illuminate\Database\Eloquent\Model;
use Lunar\FieldTypes\Text as LunarText;
use App\Product as CustomProduct;

class Cycle extends Resource
{
    public static $model = CustomProduct::class;

    public static $group = 'Store';

    public static $showPollingToggle = true;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';
    // public static $globallySearchable = false;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'attribute_data->name->value',
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->where('product_type_id', CustomProduct::$productTypes['cycles']);
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name'),
            Text::make('Price')->onlyOnIndex(),
            Text::make('Description')->onlyOnIndex(),

            Select::make('Status')
                ->default('published')
                ->options([
                    'published' => 'Published',
                    'draft' => 'Draft',
                ])
                ->onlyOnForms(),

            Badge::make('Status')->map([
                'draft' => 'danger',
                'published' => 'success',
            ]),

            // \Ebess\AdvancedNovaMediaLibrary\Fields\Images::make('Images'),
            // \Ebess\AdvancedNovaMediaLibrary\Fields\Files::make('PDF'),
            // \Ebess\AdvancedNovaMediaLibrary\Fields\Files::make('MP3'),

            KeyValue::make('Details', 'variantJson')
                ->disableEditingKeys()
                ->disableAddingRows()
                ->disableDeletingRows()
                ->resolveUsing(function () {
                    return [
                        'Price' => null,
                        'Length' => null,
                        'Width' => null,
                        'Height' => null,
                        'Weight' => null,
                    ];
                })
                ->onlyOnForms()
                ->hideWhenUpdating(),

            KeyValue::make('Details', 'variantJson')
                ->disableEditingKeys()
                ->disableAddingRows()
                ->disableDeletingRows()
                ->resolveUsing(function () {
                    $data = $this->variantJson;
                    return [
                        'Price' => $this->price,
                        'Length' => $data['length'],
                        'Width' => $data['width'],
                        'Height' => $data['height'],
                        'Weight' => $data['weight'],
                    ];
                })
                ->hideWhenCreating(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function afterCreate(NovaRequest $request, Model $model)
    {
        $data = json_decode($request->variantJson, true);

        $variant = $model->variants()->create([
            'tax_class_id' => 1,

            'length_value' => $data['Length'],
            'length_unit' => 'in',

            'width_value' => $data['Width'],
            'width_unit' => 'in',

            'height_value' => $data['Height'],
            'height_unit' => 'in',

            'weight_value' => $data['Weight'],
            'weight_unit' => 'oz',

            'sku' => 'cycle_' . $model->id,
        ]);

        $variant->prices()->create([
            'price' => $data['Price'] * 100,
            'currency_id' => 1
        ]);

        $model->update([
            'attribute_data' => collect([
                'name' => new LunarText($request->name),
            ]),
        ]);
    }

    public static function afterUpdate(NovaRequest $request, Model $model)
    {
        $data = json_decode($request->variantJson, true);

        $variant = $model->variants()->first();

        $variant->update([
            'length_value' => $data['Length'],

            'width_value' => $data['Width'],

            'height_value' => $data['Height'],

            'weight_value' => $data['Weight'],
        ]);

        $variant->prices->first()->update([
            'price' => $data['Price'] * 100,
        ]);

        $model->update([
            'attribute_data' => collect([
                'name' => new LunarText($request->name),
            ]),
        ]);
    }

    public static function usesScout()
    {
        return false;
    }
}
