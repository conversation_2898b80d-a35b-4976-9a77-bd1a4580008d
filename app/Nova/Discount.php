<?php

namespace App\Nova;

use Carbon\Carbon;
use Formfeed\DependablePanel\DependablePanel;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Laravel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\MorphedByMany;
use <PERSON>vel\Nova\Fields\MorphToMany;
use Laravel\Nova\Fields\MultiSelect;
use Laravel\Nova\Fields\Slug;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Panel;
use App\Lunar\DiscountTypes\AmountOff;
use App\Lunar\DiscountTypes\BuyXGetY;
use Lunar\Models\Channel;

class Discount extends Resource
{
    public static $group = 'Configurations';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Lunar\Discount::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'coupon',
    ];

    public function fieldsForIndex(): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Name'),
            Text::make('Coupon', 'coupon'),
            Select::make('Type')
                ->options([
                    AmountOff::class => 'AmountOff',
                    BuyXGetY::class => 'BuyXGetY',
                ])
                ->resolveUsing(function ($value) {
                    switch ($value) {
                        case AmountOff::class:
                            return 'AmountOff';
                        case BuyXGetY::class:
                            return 'BuyXGetY';
                        default:
                            return null;
                    }
                }),
            Boolean::make('Stop', 'stop'),
            Boolean::make('Enabled'),

            ...$this->morphedFields()
        ];
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Name')->rules('required'),
            Slug::make('Handle')
                ->from('Name')
                ->separator('_')
                ->rules('required')
                ->onlyOnForms(),

            DateTime::make('Start', 'starts_at')->rules('required'),
            DateTime::make('End', 'ends_at')->nullable(),
            Select::make('Type')->rules('required')
                ->fullWidth()
                ->options([
                    AmountOff::class => 'AmountOff',
                    BuyXGetY::class => 'BuyXGetY',
                ])->fillUsing(function ($request, $model) {
                    $model->type = $request->type;
                    $model->data = self::updateDataField($request);
                }),
            Boolean::make('Enabled'),
            Boolean::make('Stop', 'stop')
                ->fullWidth()
                ->help(
                    'Stop other discounts applying after this one'
                ),

            Panel::make('Conditions', [
                Text::make('Coupon', 'coupon')
                    ->rules('required', Rule::unique('lunar_discounts', 'coupon')->ignore($this->id))
                    ->fillUsing(function ($request, $model) {
                        $model->coupon = Str::upper($request->coupon);
                        $data = $model->data;
                        $data['coupon'] = Str::upper($request->coupon);
                        $model->data = $data;
                    }),
                Number::make('Max uses', 'max_uses')->onlyOnForms(),
                Number::make('Max uses per user', 'max_uses')->onlyOnForms(),
                Number::make('Minimum cart amount', 'data->min_prices')
                    ->onlyOnForms()
                    ->resolveUsing(function ($model) {
                        return isset($model['USD']) ? $model['USD'] / 100 : 0;
                    })
                    ->fillUsing(function ($request, $model) {
                        $model->data = self::updateDataField($request);
                    }),
            ]),

            DependablePanel::make('BuyXGetY', [
                Number::make('Min qty', 'data->min_qty')
                    ->onlyOnForms()
                    ->fillUsing(function ($request, $model) {
                        $model->data = self::updateDataField($request);
                    }),
                Number::make('Reward qty', 'data->reward_qty')
                    ->onlyOnForms()
                    ->fillUsing(function ($request, $model) {
                        $model->data = self::updateDataField($request);
                    }),
                Number::make('Max reward qty', 'data->max_reward_qty')
                    ->onlyOnForms()
                    ->nullable()
                    ->fillUsing(function ($request, $model) {
                        $model->data = self::updateDataField($request);
                    }),

                MultiSelect::make('Product conditions', 'purchasableConditions')
                    ->options(function () {
                        return \Lunar\Models\Product::all()->pluck('attribute_data.name', 'id')->toArray();
                    })
                    ->onlyOnForms()
                    ->resolveUsing(function () {
                        return optional($this->resource)->purchasableConditions->filter(function ($item) {
                            return $item->purchasable_type == \Lunar\Models\Product::class;
                        })->pluck('purchasable_id')->toArray();
                    })
                    ->fillUsing(function () {
                        //
                    }),
                MultiSelect::make('Product rewards', 'purchasableRewards')
                    ->options(function () {
                        return \Lunar\Models\Product::all()->pluck('attribute_data.name', 'id')->toArray();
                    })
                    ->onlyOnForms()
                    ->resolveUsing(function () {
                        return optional($this->resource)->purchasableRewards->filter(function ($item) {
                            return $item->purchasable_type == \Lunar\Models\Product::class;
                        })->pluck('purchasable_id')->toArray();
                    })
                    ->fillUsing(function () {
                        //
                    })
            ])
                ->onlyOnForms()
                ->singleRequest(true)
                ->separatePanel(true)
                ->dependsOn(["type"], function ($panel, $request, $formData) {
                    $panel->{$formData->type === BuyXGetY::class ? 'show' : 'hide'}();
                }),
            DependablePanel::make('Amount Off', [
                Boolean::make('Fixed value', 'data->fixed_value')
                    ->onlyOnForms()
                    ->nullable()
                    ->fillUsing(function ($request, $model) {
                        $model->data = self::updateDataField($request);
                    }),
                Number::make('Fixed values', 'data->fixed_values')
                    ->onlyOnForms()
                    ->step(0.01)
                    ->nullable()
                    ->resolveUsing(function ($fixedValue) {
                        return $fixedValue['USD'] ?? 0;
                    })
                    ->fillUsing(function ($request, $model) {
                        $model->data = self::updateDataField($request);
                    })
                    ->dependsOn(["data->fixed_value"], function ($field, $request, $formData) {
                        $field->{$formData['data->fixed_value'] ? 'show' : 'hide'}();
                    }),
                Number::make('Percentage', 'data->percentage')
                    ->onlyOnForms()
                    ->step(1)
                    ->nullable()
                    ->resolveUsing(function ($percentage) {
                        return $percentage ?? 0;
                    })
                    ->fillUsing(function ($request, $model) {
                        $model->data = self::updateDataField($request);
                    })
                    ->dependsOn(["data->fixed_value"], function ($field, $request, $formData) {
                        $field->{!$formData['data->fixed_value'] ? 'show' : 'hide'}();
                    }),

                MultiSelect::make('Product limitation', 'purchasableLimitations')
                    ->options(function () {
                        return \Lunar\Models\Product::all()->pluck('attribute_data.name', 'id')->toArray();
                    })
                    ->onlyOnForms()
                    ->resolveUsing(function () {
                        return optional($this->resource)->purchasableLimitations->filter(function ($item) {
                            return $item->purchasable_type == \Lunar\Models\Product::class;
                        })->pluck('purchasable_id')->toArray();
                    })
                    ->fillUsing(function () {
                        //
                    }),
                Boolean::make('Enabled exclusion', 'exclusionamountoff')
                    ->dependsOn([], function ($field, $request, $formData) {
                        $field->value = $formData['exclusionamountoff'] ?? false;
                    })
                    ->fillUsing(function () {
                        //
                    }),
                MultiSelect::make('Product exclusions', 'purchasableExclusions')
                    ->options(function () {
                        return \Lunar\Models\Product::all()->pluck('attribute_data.name', 'id')->toArray();
                    })
                    ->onlyOnForms()
                    ->resolveUsing(function () {
                        return optional($this->resource)->purchasableExclusions->filter(function ($item) {
                            return $item->purchasable_type == \Lunar\Models\Product::class;
                        })->pluck('purchasable_id')->toArray();
                    })
                    ->fillUsing(fillCallback: function () {
                    })
                    ->dependsOn(["exclusionamountoff"], function ($field, $request, $formData) {
                        $field->{$formData['exclusionamountoff'] ? 'show' : 'hide'}();
                    }),
            ])
                ->onlyOnForms()
                ->singleRequest(true)
                ->separatePanel(true)
                ->dependsOn(["type"], function ($panel, $request, $formData) {
                    $panel->{$formData->type === AmountOff::class ? 'show' : 'hide'}();
                }),


            ...$this->morphedFields()

        ];
    }

    private function morphedFields(): array
    {
        return [
            MorphToMany::make('Channels')
                ->fields(function () {
                    return [
                        Boolean::make('Enabled'),
                        DateTime::make('Start', 'starts_at')->nullable()
                            ->resolveUsing(function ($value) {
                                return $value ? Carbon::parse($value) : null;
                            }),
                        DateTime::make('End', 'ends_at')->nullable()
                            ->resolveUsing(function ($value) {
                                return $value ? Carbon::parse($value) : null;
                            }),
                    ];
                }),

            MorphedByMany::make('Product', 'purchasableProducts', 'App\Nova\DiscountProduct')
                ->allowDuplicateRelations()
                ->fields(function () {
                    return [
                        Select::make('Type', 'type')->options($this->selectPurchasable()),
                    ];
                }),
            MorphedByMany::make('Product Variant', 'purchasableProductVariants', 'App\Nova\DiscountProductVariant')
                ->allowDuplicateRelations()
                ->fields(function () {
                    return [
                        Select::make('Type', 'type')->options($this->selectPurchasable()),
                    ];
                }),
        ];
    }

    public function selectPurchasable(): array
    {
        if ($this->resource && $this->resource->type === AmountOff::class) {
            return [
                'limitation' => 'Limitation',
                'exclusion' => 'Exclusion',
            ];
        }

        if ($this->resource && $this->resource->type === BuyXGetY::class) {
            return [
                'condition' => 'Condition',
                'reward' => 'Reward',
            ];
        }

        return [];
    }

    private static function updateDataField($request): array
    {
        $data = [
            'min_prices' => $request['data->min_prices'] * 100,
            'coupon' =>  Str::upper($request->coupon),
        ];

        if ($request->type == AmountOff::class) {
            $data['fixed_value'] = (bool)$request['data->fixed_value'];

            if ($request['data->fixed_value']) {
                $data['fixed_values'] = [
                    'USD' => $request['data->fixed_values'],
                ];
            } else {
                $data['percentage'] = $request['data->percentage'];
            }
        }

        if ($request->type == BuyXGetY::class) {
            $data['min_qty'] = (int)$request['data->min_qty'] ?? null;
            $data['reward_qty'] = (int)($request['data->reward_qty'] ?? 1);
            $data['minmax_reward_qty_qty'] = (int)$request['data->max_reward_qty'] ?? null;
        }

        return $data;
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            Actions\Discount\ImportAction::make()->standalone(),
        ];
    }

    public static function afterCreate(Request $request, $model): void
    {
        self::afterCreateOrUpdate($request, $model);
    }

    public static function afterUpdate(Request $request, $model): void
    {
        self::afterCreateOrUpdate($request, $model);
    }

    private static function afterCreateOrUpdate($request, $model): void
    {
        $model->purchasableConditions()->delete();
        $model->purchasableRewards()->delete();
        $model->purchasableLimitations()->delete();
        $model->purchasableExclusions()->delete();


        switch ($model->type) {
            case AmountOff::class :
                //purchasableLimitations   purchasableExclusions

                if ($request->has('purchasableLimitations')) {
                    $products = \Lunar\Models\Product::query()
                        ->with('variants')
                        ->whereIn('id', json_decode($request->input('purchasableLimitations', '[]'), true))
                        ->get();
                    $items = [];

                    foreach ($products as $product) {
                        $items[] = [
                            'purchasable_type' => \Lunar\Models\Product::class,
                            'purchasable_id' => $product->id,
                            'type' => 'limitation',
                        ];

                        foreach ($product->variants as $variant) {
                            $items[] = [
                                'purchasable_type' => \Lunar\Models\ProductVariant::class,
                                'purchasable_id' => $variant->id,
                                'type' => 'limitation',
                            ];
                        }
                    }
                    $model->purchasableLimitations()->createMany($items);
                }

                if ($request->has('purchasableExclusions') && $request->has('exclusionamountoff')) {
                    $products = \Lunar\Models\Product::query()
                        ->with('variants')
                        ->whereIn('id', json_decode($request->input('purchasableExclusions', '[]'), true) ?? [])
                        ->get();
                    $items = [];

                    foreach ($products as $product) {
                        $items[] = [
                            'purchasable_type' => \Lunar\Models\Product::class,
                            'purchasable_id' => $product->id,
                            'type' => 'exclusion',
                        ];

                        foreach ($product->variants as $variant) {
                            $items[] = [
                                'purchasable_type' => \Lunar\Models\ProductVariant::class,
                                'purchasable_id' => $variant->id,
                                'type' => 'exclusion',
                            ];
                        }
                    }
                    $model->purchasableExclusions()->createMany($items);
                }
                break;
            case BuyXGetY::class :
                //purchasableConditions   purchasableRewards
                if ($request->has('purchasableConditions')) {
                    $products = \Lunar\Models\Product::query()
                        ->with('variants')
                        ->whereIn('id', json_decode($request->input('purchasableConditions', '[]'), true) ?? [])
                        ->get();
                    $items = [];

                    foreach ($products as $product) {
                        $items[] = [
                            'purchasable_type' => \Lunar\Models\Product::class,
                            'purchasable_id' => $product->id,
                            'type' => 'condition',
                        ];

                        foreach ($product->variants as $variant) {
                            $items[] = [
                                'purchasable_type' => \Lunar\Models\ProductVariant::class,
                                'purchasable_id' => $variant->id,
                                'type' => 'condition',
                            ];
                        }
                    }
                    $model->purchasableConditions()->createMany($items);
                }
                if ($request->has('purchasableRewards')) {
                    $products = \Lunar\Models\Product::query()
                        ->with('variants')
                        ->whereIn('id', json_decode($request->input('purchasableRewards', '[]'), true) ?? [])
                        ->get();
                    $items = [];

                    foreach ($products as $product) {
                        $items[] = [
                            'purchasable_type' => \Lunar\Models\Product::class,
                            'purchasable_id' => $product->id,
                            'type' => 'reward',
                        ];

                        foreach ($product->variants as $variant) {
                            $items[] = [
                                'purchasable_type' => \Lunar\Models\ProductVariant::class,
                                'purchasable_id' => $variant->id,
                                'type' => 'reward',
                            ];
                        }
                    }
                    $model->purchasableRewards()->createMany($items);
                }
                break;

            default:
                break;
        }


        $channels = Channel::all()->mapWithKeys(function ($channel) use ($model) {
            return [
                $channel->id => [
                    'enabled' => true,
                    'starts_at' => null,
                    'ends_at' => null,
                ],
            ];
        });

        $model->channels()->sync($channels);

    }
}
