<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class NamedPublication extends Resource
{
    public static function label()
    {
        return 'Old Shipments';
    }

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\NamedPublication::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public static function indexQuery($request, $query)
    {
        // if there is no other filter set default it to unshipped
        $filtersAmount = collect(json_decode(base64_decode($request->filters)))->map(function ($item) {
            return collect($item)->values()->filter()->count();
        })->sum();

        if ($filtersAmount == 0) {
            return $query->where('shipped_at', null);
        }

        return $query;
    }

    public static function availableForNavigation(Request $request)
    {
        return true;
    }

    /**
     * Determine if the current user can create new resources.
     *
     * @param Request $request
     * @return bool
     */
    public static function authorizedToCreate(Request $request)
    {
        return false; // Disabling create functionality
    }

    public function fields(NovaRequest $request)
    {
        return [
            ID::make('ID')->sortable(),
            BelongsTo::make('Subscription'),
            BelongsTo::make('Zone'),
            Text::make('Address')->resolveUsing(function () {
                return data_get($this->subscription, 'meta.address') . ' ' . data_get($this->subscription_data, 'payment.last_four');
            })->onlyOnIndex(),
            Text::make('Name'),
            DateTime::make('Created At')->onlyOnIndex()->sortable(),
            DateTime::make('Shipped At')->onlyOnIndex()->sortable(),
            Text::make('ShipStation order id', 'order_id'),
            Text::make('Shipment id', 'shipment_id'),
            Text::make('Shipment Cost', 'shipment_cost'),
            Text::make('Tracking Number', 'tracking_number'),

            Text::make('Label', function () {
                if ($this->label_path) {
                    return "<a href='/{$this->label_path}' target='_blank'>Download Label</a>";
                }
                return "Label not generated";
            })->asHtml(),

            // Text::make('Publication cost', 'amount'),
            // Text::make('Subscription Type'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [
            new Filters\ShippingStatus,
            new Filters\Zone,
            new Filters\Month,
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            // new Actions\CreateLabel,
            new Actions\DownloadAllLabels,
            new Actions\DownloadSelectedLabels,
            new Actions\GenerateRoute4Me,
            // new Actions\GenerateLocalLabels,
            new Actions\GenerateAllLabels,
            new Actions\GenerateSelectedLabels,
            new Actions\Shipment\SetShippingStatus,

        ];
    }
}
