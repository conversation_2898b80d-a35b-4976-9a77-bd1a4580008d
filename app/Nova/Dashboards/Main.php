<?php

namespace App\Nova\Dashboards;

use App\Nova\Metrics\ActiveSubscriptions;
use App\Nova\Metrics\ExpiringSubscription;
use App\Nova\Metrics\NewSubscriptions;
use App\Nova\Metrics\UnverifiedAddresses;
use App\SubscriptionMonth;
use App\Zone;
use Coroowicaksono\ChartJsIntegration\DoughnutChart;
use Coroowicaksono\ChartJsIntegration\StackedChart;
use Laravel\Nova\Dashboards\Main as Dashboard;

class Main extends Dashboard
{
    /**
     * Get the cards that should be displayed on the Nova dashboard.
     *
     * @return array
     */
    public function cards()
    {
        return [
            new ActiveSubscriptions,
            new NewSubscriptions,
            (new DoughnutChart())
                ->title('Subscriptions By Cycle')
                ->model('\App\Subscription')
                ->series([
                    [
                        'label' => 'Yearly',
                        'filter' => [
                            'key' => 'subscription_type',
                            'value' => 'yearly'
                        ]
                    ],
                    [
                        'label' => 'Monthly',
                        'filter' => [
                            'key' => 'subscription_type',
                            'value' => 'monthly'
                        ]
                    ]
                ])
                ->options([
                    'btnRefresh' => true,
                    'latestData' => '*',
                    'queryFilter' => [
                        ['key' => 'canceled',
                            'operator' => 'IN',
                            'value' => [0, false, null],
                        ]
                    ],
                ])
                ->width('1/3')
            ,

            (new DoughnutChart())
                ->title('Subscriptions By Location')
                ->model('\App\Subscription')
                ->series(
                    Zone::all()
                        ->map(function ($zone) {
                            return [
                                'label' => $zone->name,
                                'filter' => [
                                    'key' => 'zone_id',
                                    'value' => $zone->id,
                                ],
                            ];
                        })->toArray()
                )
                ->options([
                    'btnRefresh' => true,
                    'latestData' => '*',
                    'queryFilter' => [
                        ['key' => 'canceled',
                            'operator' => 'IN',
                            'value' => [0, false, null],
                        ]
                    ],
                ])
                ->width('1/3'),

            new UnverifiedAddresses,
            new ExpiringSubscription,
            (new StackedChart())
                ->title('Shipstation')
                ->model('\App\NamedPublication')
                ->options([
                    'btnRefresh' => true,
                    'sum' => 'shipment_cost',
                    'showTotal' => false,
                    // 'uom' => 'week',
                    'latestData' => '*'
                ]),

            (new StackedChart())
                ->title('Monthly Sales')
                ->model('\App\Order')
                ->options([
                    'btnRefresh' => true,
                    'sum' => 'amount',
                    'showTotal' => false,
                    // 'uom' => 'week',
                    'latestData' => '*'
                ])
                ->series(array([
                    'label' => 'Monthly',
                    'filter' => [
                        'key' => 'amount',
                        'value' => '19.99'
                    ],
                ], [
                    'label' => 'Yearly',
                    'filter' => [
                        'key' => 'amount',
                        'value' => '199.00'
                    ],
                ])),

            (new StackedChart())
                ->title('Sales By Book')
                ->options([
                    'btnRefresh' => true,
                    'sum' => 'amount',
                    'showTotal' => true,
                    'xaxis' => [
                        'categories' => SubscriptionMonth::pastMonths()->pluck('name')
                    ],
                ])
                ->series(array([
                    'label' => 'Monthly',
                    'backgroundColor' => '#ffcc5c',
                    'data' => SubscriptionMonth::pastMonths()
                        ->with('named_publications')->get()->map(function ($month) {
                            return $month->named_publications()->where('subscription_type', 'monthly')->sum('amount');
                        })
                ], [
                    'label' => 'Yearly',
                    'backgroundColor' => '#91e8e1',
                    'data' => SubscriptionMonth::pastMonths()
                        ->with('named_publications')->get()->map(function ($month) {
                            return ceil($month->named_publications()->where('subscription_type', 'yearly')->sum('amount') / 12);
                        })
                ])),
        ];
    }
}
