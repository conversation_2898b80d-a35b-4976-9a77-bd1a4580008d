<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Illuminate\Support\Str;

class Setting extends Resource
{
    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public static $group = 'Settings';

    public static function label()
    {
        return 'Configurations';
    }

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Setting::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'key',
        'value'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return array_merge([
            Text::make('Name')
                ->withMeta(['extraAttributes' => [
                    'readonly' => true
                ]])
                ->rules('required'),

            Text::make('Key')
                ->withMeta(['extraAttributes' => [
                    'readonly' => true
                ]])
                ->rules('required'),

            Text::make('Value')
                ->resolveUsing(function ($value) {
                    return \Illuminate\Support\Str::limit($value, 75);
                })
                ->onlyOnindex(),
            Textarea::make('Value')->rules('required')->help($this->description)->alwaysShow(),
        ], $this->value());
    }

    public function value()
    {
        return $this->type ? $this->{$this->type}() : [];
    }

    public function text()
    {
        return [
            Text::make('Value')->help($this->description)
        ];
    }

    public function text_area()
    {
        return [
            Textarea::make('Value')->help($this->description)->alwaysShow(),
            Text::make('Value')->onlyOnIndex()->resolveUsing(function ($value) {
                return Str::limit($value, 75);
            })
        ];
    }

    public function number()
    {
        return [
            Number::make('Value')->help($this->description)
        ];
    }

    public function select()
    {
        return [
            Select::make('Value')->help($this->description)->options($this->options)->displayUsingLabels()
        ];
    }


    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
