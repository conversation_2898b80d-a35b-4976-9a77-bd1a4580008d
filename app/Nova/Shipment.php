<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Shipment extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Shipment>
     */
    public static $model = \App\Shipment::class;


    public static function _indexQuery($request, $query)
    {
        // if there is no other filter set default it to unshipped
        if (collect(json_decode(base64_decode($request->filters)))->pluck('value')->filter()->isEmpty()) {
            return $query->where('shipped_at', null);
        }
        return $query;
    }

    public static function authorizedToCreate(Request $request)
    {
        return false; // Disabling create functionality
    }

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public function fieldsForIndex(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make('Product', 'purchasable')
                ->displayUsing(function ($item) {
                    return $item->product->translateAttribute('name') ?? '-';
                }),
            Text::make('Customer', 'subscription.customer.name'),
            // Text::make('Name'),

            Text::make('Line 1', 'line_one'),
            Text::make('Line 2', 'line_two'),
            Text::make('City'),
            Text::make('State'),
            Text::make('Post code', 'postcode'),
            Number::make('Quantity'),
            BelongsTo::make('Zone'),
            Text::make('Tracking Number'),
            DateTime::make('Created At')->sortable(),
            DateTime::make('Shipped At')->sortable(),
            DateTime::make('Canceled At')->sortable(),
        ];
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make('Name'),
            Text::make('Product', 'purchasable')
                ->displayUsing(function ($item) {
                    return $item->product->translateAttribute('name') ?? '-';
                })
                ->resolveUsing(function ($item) {
                    return $item->product->translateAttribute('name') ?? '-';
                })
                ->readonly(),
            Text::make('Customer', 'subscription.customer.name')
                ->readonly(),
            Text::make('Address line 1', 'line_one'),
            Text::make('Address line 2', 'line_two'),
            Text::make('City'),
            Text::make('State'),
            Text::make('Post code', 'postcode'),
            Number::make('Quantity'),
            BelongsTo::make('Subscription'),
            BelongsTo::make('Zone'),

            // todo
            // customer
            // BelongsTo::make('Customer', 'order.customer'),

            BelongsTo::make('Order')->searchable()->nullable(),

            Text::make('Tracking Number'),
            // cost
            // shipstation orderId

            DateTime::make('Created At')->sortable(),
            DateTime::make('Shipped At')->sortable(),
            DateTime::make('Canceled At')->sortable(),

            // \Laravel\Nova\Fields\MorphTo::make('Product', 'purchasable')
            // ->types([
            //     Product::class
            // ]),


            // product


            // \Laravel\Nova\Fields\MorphTo::make('Purchasable')->types([
            //     ProductVariant::class,
            // ]),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new Filters\ShippingStatus,
            new Filters\Zone,
            new Filters\Month,
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            Actions\DownloadAllLabels::make(),
            Actions\DownloadSelectedLabels::make(),
            Actions\GenerateRoute4Me::make(),

            Actions\GenerateSelectedLabels::make(),
            Actions\GenerateAllLabels::make(),
            Actions\Shipment\SetShippingStatus::make(),
            Actions\Shipment\CancelAction::make(),
            \Laravel\Nova\Actions\ExportAsCsv::make()
                ->withFormat(function ($model) {
                    return [
                        'ID' => $model->getKey(),
                        'Name' => $model->name,
                        'Customer' => $model->subscription?->name,
                        'Phone' => $model->subscription?->customer?->phone,
                        'Subscription ID' => $model->subscription_id,
                        'Order ID' => $model->order_id,
                        'Zone' => $model->zone?->name,
                        'Quantity' => $model->quantity,
                        'Line One' => $model->line_one,
                        'Line Two' => $model->line_two,
                        'City' => $model->city,
                        'State' => $model->state,
                        'Postcode' => $model->postcode,
                    ];
                }),


            // new Actions\GenerateLocalLabels,
        ];
    }
}
