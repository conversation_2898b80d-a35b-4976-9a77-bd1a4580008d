<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Number;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Services\ShipStationCarrierService;
use App\Services\ShipStationServiceService;
use App\Services\ShipStationPackageService;

class ShipStationConfiguration extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\ShipStationConfiguration::class;
    public static $displayInNavigation = false;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    public static $group = 'Configurations';
    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param Request $request
     * @return array
     */

    /**
     * Determine if the user can create new resources.
     *
     * @param Request $request
     * @return bool
     */
    public static function authorizedToCreate(Request $request)
    {
        // Check if any records exist in the database
        return !\App\ShipStationConfiguration::exists();
    }

    public function fields(NovaRequest $request)
    {
        $carrierService = app(ShipStationCarrierService::class);
        $serviceService = app(ShipStationServiceService::class);
        $packageService = app(ShipStationPackageService::class);

        $carriers = $carrierService->listCarriers();
        $services = $serviceService->listServices();
        $packages = $packageService->listPackages();

        return [
            ID::make(__('ID'), 'id')->sortable(),

            Select::make('Carrier Code', 'carrier_code')
                ->sortable()
                ->options($carriers)
                ->rules('required', 'max:255'),

            Select::make('Service Code', 'service_code')
                ->sortable()
                ->options($services)
                ->rules('required', 'max:255'),

            Select::make('Package Code', 'package_code')
                ->sortable()
                ->options($packages)
                ->rules('required', 'max:255'),

            Number::make('Weight Value', 'weight_value') // Changed to clarify it's the weight's value
            ->sortable()
                ->rules('required', 'nullable', 'numeric'),

            Select::make('Weight Units', 'weight_units') // New field for weight units
            ->sortable()
                ->options([
                    'pounds' => 'Pounds',
                    'ounces' => 'Ounces',
                    'grams' => 'Grams'
                ])
                ->rules('required', 'nullable', 'in:pounds,ounces,grams'),

            Text::make('Balance', function () use ($carrierService) {
                return $carrierService->getStampsComBalance();
            })->onlyOnIndex()->exceptOnForms(),
        ];
    }


    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
