<?php

namespace App\Nova\Repeater;

use App\Product;
use App\Lunar\OrderLine;
use <PERSON>vel\Nova\Fields\Hidden;
use <PERSON>vel\Nova\Fields\Repeater\Repeatable;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ProductItem extends Repeatable
{
    public static $model = OrderLine::class;

    /**
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Hidden::make('id'),
            Select::make('Product item', 'purchasable_id')->options(function () {
                $accessoryEntries = Product::accessories()->get();
                $issueEntries = Product::issues()->get();

                $makeLabel = fn($group, $name) => [
                    'label' => $name,
                    'group' => $group,
                ];

                $accessories = $accessoryEntries->mapWithKeys(function ($entry) use ($makeLabel) {
                    return [$entry->variant->id => $makeLabel('Accessory', $entry->name . ' - ' . $entry->price)];
                });

                $issues = $issueEntries->mapWithKeys(function ($entry) use ($makeLabel) {
                    return [$entry->variant->id => $makeLabel('Issue', $entry->name . ' - ' . $entry->price)];
                });

                return $accessories->union($issues)->toArray();
            }),
            Text::make('Quantity', 'quantity')->default(1)
        ];
    }
}
