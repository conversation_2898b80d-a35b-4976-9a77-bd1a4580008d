<?php

namespace App\Nova\Repeater;

use App\Product;
use App\Subscription;
use App\Rules\HasZipRule;
use <PERSON>vel\Nova\Fields\Repeater\Repeatable;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class SubscribeCreatingItem extends Repeatable
{
    public static $model = Subscription::class;

    /**
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Select::make('Subscribe item', 'subscription_type')->options(function () {
                return Product::cycles()
                    ->get()
                    ->mapWithKeys(fn($product) => [
                        $product->name => [
                            'label' => ucfirst($product->name) . ' - ' . $product->price,
                        ],
                    ]);
            })
            ->default('yearly')
            ->rules('required'),

            Select::make('Start Month', 'subscription_month_id')
                ->rules('required')
                ->options(\App\SubscriptionMonth::pluck('name', 'id'))
                ->default(\App\SubscriptionMonth::getCurrent()->id),

            Text::make('Name', 'name')->rules('required'),
            Text::make('Address', 'address_line_1')->rules('required'),
            Text::make('Address line 2', 'address_line_2'),
            Text::make('City', 'city')->rules('required'),
            Text::make('State', 'state')->rules('required'),
            Text::make('Zip code', 'postal_code')->rules('required', new HasZipRule()),
        ];
    }
}
