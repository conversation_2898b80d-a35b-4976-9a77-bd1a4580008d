<?php

namespace App\Nova;

use App\Customer;
use App\Nova\Actions\Order\RefundPartialAction;
use App\Nova\Actions\Order\RefundProratedAction;
use App\Nova\Filters\OrderPromoResource;
use App\Nova\Filters\OrderResource;
use App\Services\Checkout\CheckoutBackend;
use Illuminate\Support\Facades\DB;
use Laravel\Nova\Actions\ExportAsCsv;
use Laravel\Nova\Fields\Badge;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasManyThrough;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Fields\MorphedByMany;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Stack;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Panel;
use Laravel\Nova\URL;
use Illuminate\Database\Eloquent\Builder;
use Lunar\Models\Discount;
use Wds\OrderRepeaterProduct\CreateRequest;
use Wds\OrderRepeaterProduct\OrderRepeaterProduct;
use Wds\OrderRepeaterProduct\Requests;
use Wds\StripeBelongToField\StripeBelongToField;

class Order extends Resource
{
    public static $model = \App\Models\Collections\Order::class;
    public static $title = 'id';
    public static $showPollingToggle = true;
    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Panel::make('Customer Information', $this->customerFields()),
            Panel::make('Payment Information', $this->paymentFields()),

            ...$this->showInIndex(),

            Panel::make('Items Information', [

                OrderRepeaterProduct::make('Information')
                    ->rules('required', new CreateRequest())
                    ->fillUsing(function ($request, $model) {
                        if ($request->editMode == 'create') {
                            return;
                        }

                        Requests::fillUsingHandler($request, $model);

                    }),
            ]),

            HasMany::make('Payments')->showOnDetail(),
            HasMany::make('Shipments')->showOnDetail(),

            $this->getProductsField(),

            HasManyThrough::make('Subscriptions', 'lunarSubscriptionsThrough')->showOnCreating(false),
//            HasMany::make('Subscriptions', 'subscriptions')->showOnDetail(),
        ];
    }

    protected function showInIndex(): array
    {
        return [
            Text::make('Name', 'shippingAddress.first_name')
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Text::make('Payment type', 'meta->payment_type')
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Badge::make('Status')
                ->map([
                    'payment-declined' => 'danger',
                    'payment-received' => 'success',
                    'payment-refunded' => 'danger',
                    'awaiting-payment' => 'warning',
                    'no-payment' => 'success',
                    'cash' => 'success',
                    'canceled' => 'danger',
                ])
                ->filterable()
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Currency::make('Sub Total')
                ->resolveUsing(function ($value) {
                    return $value?->value / 100;
                })
                ->displayUsing(function ($value) {
                    return $value?->formatted();
                })
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Currency::make('Tax Total')
                ->resolveUsing(function ($value) {
                    return $value?->value / 100;
                })
                ->displayUsing(function ($value) {
                    return $value?->formatted();
                })
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Currency::make('Shipping Total', 'shipping_without_tax')
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Currency::make('Discount Total')
                ->resolveUsing(function ($value) {
                    return $value?->value / 100;
                })
                ->displayUsing(function ($value) {
                    return $value?->formatted();
                })
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Currency::make('Total')
                ->resolveUsing(function ($value) {
                    return $value?->value / 100;
                })
                ->displayUsing(function ($value) {
                    return $value?->formatted();
                })
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Text::make('Address')
                ->resolveUsing(function () {
                    return $this->shippingAddress?->address;
                })
                ->onlyOnForms()
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            Stack::make('Address', [
                Text::make('Address', 'shippingAddress.first_name'),
                Text::make('Address', 'address_string'),
            ])->hideWhenUpdating()->hideWhenCreating(),
            Text::make('Subscription Type', 'id')
                ->displayUsing(function () {
                    return $this->resource->subscription()->pluck('name')->join('<br/>');
                })
                ->asHtml()
                ->hideWhenUpdating()
                ->hideWhenCreating(),
            DateTime::make('Created At')
                ->filterable()
                ->sortable()
                ->hideWhenUpdating()
                ->hideWhenCreating(),
        ];
    }

    protected function customerFields(): array
    {
        return [
            BelongsTo::make('Customer', 'customer')
                ->showCreateRelationButton()
                ->filterable()
                ->searchable(),
            Boolean::make('I confirmed with customers that the subscriptions is set on Auto-Renew', 'customer_confirm_renew')
                ->trueValue(true)
                ->falseValue(false)
                ->hideFromIndex()
                ->hideWhenUpdating(),
            Boolean::make('Auto Renew', 'subscription_auto_renew')
                ->trueValue(true)
                ->falseValue(false)
                ->default(true)
                ->hideFromIndex()
                ->hideWhenUpdating(),
        ];
    }

    protected function paymentFields(): array
    {

        return [
            Select::make('Payment Type', 'payment_type')
                ->options(function () {
                    return [
                        'credit' => 'Credit Card',
                        'check' => 'Check',
                        'cash' => 'Cash',
                    ];
                })
                ->fillUsing(function ($request, $model) {
                    if ($request->editMode == 'create') {
                        return;
                    }
                    $model->update([
                        'meta->payment_type' => $request->payment_type
                    ]);
                })
                ->readonly(function () {
                    return (bool)$this->resource->id;
                })
                ->hideFromDetail()
                ->hideFromIndex()
                ->creationRules('required'),

            StripeBelongToField::make('Credit Card', 'creditCard')
                ->dependsOn(
                    ['payment_type', 'customer'], function ($field, $request, $formData) {
                    $field->{$formData->payment_type === 'credit' && $formData->customer ? 'show' : 'hide'}();

//                    $field->relatableQueryUsing(function (NovaRequest $request, Builder $query) use ($formData) {
//                        $query->where('customer_id', $formData->customer);
//                    });

                    $field->customer($formData->customer);
                }
                ),
//            BelongsTo::make('Credit Card', 'creditCard')
//                ->showCreateRelationButton()
//                // ->searchable()
//                ->fillUsing(function ($request, $model) {
//                    if ($request->editMode == 'create') {
//                        return;
//                    }
//                    if ($request->creditCard) {
//                        $card = \App\CreditCard::query()->find($request->creditCard);
//                        $model->update([
//                            'meta->creditCardData' => [
//                                'token' => $card->token,
//                                'creditCardId' => $card->id,
//                            ]
//                        ]);
//                    } else {
//                        $meta = $model->meta;
//                        unset($meta['creditCardData']);
//                        $model->update([
//                            'meta' => $meta
//                        ]);
//                    }
//                })
//                ->withMeta(['value' => $this->meta->creditCardData['creditCardId'] ?? null])
//                ->creationRules('required_if:paymentType,credit') // remove update credit card
//                ->nullable()
//                ->dependsOn(
//                    ['payment_type', 'customer'], function ($field, $request, $formData) {
//                    $field->{$formData->payment_type === 'credit' ? 'show' : 'hide'}();
//
//                    $field->relatableQueryUsing(function (NovaRequest $request, Builder $query) use ($formData) {
//                        $query->where('customer_id', $formData->customer);
//                    });
//                }
//                )
//                ->hideFromDetail()
//                ->hideFromIndex()
//                ->readonly(function () {
//                    return (bool)$this->resource->id;
//                }) // remove update credit card
//                ->showOnCreating()->modalSize('7xl'),

            Select::make('Discount')
                ->nullable()
                ->searchable()
                ->onlyOnForms()
                ->readonly(function () {
                    return (bool)$this->resource->id;
                })
                ->hideWhenCreating()
                ->options(Discount::query()->pluck('name', 'id')),
        ];
    }

    private function getProductsField(): MorphedByMany
    {
        return
            MorphedByMany::make('Products', 'productVariants', 'App\Nova\SubResources\Order\OrderPhysical')
                ->allowDuplicateRelations()
                ->fields(function () {
                    return [
                        Text::make('Type')
                            ->hideWhenUpdating()
                            ->dependsOn(['productVariants'], function ($field, $request, $formData) {
                                $entry = $this->getProductVariant($formData->productVariants);
                                if ($entry) {
                                    $field->setValue($entry->shippable ? 'physical' : 'digital');
                                    return;
                                }
                                $field->setValue(null);
                            }),
                        Text::make('Description')
                            ->hideWhenUpdating()
                            ->dependsOn(['productVariants'], function ($field, $request, $formData) {
                                $entry = $this->getProductVariant($formData->productVariants);
                                if ($entry) {
                                    $field->setValue($entry->product->attribute_data->get('name'));
                                    return;
                                }
                                $field->setValue(null);
                            }),
                        Text::make('Identifier')
                            ->hideFromIndex()
                            ->hideWhenUpdating()
                            ->dependsOn(['productVariants'], function ($field, $request, $formData) {
                                $entry = $this->getProductVariant($formData->productVariants);
                                if ($entry) {
                                    $field->setValue($entry->sku);
                                    return;
                                }
                                $field->setValue(null);
                            }),
                        Currency::make('Unit price')
                            ->resolveUsing(function ($value) {
                                return $value?->value / 100;
                            })
                            ->displayUsing(function ($value) {
                                return '$' . $value / 100;
                            }),
                        Number::make('Quantity'),
                        Currency::make('Sub total')
                            ->resolveUsing(function ($value) {
                                return $value?->value / 100;
                            })
                            ->displayUsing(function ($value) {
                                return '$' . $value / 100;
                            }),
                        Currency::make('Discount total')
                            ->resolveUsing(function ($value) {
                                return $value?->value / 100;
                            })
                            ->displayUsing(function ($value) {
                                return '$' . $value / 100;
                            }),
                        Hidden::make('Tax breakdown')
                            ->default(function () {
                                return '[]';
                            }),
                        Currency::make('Tax total')
                            ->resolveUsing(function ($value) {
                                return $value?->value / 100;
                            })
                            ->displayUsing(function ($value) {
                                return '$' . $value / 100;
                            }),
                        Currency::make('Total')
                            ->resolveUsing(function ($value) {
                                return $value?->value / 100;
                            })
                            ->displayUsing(function ($value) {
                                return '$' . $value / 100;
                            }),
                    ];
                })
                ->showOnDetail();
    }

    public static function afterUpdate(NovaRequest $request, $model): void
    {
        $model->sub_total = $model->lines->sum('sub_total.value');
        $model->total = $model->lines->sum('total.value');
        $model->save();
    }

    private function getProductVariant($variantId)
    {
        return \Lunar\Models\ProductVariant::with('product')->find($variantId);
    }

    public static function creating()
    {
        $data = request();
        DB::commit();
        DB::beginTransaction();

        $customer = Customer::query()->find($data->input('customer'));
        $productsData = collect($data->product)->map(function ($item) {
            return (object)[
                'id' => $item['id'],
                'count' => $item['count'],
            ];
        });
        $address = [];


        $products = \Lunar\Models\ProductVariant::query()->whereIn('id', $productsData->pluck('id'))
            ->get()
            ->map(function ($item) use ($productsData) {
                $data = $productsData->firstWhere('id', $item['id']);

                $item->count = $data->count;

                return $item;
            });

        if ($products->count()) {
            $delivery = (object)$data->input("delivery", []);

            $address = [
                "name" => $delivery->name,
                "address_line_1" => $delivery->address_line_1,
                "address_line_2" => $delivery->address_line_2,
                "city" => $delivery->city,
                "state" => $delivery->state,
                "zip" => $delivery->postcode,
            ];
        }
        $subscriptionsData = collect($data->cycle)->map(function ($item) {
            return (object)[
                'id' => $item['type'],
                'subscription_month_id' => $item['month'],
                'name' => $item['name'],
                "address_line_1" => $item["address_line_1"],
                "address_line_2" => $item["address_line_2"],
                "city" => $item["city"],
                "state" => $item["state"],
                "zip" => $item["postcode"],
            ];
        });
        $entriesSubscription = \App\Product::query()
            ->where('product_type_id', \App\Lunar\Cycle::$productType)
            ->whereIn('attribute_data->name->value', $subscriptionsData->pluck('id'))
            ->get();

        $subscriptions = $subscriptionsData->map(function ($data) use ($entriesSubscription, &$address) {
            $item = $entriesSubscription->where('name', $data->id)->first();
            $item->delivery = (object)[
                'name' => $data->name,
                "address_line_1" => $data->address_line_1,
                "address_line_2" => $data->address_line_2,
                "city" => $data->city,
                "state" => $data->state,
                "zip" => $data->zip,
            ];

            $item->subscription_month_id = $data->subscription_month_id;

            if (!count($address)) {
                $address = [
                    'name' => $data->name,
                    "address_line_1" => $data->address_line_1,
                    "address_line_2" => $data->address_line_2,
                    "city" => $data->city,
                    "state" => $data->state,
                    "zip" => $data->zip,
                ];
            }

            return $item;
        });

        $checkout = app()->make(CheckoutBackend::class);
        $checkout->setCustomer($customer);
        $checkout->setTypePayment($data->input("payment_type"));

        $checkout->setAutoRenew((bool)$data->input("subscription_auto_renew"));
        if ($data->input("payment_type") == 'credit') {
            $checkout->setCardId($data['creditCard']);
        }
        $checkout->setAdminRenewNotification((bool)$data->input('customer_confirm_renew'));
        $checkout->setAddress($address);
        $checkout->setDiscount($data);

        if ($products->count()) {
            $checkout->setProducts($products);
        }

        if ($subscriptions->count()) {
            $checkout->setSubscribes($subscriptions);
        }

        $response = $checkout->store();

        if ($response->status != 200) {
            response()->json($response, $response->status)->send();

            exit;
        }

        DB::commit();
        response()->json([
            'id' => null,
            'resource' => null,
            'redirect' => URL::make('/resources/' . static::uriKey()),
        ], 201)->send();

        exit;
    }

    public function actions(NovaRequest $requests): array
    {
        return [
            ExportAsCsv::make()
                ->withFormat(function ($model) {
                    return collect([
                        'customer_name' => data_get($model->customer, 'name'),
                        'customer_email' => data_get($model->customer, 'email'),
                        'customer_phone' => data_get($model->customer, 'phone'),
                    ])->merge($model->only(['id', 'status', 'created_at']));
                }),
            RefundPartialAction::make()
                ->canSee(function () {
                    return !in_array($this->resource->status, config('lunar.orders.non_refundable_statuses'));
                }),
            RefundProratedAction::make()
                ->canSee(function () {
                    return !in_array($this->resource->status, config('lunar.orders.non_refundable_statuses'));
                })
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new OrderResource,
            new OrderPromoResource
        ];
    }
}
