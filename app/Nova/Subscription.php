<?php

namespace App\Nova;

use <PERSON><PERSON><PERSON>\DependencyContainer\DependencyContainer;
use <PERSON><PERSON><PERSON>\DependencyContainer\HasDependencies;
use App\Nova\Filters\AddressVerified;
use App\Nova\Filters\Promos;
use App\Nova\Lenses\AddressInformation;
use App\Nova\Metrics\ExpiringSubscription;
use App\Nova\Metrics\Subscriptions;
use App\Nova\Metrics\SubscriptionsByZone;
use App\Price;
use Capitalc\SubscriptionTotal\SubscriptionTotal;
use Coroowicaksono\ChartJsIntegration\DoughnutChart;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Laravel\Nova\Actions\ExportAsCsv;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Country;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\FormData;
use Laravel\Nova\Fields\HasMany;
use Lara<PERSON>\Nova\Fields\HasManyThrough;
use <PERSON><PERSON>\Nova\Fields\Heading;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MultiSelect;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\ActionRequest;
use Laravel\Nova\Http\Requests\NovaRequest;
use Vyuldashev\NovaMoneyField\Money;

class Subscription extends Resource
{
    use HasDependencies;

    public static $group = 'Subscriptions';
    public static $clickAction = 'ignore';


    public static array $searchRelations = [
        'customer' => [
            'name',
            // 'phone',
            'email'
        ],
    ];

    public static function softDeletes()
    {
        return true;
    }
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\Subscription';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'address_line_1',// 'address_line_2', 'city', 'state', 'postal_code'
    ];

    public static $showPollingToggle = true;


    public function fieldsForIndex()
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make('Customer')
                ->showCreateRelationButton()
                ->sortable()
                ->onlyOnIndex(),

            // Text::make('Verified Address', 'meta.address')->readonly(),

            Text::make('Address')
                ->displayUsing(function () {
                    return $this->address_line_1 . ' ' . $this->address_line_2;
                }),

            BelongsTo::make('Zone')
                ->readonly()
                ->sortable(),

            BelongsTo::make('Subscription Month')
                ->rules('required')
                ->default(\App\SubscriptionMonth::getCurrent()->id),
            Number::make('Remaining Issues', 'issues')
                ->filterable()
                ->sortable(),

            Number::make('Publications Sent', 'publications_sent')
                ->sortable(),

            Boolean::make('Auto Renew', 'renew'),
            Select::make('Status', 'canceled')
                ->resolveUsing(function ($value) {
                    return $this->status();
                })
                ->sortable()
                ->exceptOnForms(),

            Select::make('Canceled reason')
                ->resolveUsing(function ($value) {
                    return $value;
                })
                ->options([
                    null => 'None',
                    'cc failed' => 'CC failed',
                    'Refunded' => 'Refunded',
                ])
                ->sortable(),
        ];
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name'),


            Select::make('Subscription Type')
                ->options(function () {
                    $prices = Price::whereIn('type', ['monthly', 'yearly'])->get();
                    $options = [];
                    foreach ($prices as $price) {
                        $options[$price->type] = ucfirst($price->type) . ' - $' . $price->amount;
                    }
                    return $options;
                })
                ->onlyOnForms()
                ->rules('required'),

            Number::make('Publications Sent', 'publications_sent')
                ->min(0)
                ->step(1)
                ->sortable()
                ->hideWhenCreating(),

            Number::make('Remaining Issues', 'issues')
                ->filterable()
                ->sortable()
                ->hideWhenCreating()
                ->min(0),

            BelongsTo::make('Subscription Month')
                ->rules('required')
                ->default(\App\SubscriptionMonth::getCurrent()->id)
            // ->hideFromIndex()
            // ->onlyOnForms()
            ,

            // Select::make('Subscription Month', 'subscription_month_id')
            //     ->options(function () {
            //         return \App\SubscriptionMonth::pluck('id', 'name');
            //         return collect([
            //             \App\SubscriptionMonth::getCurrent()->id => \App\SubscriptionMonth::getCurrent()->name,
            //             \App\SubscriptionMonth::getNext()->id => \App\SubscriptionMonth::getNext()->name
            //         ]);
            //     })
            //     ->hideFromIndex()
            //     ->rules('required')
            //     ->onlyOnForms(),


            // Text::make('Subscription Month Name')
            //     ->onlyOnDetail() // Display only on the detail page
            //     ->rules('required'),

            BelongsTo::make('Cycle')->sortable(),

            BelongsTo::make('Gift')
                ->nullable()->sortable(),


            BelongsTo::make('Customer')
                ->searchable()
                ->withSubtitles()
                ->showCreateRelationButton()
                ->sortable()
                ->hideFromIndex(),


            Select::make('Preferred Contact Method', 'customer.preferred_contact_method')
                ->options([
                    'email' => 'Email',
                    'text' => 'Text',
                    'phone' => 'Phone'
                ])
                ->displayUsing(function ($preferredContactMethod) {
                    return ucfirst($preferredContactMethod);
                })
                ->fillUsing(function ($request, $model, $attribute, $requestAttribute) {
                    if (isset($request[$requestAttribute]) && !is_null($request[$requestAttribute])) {
                        $model->customer->preferred_contact_method = $request[$requestAttribute];
                        $model->customer->save();
                    }
                }),


            HasManyThrough::make('Orders', 'lunarOrderThrough')->showOnCreating(false),
            BelongsTo::make('Created By', 'user', '\App\Nova\User')->nullable()->onlyOnDetail(),

            Heading::make('Delivery Information'),
            // new Panel('Delivery Information', [
            // Boolean::make('This Is A Gift', 'is_gift'),
            // DependencyContainer::make([
            //     Text::make('Recipients Name', 'name')->hideFromIndex()->nullable(),
            //     Textarea::make('Gift Note', 'note')->nullable(),
            // ])->dependsOn('is_gift', true),
            Text::make('Name')->onlyOnDetail()->rules('required'),
            Text::make('Address', 'address_line_1')->hideFromIndex()->rules('required'),
            Text::make('Address Line 2')->hideFromIndex(),
            Text::make('City')->hideFromIndex()->rules('required'),
            Text::make('State')->hideFromIndex()->rules('required'),
            Country::make('Country')
                ->default('US')
                ->hideFromIndex()
                ->nullable(),
            Text::make('Postal Code', 'postal_code')->hideFromIndex()->rules('required'),
//            Boolean::make('Apartment Building', 'apartment_building')
//                ->trueValue(true)
//                ->falseValue(false)
//                ->hideFromIndex()
//                ->default(true),

//            DependencyContainer::make([
//                Text::make('Apartment Number', 'apt_number')
//                    ->hideFromIndex(),
//
//                Text::make('Building Door Code', 'building_door_code')
//                    ->hideFromIndex(),
//            ])->dependsOn('apartment_building', true),

            Text::make('Verified Address', 'meta.address')->readonly(),
            BelongsTo::make('Zone')->readonly()->hideWhenCreating()->sortable(),
            // Heading::make('Payment'),
            // new Panel('Payment', [
            Select::make('Promo', 'promo_id')
                ->nullable()
                ->onlyOnForms()
                ->hideWhenUpdating()
                ->options(\App\Promo::pluck('name', 'id')),

            Heading::make('Changing the payment method will only reflect on future renewals but will not charge the card now. To add a payment to an existing subscription please go to Orders.')->onlyOnForms()->hideWhenCreating(),

            Select::make('Payment Type')
                ->hideFromIndex()->rules('required')->options([
                    'Check' => 'Check',
                    'Cash' => 'Cash',
                    'Credit Card' => 'Credit Card'
                ]),

            // BelongsToDependency::make('Credit Card')
            //     ->nullable()
            //     ->showCreateRelationButton()
            //     ->dependsOn('customer', 'customer_id')->hideFromIndex(),

            BelongsTo::make('Credit Card')
                ->showCreateRelationButton()
                ->hide()
                ->dependsOn(['customer', 'payment_type'], function (BelongsTo $field, NovaRequest $request, FormData $formData) {
                    if ($formData->payment_type == 'Credit Card') {
                        $field->show()->rules('required');
                    } else {
                        $field->nullable();
                    }

                    $field->relatableQueryUsing(function (NovaRequest $request, Builder $query) use ($formData) {
                        $query->where('customer_id', $formData->customer);
                    });
                }),

            // CreditCardField::make('')
            //     ->dependsOn('customer')
            //     ->withMeta([
            //         'name' => 'Credit Card',
            //         'creditCard' => $this->credit_card
            //     ])
            //     ->hideFromIndex(),

            SubscriptionTotal::make('Hello')->onlyOnForms()->hideWhenUpdating(),

            DependencyContainer::make([
                Money::make('Amount')->hideWhenUpdating(),
            ])->dependsOn('charge_partially', true),
            // ]),
            Text::make('Coupon Code (For Renew)', 'coupon_code'),

            Heading::make('Timeframe & Pause'),
            // new Panel('Timeframe & Pause', [
            // Date::make('Start')
            //     ->filterable()
            //     ->hideWhenCreating()
            //     ->sortable()
            //     ->rules('required'),

            // Select::make('Start')
            //     ->options((new \App\Zone)->dates())
            //     ->withMeta([
            //         'value' => (new \App\Zone)->upcomingStartDate()->toDateString(),
            //     ])
            //     ->rules('required')
            //     ->onlyOnForms()->hideWhenUpdating(),

            // Date::make('Finish')->hideWhenCreating()->sortable(),
            Boolean::make('I confirmed with customers that the subscriptions is set on Auto-Renew', 'admin_renew_notification'),
            Boolean::make('Auto Renew', 'renew'),
            Boolean::make('Canceled')->onlyOnForms(),

            Select::make('Status', 'canceled')
                ->resolveUsing(function ($value) {
                    return $this->status();
                })
                ->sortable()
                ->exceptOnForms(),

            Select::make('Canceled reason')
                ->resolveUsing(function ($value) {
                    return $value;
                })
                ->options([
                    null => 'None',
                    'cc failed' => 'CC failed',
                    'Refunded' => 'Refunded',
                ])
                ->sortable(),
            // Date::make('Pause From')->hideWhenCreating()->hideFromIndex()->readonly(),
            // Date::make('Pause To')->hideWhenCreating()->hideFromIndex()->readonly(),

            // Display field for detail view - shows month names
            Text::make('Skip Months', 'skip')
                ->resolveUsing(function ($value) {
                    $skipIds = $this->skip ?? [];
                    if (empty($skipIds)) {
                        return 'None';
                    }

                    $months = \App\SubscriptionMonth::whereIn('id', $skipIds)->pluck('name')->toArray();
                    return implode(', ', $months);
                })
                ->onlyOnDetail(),

            // MultiSelect field for forms - allows editing
            MultiSelect::make('Skip Months', 'skip')
                ->onlyOnForms()
                ->options(
                    \App\SubscriptionMonth::pluck('name', 'id')->toArray()
                ),

            HasMany::make('Routes', 'routeDetails', \App\Nova\SubscriptionSubscriptionRoute::class),



            HasMany::make('Shipments')
                ->sortable(),
        ];
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }
    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [
            new SubscriptionsByZone,
            new Subscriptions,
            (new DoughnutChart())
                ->title('Subscriptions By Cycle')
                ->model('\App\Subscription')
                ->series([
                    [
                        'label' => 'Yearly',
                        'filter' => [
                            'key' => 'subscription_type',
                            'value' => 'yearly'
                        ]
                    ],
                    [
                        'label' => 'Monthly',
                        'filter' => [
                            'key' => 'subscription_type',
                            'value' => 'monthly'
                        ]
                    ]
                ])
                ->options([
                    'btnRefresh' => true,
                    'latestData' => '*',
                    'queryFilter' => [
                        ['key' => 'canceled',
                            'operator' => 'IN',
                            'value' => [0, false, null],
                        ]
                    ],
                ])
                ->width('1/3'),
            new ExpiringSubscription,
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [
            // new Filters\Finished,
            new Filters\International,
            new Filters\Cycle,
            new Filters\Zone,
            new Filters\AutoRenew,
            new Filters\Status,
            new Filters\MissingMonth,
            new Filters\NoIssuesLeft,
            new Filters\NotPausedForMonth,
            new Filters\ReceivingForMonth,
            new Promos,
            new AddressVerified
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [
            new AddressInformation
        ];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            new Actions\Subscription\CancelAction,
            new Actions\Subscription\Renew,
            (new Actions\Subscription\Pause)
                ->canSee(function ($request) {
                    if ($request instanceof ActionRequest) {
                        return true;
                    }
                    return !optional($this->resource)->isPaused();
                }),

            (new Actions\Subscription\Unpause)
                ->canSee(function ($request) {
                    if ($request instanceof ActionRequest) {
                        return true;
                    }
                    return !!optional($this->resource)->isPaused();
                }),
            (new Actions\Subscription\Extend),
            // (new Actions\Subscription\ChangeCycle),
            new Actions\Subscription\DownloadExpired,
            new Actions\Subscription\DownloadNotes,
            new Actions\Subscription\DownloadActive,
            new Actions\Subscription\DownloadUnsentCurrentIssues,
            new Actions\CreateAllShipments,
            (new Actions\CreateSelectedShipments)
                ->canSee(function ($request) {
                    if ($request instanceof ActionRequest) {
                        return true;
                    }
                    return !!optional($this->resource)->issues > 0;
                }),
            // new Actions\Subscription\RefundFullAction,
            // new Actions\Subscription\RefundPartialAction,
            // new Actions\Subscription\RefundProratedAction,
            ExportAsCsv::make()
                ->withFormat(function ($model) {
                    return $model->toExport();
                }),
        ];
    }

    public static function _relatableCreditCards($request, $query)
    {
        if ($id = data_get($request->findModelQuery()->first(), 'customer_id')) {
            return $query->where('customer_id', $id);
        }

        return $query;
    }
    public static function afterUpdate(NovaRequest $request, Model $model){
        if($model->lunar_order){
            $model->lunar_order?->shippingAddress?->update([
                'line_one' => $model->address_line_1,
                'line_two' => $model->address_line_2,
                'city' => $model->city,
                'state' => $model->state,
                'postcode' => $model->postal_code,
                'country' => $model->country,
            ]);
        }
    }
}
