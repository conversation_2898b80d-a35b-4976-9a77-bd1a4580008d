<?php

namespace App\Nova;

use App\Promo as AppPromo;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Select;

class Promo extends Resource
{
    public static $group = 'Configurations';

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Promo::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'code',
    ];

    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Name')->rules('required')->detailLink(),
            Text::make('Code')->rules(['required', 'unique:promos,code,{{resourceId}}']),
            Number::make('Amount')->rules('required'),
            Select::make('Type')->rules('required')->options(AppPromo::$types),
            Date::make('Start')->nullable(),
            Date::make('End')->nullable(),
            Number::make('Limit amount of uses', 'limit')->nullable(),
            // Boolean::make('Per Customer')->nullable()->hideFromIndex(),//never worked on it
            Boolean::make('Eligible on renewal', 'work_on_renew')->nullable()->hideFromIndex(),
            Number::make('Remaining', 'left')->hideWhenCreating()->readonly(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            // new ImportPromos,
        ];
    }
}
