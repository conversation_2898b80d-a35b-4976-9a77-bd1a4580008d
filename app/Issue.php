<?php

namespace App;

use Illuminate\Database\Eloquent\Casts\Attribute;

class Issue extends \Dystcz\LunarApi\Domain\Products\Models\Product
{
    public function newQuery()
    {
        $query = parent::newQuery();

        $query->where('product_type_id', static::$productType);

        return $query;
    }

    public $guarded = [];

    public $with = ['variants'];

    static public $productType = 2;

    protected $attributes = [
        'product_type_id' => 2, //static::$productType,
    ];

    static public $variant_data = [
        'length',
        'width',
        'height',
        'weight',
    ];

    public $price_data = [
        'price'
    ];

    public function toArray()
    {
        return $this->only([
                'id',
                'name',
                'description',
                'price',
                'img',
                'pdf',
                'mp3',
            ])
            + [
                'count' => 0
            ];
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->translateAttribute('name'),
            set: fn(mixed $value) => [
            ],
        );
    }

    protected function img(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->getFirstMediaUrl('images'),
        );
    }

    protected function mp3(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->getFirstMediaUrl('mp3'),
        );
    }

    protected function pdf(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->getFirstMediaUrl('pdf'),
        );
    }

    protected function price(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->prices->first()?->price->decimal(rounding: true),
        );
    }

    protected function variantJson(): Attribute
    {
        if ($variant = $this->variants->first()) {
            $data = collect(static::$variant_data)
                ->mapWithKeys(function ($index) use ($variant) {
                    $column = $index . '_value';
                    return [
                        $index => $variant->$column,
                    ];
                });
        } else {
            $data = [];
        }

        return Attribute::make(
            get: fn(mixed $value) => $data,
            set: fn(mixed $value) => [],
        );
    }

    public function variant()
    {
        return $this->variants->first();
    }
}
