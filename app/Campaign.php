<?php

namespace App;

use App\Http\Controllers\EmailsController;
use CampaignMonitor;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Campaign extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $with = ['media'];

    protected $casts = [
        'date' => 'date',
        'schedule' => 'datetime',
        'components' => 'array',
    ];

    public function getRouteKeyName()
    {
        return 'number';
    }

    public static function boot()
    {
        parent::boot();

        self::deleted(function ($model) {
            if ($model->campaign_id) {
                CampaignMonitor::Campaigns($model->campaign_id)->delete();
            }
        });

        self::saved(function ($model) {
            if (!$model->toggle_schedule && $model->getOriginal('toggle_schedule')) {
                EmailsController::deleteCampaign($model->id);
            }
        });
    }

    public function getStatusAttribute()
    {
        if ($this->sent && $this->schedule && $this->schedule->isAfter(now())) {
            return 'Scheduled';
        } elseif ($this->sent) {
            return 'Sent';
        } else {
            return 'Draft';
        }
    }

    public function getPathAttribute()
    {
        return '/campaign/' . $this->number;
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        $this->addMediaConversion('components')
            ->width(600)
            ->performOnCollections('images');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('components');
    }

    public function updateStatus()
    {
        EmailsController::summary($this->id);
    }
}
