<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;

class Gift extends Model implements HasMedia
{
    use InteractsWithMedia, HasFactory;

    protected $guarded = [];

    protected $casts = [
        'fields' => 'json',
        'extension' => 'boolean',
        'subscription_ids' => 'array'
    ];

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function cycles()
    {
        return $this->belongsToMany(Cycle::class)
            ->where('active', true);
    }

    public function getPictureAttribute()
    {
        return $this->getFirstMediaUrl('picture');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('picture')
            ->singleFile();
    }

    public static function customFields()
    {
        return self::pluck('fields')->filter()->map(function ($field) {
            return collect($field)->pluck('attributes')->map->title;
        })->flatten()->sort()->values();
    }
}
