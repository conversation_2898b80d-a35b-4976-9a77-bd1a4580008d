<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function getFrontEndAttribute()
    {
        return $this->only([
            'id', 'name', 'path'
        ]);
    }

    public function getPathAttribute()
    {
        return "/categories/" . slug($this->name) . "/" . $this->id;
    }

    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    public function articles()
    {
        return $this->belongsToMany(Article::class);
    }
}
