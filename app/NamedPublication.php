<?php

namespace App;

use App\Emails\TrackingInformation;
use App\Services\CreateLabelForOrderService;
use App\Services\ShipStationOrderService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NamedPublication extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'shipped_at' => 'datetime',
        'is_merged' => 'boolean',
    ];

    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    public function zone()
    {
        return $this->belongsTo(Zone::class);
    }

    public function subscriptionMonth()
    {
        return $this->belongsTo(SubscriptionMonth::class);
    }

    public function subscription_months()
    {
        return $this->belongsTo(SubscriptionMonth::class);
    }

    public function createLabel($sent = false)
    {
        $subscription = $this->subscription;

        $addressData = array_merge(
            $subscription->getVerifiedAddress(),
            [
                'name' => str_contains($subscription->name, ' ')
                    ? $subscription->name
                    : 'Family ' . $subscription->name,
                'phone' => $subscription->customer->phone,
                'residential' => false,
            ]
        );

        $config = \App\ShipStationConfiguration::find(1);

        $shipStationOrderData = [
            'orderNumber' => $this->id,
            'orderDate' => $this->created_at->toIso8601String(),
            'orderStatus' => 'awaiting_shipment',
            'billTo' => $addressData,
            'shipTo' => $addressData,

            "weight" => [
                "value" => $config->weight_value,
                "units" => $config->weight_units,
                "WeightUnits" => "1",
            ],
            "testLabel" => env('SHIPSTATION_TEST')
        ];

        $result = ShipStationOrderService::createOrder($shipStationOrderData);

        $this->update([
            'order_id' => $result['orderId'] ?? null,
        ]);

        if ($sent) {
            $this->update([
                'shipped_at' => now()
            ]);
        }

        $addressDataShipFrom = [
            'name' => 'Vinderkind',
            'street1' => '1303 53rd street',
            'street2' => '1303 53rd street',
            'city' => 'brooklyn',
            'state' => 'NY',
            'postalCode' => '11219',
            'country' => 'US',
            'phone' => '3473519597',
            'residential' => true,
        ];

        $labelData = [
            "orderId" => $this->order_id,
            "carrierCode" => $config->carrier_code,
            "serviceCode" => $addressData['country'] != 'US'
                ? $config->service_code
                : $config->service_code_intl,
            "confirmation" => "delivery",
            "shipDate" => today()->toDateString(),
            "packageCode" => $config->package_code,
            "weight" => [
                "value" => $config->weight_value,
                "units" => $config->weight_units
            ],
            "testLabel" => env('SHIPSTATION_TEST'),
            'shipFrom' => $addressDataShipFrom,
            'shipTo' => $addressData
        ];
        if ($addressData['country'] != 'US') {
            $labelData["internationalOptions"] = [
                'contents' => 'Book',
                'description' => 'Book',
                'quantity' => 1,
                'value' => 20,
                'harmonizedTariffCode' => 4901,
                'countryOfOrigin' => "US",
                // 'nonDelivery' => 'return_to_sender'
            ];
        }

        $labelData = [
            "orderId" => $result['orderId'],
            "carrierCode" => $config->carrier_code,
            "serviceCode" => $config->service_code,
            "confirmation" => "delivery",
            "shipDate" => today()->addDay(),
            "packageCode" => $config->package_code,
            "weight" => [
                "value" => $config->weight_value,
                "units" => $config->weight_units
            ],
            "testLabel" => env('SHIPSTATION_TEST')
        ];

        // Create label for the order
        $labelResult = resolve(CreateLabelForOrderService::class)->createLabelForOrder($labelData, 1, $this->id);

        $trackingData = [
            'user_name' => $subscription->name,
            'tracking_name' => $labelResult['trackingNumber'],
            'issue' => $this->subscriptionMonth->name,
            'tracking_url' => 'https://tools.usps.com/go/TrackConfirmAction_input?strOrigTrackNum=' . $labelResult['trackingNumber'],
        ];
        (new TrackingInformation)
            ->withData($trackingData)
            ->withUser($subscription->customer)
            ->sendTo();
    }
}
