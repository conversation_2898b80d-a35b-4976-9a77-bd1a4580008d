<?php

namespace App\Console;

use App\Console\Commands\NotifySubscriptionsWithOneIssueLeft;
use App\Console\Commands\RecurringOrders;
use App\Console\Commands\Subscription\ExpiringCommand;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    protected $commands = [
        Commands\TestShipStationConnection::class,
        NotifySubscriptionsWithOneIssueLeft::class,
    ];

    protected function schedule(Schedule $schedule)
    {
        $settingService = setting();

        $schedule
            ->command('recurring:orders --again=1')
            ->daily()
            ->onFailure(function () {
                Log::channel('slack')->critical('VK Recurring Orders Schedule failed.');
            })->when(function () use ($settingService) {
                return hebDateArray()['day'] == $settingService->getValue('hebrew_day_of_month_to_create_first_attempt_orders');
            });

        $schedule
            ->command('recurring:orders --again=0')
            ->daily()
            ->onFailure(function () {
                Log::channel('slack')->critical('VK Recurring Orders Schedule failed.');
            })->when(function () use ($settingService) {
                return hebDateArray()['day'] == $settingService->getValue('hebrew_day_of_month_to_create_second_attempt_orders');
            });

        $schedule
            ->command('backup:run --only-db')
            ->daily()
            ->onFailure(function () {
                Log::channel('slack')->critical('VK database backup failed.');
            });

        $schedule
            ->command('subscription:expiring')
            ->daily()
            ->onFailure(function () {
                Log::channel('slack')->critical('VK expiring subscriptions Schedule failed.');
            })
            ->when(function () {
                return hebDateArray()['day'] == 3;
            });

        // $schedule
        //     ->command('backup:media')
        //     ->daily()
        //     ->onFailure(function () {
        //         \Log::channel('slack')->critical('VK Image backup failed.');
        //     });

        // $schedule
        //     ->command('email-providers')
        //     ->hourly()
        //     ->onFailure(function () {
        //         \Log::channel('slack')->critical('VK Zone emails not sent.');
        //     });

        $schedule
            ->command('create:shipments')
            ->dailyAt('03:00')
            ->onFailure(function () {
                Log::channel('slack')->critical('VK Routes were not created.');
            })->when(function () use ($settingService) {
                return hebDateArray()['day'] == $settingService->getValue('hebrew_day_of_month_to_create_shipments');
            });

        // $schedule
        //     ->command('create:subscriptionroutes')
        //     ->dailyAt('03:00')
        //     ->onFailure(function () {
        //         \Log::channel('slack')->critical('VK Routes were not created.');
        //     })->when(function () {
        //         return hebDateArray()['day'] == 18;
        //     });

        $schedule
            ->command('heartbeat')
            ->hourly();

        /*  $schedule->command('renew:subscriptions')
            ->dailyAt('16:00')
            ->onFailure(function () {
                \Log::channel('slack')->critical('Subscriptions due to renew today were not created.');
            }); */

        // $schedule->command('subscriptions:cancel')
        //     ->daily()
        //     ->onSuccess(function () {
        //         \Log::channel('slack')->critical('Canceled subscriptions');
        //     });

        // $schedule->command('notify:subscriptions')->dailyAt('12:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
