<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class BackupMedia extends Command
{
    protected $signature = 'backup:media';
    protected $description = 'Backup Media to other file system.';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $missing = array_diff(
            Storage::disk(env('MEDIA_DISK'))->allFiles(),
            Storage::disk(env('BACKUP_MEDIA_DISK'))->allFiles(),
        );
        collect($missing)->each(function ($item) {
            $this->info('copying: ' . $item);
            Storage::disk(env('BACKUP_MEDIA_DISK'))->put(
                $item,
                Storage::disk(env('MEDIA_DISK'))->get($item)
            );
            $this->info('copied: ' . $item);
        });
        return 0;
    }
}
