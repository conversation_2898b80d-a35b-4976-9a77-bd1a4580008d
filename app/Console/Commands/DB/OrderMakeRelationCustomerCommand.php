<?php

namespace App\Console\Commands\DB;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class OrderMakeRelationCustomerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:order-make-relation-customer-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected int $limit = 500;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::table('lunar_orders')
            ->whereNull('customer_id')
            ->whereNotNull(DB::raw("JSON_EXTRACT(meta, '$.customer_id')"))
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('customers')
                    ->whereRaw('customers.id = JSON_EXTRACT(lunar_orders.meta, \'$.customer_id\')');
            })
            ->limit($this->limit)
            ->update([
                'customer_id' => DB::raw("JSON_EXTRACT(meta, '$.customer_id')")
            ]);
    }
}
