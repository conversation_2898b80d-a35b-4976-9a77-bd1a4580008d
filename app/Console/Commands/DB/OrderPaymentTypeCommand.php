<?php

namespace App\Console\Commands\DB;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class OrderPaymentTypeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:order-payment-type-command';

    protected int $limit = 500;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::table('lunar_orders')
            ->whereNull('meta->payment_type')
            ->limit($this->limit)
            ->update([
                'meta->payment_type' => 'credit'
            ]);
    }
}
