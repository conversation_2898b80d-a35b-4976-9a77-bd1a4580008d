<?php

namespace App\Console\Commands\DB;

use App\Lunar\Cycle;
use App\Lunar\OrderLine;
use App\NamedPublication;
use App\Product;
use App\Shipment;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ShipmentDigitalMigrateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:shipment-digital-migrate-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected int $limit = 300;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $entriesPublication = NamedPublication::query()
            ->with(['subscription'])
            ->where('is_merged', false)
            ->limit($this->limit)
            ->get();

        $cycles = Product::cycles()->get();

        $orderLineEntries = OrderLine::query()->whereIn('meta->subscription->id', $entriesPublication->pluck('subscription_id'))
            ->with('order')->get();

        foreach ($entriesPublication as $entryPublication) {
            $orderLineEntry = $orderLineEntries->where('meta.subscription.id', $entryPublication->subscription_id)->first();

            if(!$orderLineEntry){
                $entryPublication->is_merged = true;
                $entryPublication->saveQuietly();
                $this->info("Entry Publication {$entryPublication->id} without order line information");
                Log::info("Entry Publication {$entryPublication->id} without order line information");
                continue;
            }

            $address = $orderLineEntry->order?->shippingAddress;

            $entryPublication->subscription->update([
                'order_id' => $orderLineEntry->order_id
            ]);

            $subscriptionType = $entryPublication->subscription_type;
            $product = $cycles->where('name', $subscriptionType)->first();

            $shippingAddressId = $orderLineEntry->order?->shippingAddress?->id;


            if (!$product) {
                $entryPublication->is_merged = true;
                $entryPublication->saveQuietly();
                $this->info("Entry Publication {$entryPublication->id} without product variants");
                Log::info("Entry Publication {$entryPublication->id} without product variants");
                continue;
            }

            if (!$shippingAddressId) {
                $entryPublication->is_merged = true;
                $entryPublication->saveQuietly();
                $this->info("Entry Publication {$entryPublication->id} without address");
                Log::info("Entry Publication {$entryPublication->id} without address");
                continue;
            }

            Shipment::query()->create([
                'subscription_month_id' => $entryPublication->subscription_month_id,
                'name' => $subscriptionType,
                'purchasable_type' => $product->variant->getMorphClass(),
                'purchasable_id' => $product->variant->id,
                'subscription_id' => $entryPublication->subscription_id,
                'order_id' => $orderLineEntry->order_id,
                'zone_id' => $entryPublication->zone_id,
                'address_id' => $shippingAddressId,
                'tracking_number' => $entryPublication->tracking_number,
                'label_path' => null,
                'shipped_at' => $entryPublication->shipped_at,
                'amount' => $entryPublication->amount,
                'shipment_cost' => $entryPublication->shipment_cost,
                'updated_at' => now(),
                'created_at' => $entryPublication->created_at,
                'line_one' => $address->line_one,
                'line_two' => $address->line_two,
                'city' => $address->city,
                'state' => $address->state,
                'postcode' => $address->postcode,
            ]);

            $entryPublication->is_merged = true;
            $entryPublication->saveQuietly();
        }
    }
}
