<?php

namespace App\Console\Commands\Subscription;

use App\Services\Checkout\Checkout;
use App\Subscription;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RenewCommand extends Command
{
    protected $signature = 'subscriptions:renew';
    protected $description = 'Renew subscriptions that are due today';

    public function handle(): void
    {
        // Get all subscriptions that should be renewed today

        $subscriptions = Subscription::query()
            ->where('issues', 0)
            ->where('canceled', 0)
            ->where('renew', 1)
            ->get();

        // If no subscriptions are found, log this information and return
        if ($subscriptions->isEmpty()) {
            Log::info('No subscriptions are due for renewal today.');
            return;
        }


        // Log the ids of subscriptions due for renewal
        $subscriptionIds = $subscriptions->pluck('id')->toArray();
        Log::info('Subscriptions due for renewal today: ' . implode(', ', $subscriptionIds));

        $controller = app(Checkout::class);

        foreach ($subscriptions as $subscription) {
            $controller->renew($subscription->id);
        }
    }
}
