<?php

namespace App\Console\Commands\Subscription;

use App\Services\NotificationService;
use App\Subscription;
use Exception;
use Illuminate\Console\Command;

class ExpiringCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:expiring';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     * @throws Exception
     */
    public function handle(NotificationService $notificationService)
    {
        $entries = Subscription::query()
            ->active()
            ->where('subscription_type', 'yearly')
            ->where('issues', 0)
            ->where('canceled', false)
            ->get();


        foreach ($entries as $entry) {
            try {
                $notificationService->subscriptionExpired($entry);
            }catch (Exception $e) {
                $this->error($e->getMessage());
            }
        }

    }
}
