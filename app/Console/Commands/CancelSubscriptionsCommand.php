<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Subscription;

class CancelSubscriptionsCommand extends Command
{
    protected $signature = 'subscriptions:cancel';
    protected $description = 'Cancel subscriptions due to expire  7 days after their finish date';

    public function handle()
    {
        $sevenDaysAgo = now()->subDays(7);

        Subscription::query()->whereDate('finish', '<=', $sevenDaysAgo)
            ->where('canceled', 0)
            ->update(['canceled' => 1]);

        return 0;
    }
}
