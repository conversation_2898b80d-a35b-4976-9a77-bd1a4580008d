<?php

namespace App\Console\Commands;

use App\Subscription;
use Illuminate\Console\Command;
use App\Http\Controllers\SubscriptionController;
use Illuminate\Support\Facades\Log;

class RenewSubscriptions extends Command
{
    protected $signature = 'renew:subscriptions';
    protected $description = 'Renew subscriptions that are due today';

    public function handle(): void
    {
        // Get all subscriptions that should be renewed today

        $subscriptions = Subscription::query()
            ->where('issues', 0)
            ->where('canceled', 0)
            ->where('renew', 1)
            ->get();

        // If no subscriptions are found, log this information and return
        if ($subscriptions->isEmpty()) {
            Log::info('No subscriptions are due for renewal today.');
            return;
        }

        // Log the ids of subscriptions due for renewal
        $subscriptionIds = $subscriptions->pluck('id')->toArray();
        Log::info('Subscriptions due for renewal today: ' . implode(', ', $subscriptionIds));

        // Instantiate the controller containing the renew function
        $controller = app(SubscriptionController::class);

        foreach ($subscriptions as $subscription) {
            $controller->renew($subscription->id);
        }
    }
}
