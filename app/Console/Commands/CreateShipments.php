<?php

namespace App\Console\Commands;

use App\Subscription;
use App\SubscriptionMonth;
use Illuminate\Console\Command;

class CreateShipments extends Command
{
    protected $signature = 'create:shipments';
    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $month = SubscriptionMonth::getNext();

        $subscriptions = Subscription::query()
            ->with('order', 'customer')
            ->current()
            ->where('issues', '>', 0)
            ->where('subscription_month_id', '<=', $month->id)
            ->get();


        $subscriptions->each->createShipment($month);

        return 0;
    }
}
