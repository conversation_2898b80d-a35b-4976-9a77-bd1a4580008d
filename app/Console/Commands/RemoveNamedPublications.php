<?php

namespace App\Console\Commands;

use App\NamedPublication;
use Illuminate\Console\Command;

class RemoveNamedPublications extends Command
{
    protected $signature = 'publications:remove';
    protected $description = 'Remove Named Publications';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // \App\NamedPublication::truncate();
        NamedPublication::query()
            ->where('name', 'תשרי')
            ->whereRaw('date(created_at) = ?', [date('Y-m-d', mktime(0, 0, 0, 9, 5, 2023))])
            ->delete();
        return 0;
    }
}
