<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ShipStationService;

class TestShipStationConnection extends Command
{
    protected $signature = 'test:shipstation {productId}';
    protected $description = 'Test ShipStation API Connection';

    private ShipStationService $shipStationService;

    public function __construct(ShipStationService $shipStationService)
    {
        parent::__construct();
        $this->shipStationService = $shipStationService;
    }

    public function handle()
    {
        $product = $this->shipStationService->getProductById($this->argument('productId'));

        if ($product) {
            $this->info('Successfully retrieved product data from ShipStation.');
            $this->line(json_encode($product, JSON_PRETTY_PRINT));
        } else {
            $this->error('Failed to retrieve product data from ShipStation.');
        }
    }
}
