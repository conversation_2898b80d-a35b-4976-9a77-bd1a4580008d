<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Setting;

class Settings extends Command
{
    protected $signature = 'settings';
    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        Setting::updateOrCreate(['key' => 'reasons'], [
            'name' => 'Reasons For Notes',
            'type' => 'text',
            'value' => ''
        ]);

        Setting::updateOrCreate(['key' => 'additional_provider_emails'], [
            'name' => 'Additional Provider Emails',
            'type' => 'text',
            'value' => ''
        ]);

        Setting::updateOrCreate(['key' => 'hebrew_day_of_month_to_create_shipments'], [
            'name' => 'Hebrew day of month to create shipments',
            'type' =>  'number',
            'value' => 18
        ]);

        Setting::updateOrCreate(['key' => 'hebrew_day_of_month_to_create_first_attempt_orders'], [
            'name' => 'Hebrew day of month to create first attempt orders',
            'type' => 'number',
            'value' => 11
        ]);

        Setting::updateOrCreate(['key' => 'hebrew_day_of_month_to_create_second_attempt_orders'], [
            'name' => 'Hebrew day of month to create second attempt orders',
            'type' => 'number',
            'value' => 15
        ]);
        return true;
    }
}
