<?php

namespace App\Console\Commands;

use App\Services\SubscriptionService;
use Illuminate\Console\Command;

class UpdateSubscriptionFields extends Command
{
    protected $signature = 'command:updateSubs';
    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $subscriptionService = app()->make(SubscriptionService::class);
        $updatedIds = $subscriptionService->updatePublicationsToKislev();

        $this->info('Updated publication IDs: ' . implode(', ', $updatedIds['publications']));
        $this->info('Updated subscription IDs: ' . implode(', ', $updatedIds['subscriptions']));

        $this->info('Subscription fields updated successfully.');
    }

}
