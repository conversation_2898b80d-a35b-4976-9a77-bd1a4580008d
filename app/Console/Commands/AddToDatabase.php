<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

class AddToDatabase extends Command
{
    protected $signature = 'add-to-database';
    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->runOnDB('zones', 'original_set', 'json');
        $this->runOnDB('providers', 'only_changes', 'boolean');
    }

    public function runOnDB($table, $column, $type = 'string')
    {
        if (!Schema::hasColumn($table, $column)) {
            Schema::table($table, function (Blueprint $db) use ($column, $type) {
                $db->{$type}($column)->nullable();
            });
            $this->info("Added $column to $table");
        }
    }
}
