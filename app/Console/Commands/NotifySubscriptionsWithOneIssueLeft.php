<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Subscription;
use App\Services\NotificationService;

class NotifySubscriptionsWithOneIssueLeft extends Command
{
    protected $signature = 'notify:subscriptions';
    protected $description = 'Notify subscriptions with 0 issues left and renew is not turned on';
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    public function handle()
    {
        $subscriptions = Subscription::query()->where('issues', 1)->where('renew', 0)->get();

        foreach ($subscriptions as $subscription) {
            $response = $this->notificationService->sendSubscriptionExpiryNotification($subscription); // TODO notification

            $customer = $subscription->customer;

            if ($response) {
                $this->info("Notification sent to customer: {$customer->name}");
            } else {
                $this->error("Failed to send notification to customer: {$customer->name}");
            }
        }
    }
}
