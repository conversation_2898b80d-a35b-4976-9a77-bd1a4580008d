<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class HeartBeat extends Command
{
    protected $signature = 'heartbeat';
    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        dispatch(function () {
            Http::get('https://heartbeat.uptimerobot.com/m796339063-db2c276095f6b7b43f9609c9101395d3d7c28b30');
        });
        return 0;
    }
}
