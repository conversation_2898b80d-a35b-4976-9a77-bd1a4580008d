<?php

namespace App\Console\Commands;

use App\Zone;
use Illuminate\Console\Command;

class CreateRoutes extends Command
{
    protected $signature = 'create:subscriptionroutes';
    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        Zone::query()->where('route_4_me', 1)
            ->get()
            ->each
            ->createRoute4Me();
    }
}
