<?php

namespace App\Console\Commands;

use App\Exports\MonthlyExport;
use App\HebrewDate;
use App\Zone;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class EmailProviders extends Command
{
    protected $signature = 'email-providers';
    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $time = now()->subHour();
        Zone::query()
            ->where('cutoff_day', $time->dayOfWeek)
            ->where('cutoff_time', 'like', $time->format('H') . ':%')
            ->each(function ($zone) use ($time) {
                $start = $time->startOfWeek()->toDateString();

                Excel::store(
                    new MonthlyExport($zone->id, $start),
                    'exports/' . $start . '-subscriptions-' . $zone->name . '.xls',
                );

                $zone->addMedia(storage_path('app/exports/') . $start . '-subscriptions-' . $zone->name . '.xls')
                    ->withCustomProperties([
                        'date' => $start,
                        'parsha' => HebrewDate::getParshaString($time->startOfWeek()),
                    ])->toMediaCollection('exports', 'exports');
            });

        // the emails are send out in EmailProviderAfterUpload 
        return 0;
    }
}
