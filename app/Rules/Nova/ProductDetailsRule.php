<?php

namespace App\Rules\Nova;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ProductDetailsRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {

        if(request()->input('status') === "published") {
            $data = json_decode($value, true);

            foreach ($data as $value) {
                if (!is_numeric($value)) {
                    $fail("validation.numeric")->translate();
                }
                if (empty($value) || $value <= 0) {
                    $fail("validation.details")->translate();
                }
            }
        }
    }
}
