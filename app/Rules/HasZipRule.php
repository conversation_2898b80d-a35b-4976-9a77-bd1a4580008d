<?php

namespace App\Rules;

use App\Zone;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class HasZipRule implements ValidationRule
{

    protected bool $isEmpty = false;

    public function __construct($isEmpty = false)
    {
        $this->isEmpty = $isEmpty;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if($this->isEmpty && empty($value)) {
            return;
        }

        if (!Zone::whereJsonContains('zips', (string)$value)
            ->orwhereJsonContains('zips', substr($value, 0, 3))
            ->exists()) {
            $fail('There is no such zip code!');
        }
    }
}
