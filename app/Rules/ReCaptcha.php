<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ReCaptcha implements Rule
{
    /**
     * Run the validation rule.
     *
     */
    public function passes($attribute, $value)
    {
        $response = Http::get("https://www.google.com/recaptcha/api/siteverify", [
            'secret' => config('services.recaptcha.secret'),
            'response' => $value
        ]);

        Log::info($response->json());
        return $response->json()["success"] && $response->json()["score"] > .5;
    }

    public function message(): string
    {
        return 'The google recaptcha is required.';
    }
}
