<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lunar\Facades\Payments;
use USAePay\API;
use USAePay\Transactions\Post;
use USAePay\Transactions;
use USAePay\Tokens;


include storage_path('includes/epay/master/usaepay.php');

class CreditCard extends Model
{
    public $guarded = [];

    public function getFrontEndAttribute()
    {
        return [
            'id' => $this->id,
            'zip' => $this->zip,
            'type' => $this->type,
            'last_four' => $this->last_four,
        ];
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    private static function setAuthentication()
    {
        if (
            env('APP_ENV') === 'local'
            || env('APP_URL') == 'https://staging.vinderkind.com'
        ) {
            Log::debug('here');
            API::setSubdomain('sandbox');

            API::setAuthentication(
                '_01m7ax7m0qGH523F8vk5uj2it9cQNL6',
                '1234'
            );
        } else {
            API::setAuthentication(env('USAEPAY_API_KEY'), env('USAEPAY_API_PIN'));
        }
    }

    public function credit_card_payments()
    {
        return $this->hasMany(CreditCardPayment::class);
    }

    // this is the last (dummy) feild in Nova so this->attributes has everything alreay
    public function setCustomAttribute($value)
    {
//        $token = self::getToken([
//            'card' => preg_replace('/[^0-9]/', '', $this->attributes['last_four']),
//            'exp' => preg_replace('/[^0-9]/', '', $this->attributes['expires']),
//        ]);
//
//        $this->attributes['token'] = $token;
//        $this->attributes['last_four'] = substr($this->attributes['last_four'], -4);
    }

    public static function createFromCard($data, $customer)
    {
        $token = self::getTokenSubForm([
            'card' => preg_replace('/[^0-9]/', '', data_get($data, 'card') ?? data_get($data, 'card_number')),
            'exp' => preg_replace('/[^0-9]/', '', data_get($data, 'expiration') ?? data_get($data, 'expires')),
            'cvc' => $data['cvc'] ?? null,
            'zip' => $data['zip'],
        ]);

        return self::create([
            'token' => $token['token'],
            'customer_id' => $customer->id,
            'zip' => data_get($data, 'zip'),
            'type' => data_get($data, 'type'),
            'expires' => data_get($data, 'expires'),
            'last_four' => substr(data_get($data, 'card'), -4),
            'security_code' => data_get($data, 'cvc'),
        ]);
    }

    public function charge($amount)
    {
        abort(400, 'outdated method!');
        //                $paymentDriver = Payments::driver($creditCard->payment_method);
        //                $paymentDriver->order($order);
        //                $paymentDriver->setAmount($diff);
        //                $paymentDriver->charge();
    }

    public function refund($amount, $orderId = null)
    {
        Log::info("Refund process started for amount: $amount");
        $payment = $this->credit_card_payments()->latest('created_at')->first();
        $paymentDriver = Payments::driver($this->payment_method);
        $response = $paymentDriver->refundPayment($payment, $this->customer, $amount);
        if($response->success === true){
            Log::info("Transaction Approved with refnum: {$payment->refnum}");
            $payment = $this->credit_card_payments()->create([
                'amount' => $amount / 100,
                'charges' => [
                    [
                        'Date' => now(),
                        'RefNum' => $payment->refnum,
                        'Authcode' => $payment->refnum,
                        'Amount' => $amount
                    ]
                ],
                'refnum' => $payment->refnum,
                'refunds' => 'This is a refund'
            ]);

            Payment::create([
                'amount' => $amount / 100,
                'order_id' => $orderId,
                'type' => 'card',
                'credit_card_payment_id' => $payment->id,
                'credit_card_id' => $this->id,
                'payment_type' => 'Credit Card',
            ]);

            Log::info('Payment record created.');

            return (object)array_merge((array)$payment->toArray(), ['result' => $response->message]);
        }else{
            if (request()->is('nova-api/*')) {
                Log::error("Transaction failed with message: {$response}");
                abort(500, $response->message);
            }
            abort(400, $response->message);
        }
    }


    public static function getToken($data)
    {
        self::setAuthentication();

        $expiration = str_replace([' ', '/'], '', $data['exp']);

        $postData = [
            'command' => 'cc:save',
            'amount' => 0.01,
            'creditcard' => [
                'number' => $data['card'],
                'expiration' => $expiration,
            ],
        ];

        try {
            $response = Transactions::post($postData);
            Log::debug('Token');
            Log::debug(json_encode($response));

            if (isset($response->result_code) && $response->result_code === 'D') {
                Log::debug('here');
                abort(400, $response->error);
            }

            if (isset($response->error) && isset($response->result_code) && $response->result_code === 'E') {
                Log::debug('or here');
                abort(500, $response->error);
            }

            if (isset($response->savedcard)) {
                Log::info('Token obtained');
                return $response->savedcard->key;
            } else {
                abort(request()->is('nova-api/*') ? 500 : 400, $response->error ?? 'Error processing the transaction');
            }
        } catch (\Exception $e) {
            Log::error('Error tokenizing card', ['error' => $e->getMessage()]);
            abort(500, $e->getMessage());
        }
    }


    public static function getTokenSubForm($data)
    {
        self::setAuthentication();

        $expiration = str_replace([' ', '/'], '', $data['exp']);

        $postData = [
            'command' => 'cc:authonly',
            'amount' => 1.00,
            'creditcard' => [
                'number' => $data['card'],
                'expiration' => $expiration,
            ],
        ];

        try {
            $response = Tokens::post($postData);

            if (isset($response->result_code) && $response->result_code === 'E') {
                Log::info('la respuesta de una tarjeta rechazada', (array)$response);
                abort(400, 'Card is declined');
            }

            if (isset($response->key)) {
                Log::info('Token obtained');
                return ['token' => $response->key];
            } else {
                abort(request()->is('nova-api/*') ? 500 : 400, $response->error ?? 'Error processing the transaction');
            }
        } catch (\Exception $e) {
            Log::error('Error tokenizing card', ['error' => $e->getMessage()]);
            abort(500, 'Error tokenizing card');
        }
    }

    private static function GetTran()
    {
        $tran = new \umTransaction;

        $tran->key = ('_01m7ax7m0qGH523F8vk5uj2it9cQNL6');
        $tran->pin = ('1234');

        // $tran->usesandbox = true;
        $tran->testmode = 0;

        return $tran;
    }

    private static function ProccessAndReturn($tran)
    {
        if ($tran->Process()) {
            return [
                'Approved' => true,
                'Token' => $tran->cardref,
                'RefNum' => $tran->refnum,
                'Authcode' => $tran->authcode,
                'Message' => 'Request Approved',
                'AVS Result' => $tran->avs_result,
                'Cvv2 Result' => $tran->cvv2_result,
            ];
        } else {
            return [
                'Approved' => false,
                'Reason' => $tran->error,
                'Message' => 'Card Declined',
                'Curl Error' => @$tran->curlerror ? $tran->curlerror : '',
            ];
        }
    }

    public function applyToken()
    {
//        $card = request('card');

//        $token = self::getToken(request()->merge(['card' => $card, 'exp' => request('expires')]));
//
//        $this->withoutEvents(function () use ($token, $card) {
//            $this->update([
//                'token' => $token,
//                'last_four' => substr($card, -4)
//            ]);
//        });
    }

    protected static function boot()
    {
        parent::boot();

//        static::created(function ($credit_card) {
//            // for nova we use setCustomAttribute
//            if (request()->is('nova-api/*')) {
//                // $credit_card->applyToken();
//            }
//        });
    }
}
