<?php

namespace App;

use App\Services\CreateLabelForOrderService;
use App\Services\ShipStationOrderService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Shipment extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'shipped_at' => 'datetime',
        'canceled_at' => 'datetime',
    ];

    public function getVerifiedAddress() {
        
        if($this->subscription) {
            return collect($this->subscription->meta)->only('lat', 'lng', 'address')->toArray();
        }
        if($this->order) {
            return collect($this->order->shippingAddress->meta)->only('lat', 'lng', 'address')->toArray();
        }
    }

    public function getShippingNameAttribute()
    {
        if($this->subscription) {
            return  $this->subscription?->name;
        }
        if($this->order) {
            return  $this->order->shippingAddress?->last_name;
        }
        return null;
    }

    public function getShippingPhoneAttribute()
    {
        if($this->subscription) {
            return  $this->subscription?->customer?->phone;
        }
        if($this->order) {
            return data_get($this->order, 'meta.request.order.customer.phone');
        }
        return null;
    }

    public function getAddressStringAttribute()
    {
        $data = [];
        if($this->subscription) {
            $data = [
                'line_one' => $this->subscription?->address_line_1,
                'line_two' => $this->subscription?->address_line_2,
                'city' => $this->subscription?->city,
                'state' => $this->subscription?->state,
                'postcode' => $this->subscription?->postal_code,
            ];   
        }
        else if($this->order) {
            $data = [
                'line_one' => $this->order->shippingAddress?->line_one,
                'line_two' => $this->order->shippingAddress?->line_two,
                'city' => $this->order->shippingAddress?->city,
                'state' => $this->order->shippingAddress?->state,
                'postcode' => $this->order->shippingAddress?->postcode,
            ];   
        }
        return collect($data)->filter()->join(', ');
    }
    
    public function getAddressArrayAttribute()
    {
        if($this->subscription) {
            return [
                'line_one' => $this->subscription?->address_line_1,
                'line_two' => $this->subscription?->address_line_2,
                'city' => $this->subscription?->city,
                'state' => $this->subscription?->state,
                'postcode' => $this->subscription?->postal_code,
            ];   
        }
        if($this->order) {
            return [
                'line_one' => $this->order->shippingAddress?->line_one,
                'line_two' => $this->order->shippingAddress?->line_two,
                'city' => $this->order->shippingAddress?->city,
                'state' => $this->order->shippingAddress?->state,
                'postcode' => $this->order->shippingAddress?->postcode,
            ];   
        }
    }    


    // Relationships
    public function address()
    {
        return $this->belongsTo(Lunar\Address::class);
    }

    public function order()
    {
        return $this->belongsTo(Lunar\Order::class);
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    public function zone()
    {
        return $this->belongsTo(Zone::class);
    }

    public function purchasable()
    {
        return $this->morphTo();
    }

    public function createLabel($sent = false)
    {
        $order = $this->order;
        $address = $this->getAddressArrayAttribute();

        $orderAddress = [
            'street1' => data_get($address, 'line_one'),
            'street2' => data_get($address, 'line_two'),
            'city' => data_get($address, 'city'),
            'state' => data_get($address, 'state'),
            'postalCode' => data_get($address, 'postcode'),
            'country' => "US",
        ];

        $addressData = array_merge(
            $orderAddress,
            [
                'name' => $this->getShippingNameAttribute(),
                'phone' => $this->getShippingPhoneAttribute(),
                'residential' => false,
            ]
        );

        $config = \App\ShipStationConfiguration::find(1);

        $shipStationOrderData = [
            'orderNumber' => $this->id,
            'orderDate' => $this->created_at->toIso8601String(),
            'orderStatus' => 'awaiting_shipment',
            'billTo' => $addressData,
            'shipTo' => $addressData,

            "weight" => [
                "value" => $this->purchasable->weight_value,
                "units" => 'ounces',
                // "WeightUnits" => "1",
            ],
            "dimensions" => [
                "length" => $this->purchasable->length_value,
                "width" => $this->purchasable->width_value,
                "height" => $this->purchasable->height_value,
                "units" => 'inches'
            ],
            "testLabel" => env('SHIPSTATION_TEST')
        ];
        $result = ShipStationOrderService::createOrder($shipStationOrderData);
        $orderId = $result['orderId'];

        // $this->update([
        //     'order_id' => $result['orderId'] ?? null,
        // ]);

        if ($sent) {
            $this->update([
                'shipped_at' => now()
            ]);
        }

        $addressDataShipFrom = [
            'name' => 'Vinderkind',
            'street1' => '1303 53rd street',
            'street2' => '1303 53rd street',
            'city' => 'brooklyn',
            'state' => 'NY',
            'postalCode' => '11219',
            'country' => 'US',
            'phone' => '3473519597',
            'residential' => true,
        ];

        $labelData = [
            "orderId" => $result['orderId'],
            "carrierCode" => $config->carrier_code,
            "serviceCode" => $config->service_code,
            "confirmation" => "delivery",
            "shipDate" => today()->addDay()->toDateString(),
            "packageCode" => $config->package_code,
            "weight" => [
                "value" => $this->purchasable->weight_value,
                "units" => 'ounces'
            ],
            "dimensions" => [
                "length" => $this->purchasable->length_value,
                "width" => $this->purchasable->width_value,
                "height" => $this->purchasable->height_value,
                "units" => 'inches'
            ],
            "testLabel" => env('SHIPSTATION_TEST'),
            'shipTo' => $addressData,
            'insuranceOptions' => [
                'provider' => 'parcelguard',
                'insureShipment' => true,
                'insuredValue' => '0.01',
            ],
        ];

        // Create label for the order
        $labelResult = resolve(CreateLabelForOrderService::class)->createLabelForOrder($labelData, 1, $this->id);

        $trackingData = [
            'user_name' => $this->getShippingNameAttribute(),
            'tracking_name' => $labelResult['trackingNumber'],
            'issue' => $this->purchasable->product->translateAttribute('name'),
            'tracking_url' => 'https://tools.usps.com/go/TrackConfirmAction_input?strOrigTrackNum=' . $labelResult['trackingNumber'],
        ];

        $this->update([
            'tracking_number' => $trackingData['tracking_url']
        ]);
        // (new \App\Emails\TrackingInformation)
        //      ->withData($trackingData)
        //      ->sendTo();
    }
}
