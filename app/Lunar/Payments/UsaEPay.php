<?php

namespace App\Lunar\Payments;

use App\CreditCard;
use App\CreditCardPayment;
use App\Customer;
use App\Payment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lunar\Base\DataTransferObjects\PaymentCapture;
use Lunar\Base\DataTransferObjects\PaymentRefund;
use Lunar\Base\DataTransferObjects\PaymentAuthorize;
use Lunar\Events\PaymentAttemptEvent;
use Lunar\Models\Transaction;
use Lunar\PaymentTypes\AbstractPayment;
use Stripe\Exception\ApiErrorException;
use USAePay\API;
use USAePay\Tokens;
use USAePay\Transactions;


class UsaEPay extends AbstractPayment
{

    private $amount = null;

    public function __construct()
    {
        if (request()->ip() == '**************' || env('APP_ENV') === 'local') {
            API::setSubdomain('sandbox');
            API::setAuthentication(
                config('services.usaepay.local.api_key'),
                config('services.usaepay.local.api_pin'),
            );
        } else {
            API::setAuthentication(
                config('services.usaepay.production.api_key'),
                config('services.usaepay.production.api_pin'),
            );
        }
    }

    /**
     * @param array $payment [number, cvv, exp, zip, address_zip, exp_month, exp_year]
     * @param $customer
     * @return array|void
     * @throws \USAePay\Exception\CurlException
     * @throws \USAePay\Exception\SDKException
     * @throws \USAePay\Exception\ueException
     */
    public function getToken(
        array $payment,
        $customer,
        bool $isCreating = true,
        bool $withoutCard = false
    ){

        if($withoutCard){
            return [];
        }
        $postData = [
            'command' => 'cc:save',
            'creditcard' => [
                'number' => data_get($payment, 'cardNumber'),
                'expiration' => preg_replace('/[^0-9]/', '', data_get($payment, 'exp') ?? data_get($payment, 'expires')),
                'cvc' => data_get($payment, 'cvv'),
                'avs_postalcode' => data_get($payment, 'zip'),
            ],
        ];

        logToPath('UsaEPay', $postData);

        try {
            $response = Tokens::post($postData);
        } catch (\Throwable $th) {
            abort(400, $th->getMessage());
        }
        logToPath('UsaEPay', (array)$response);

        if (isset($response->key)) {
            if($isCreating) {
                $creditCardId = DB::table('credit_cards')->insertGetId([
                    'token' => $response->key,
                    'expires' => $postData['creditcard']['expiration'],
                    'type' => $response->type,
                    'security_code' => $postData['creditcard']['cvc'],
                    'last_four' => substr($postData['creditcard']['number'], -4),
                    'zip' => $postData['creditcard']['avs_postalcode'],
                    'customer_id' => $customer->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'payment_method' => 'usaepay'
                ]);
                return [
                    'creditCardId' => $creditCardId,
                    'token' => $response->key,
                    'last_four' => substr($postData['creditcard']['number'], -4),
                ];
            }
            return [
                'token' => $response->key,
                'last_four' => substr($postData['creditcard']['number'], -4),
            ];
        } else {
            abort(400, 'No token found in the response');
        }
    }

    public function setAmount(float $amount)
    {
        $this->amount = $amount;
    }

    public function charge()
    {
        if (!$this->order) {
            if (!$this->order = $this->cart->order) {
                $this->order = $this->cart->createOrder();
            }
        }

        $creditCardId = data_get($this->order->meta->toArray(), 'creditCardData.creditCardId');
        $creditCard = CreditCard::find($creditCardId);

        $amount = $this->amount ?? $this->order->total->decimal();
        $postData = [
            'amount' => $amount,
            'command' => 'cc:sale',
            'creditcard' => [
                'number' => $creditCard->token,
                'expiration' => '0000',
                'cvc' => $creditCard->security_code,
                'avs_postalcode' => $creditCard->zip,
            ],
        ];

        $response = Transactions::post($postData);
        Log::channel('charges')->info(json_encode($response));


        if (!isset($response->error) && $response->result === "Approved") {
            $creditCardPayment = CreditCardPayment::create([
                'credit_card_id' => $creditCard->id,
                'amount' => $amount,
                'status' => 'active',
                'charges' => [
                    [
                        'Date' => now(),
                        'RefNum' => $response->refnum, // Corrected the case
                        'Authcode' => $response->authcode, // Corrected the case
                        'Amount' => $amount,
                    ]
                ],
                'refnum' => $response->refnum // Corrected the case
            ]);

            Payment::create([
                'amount' => $amount,
                'order_id' => $this->order->id,
                'type' => 'card',
                'credit_card_payment_id' => $creditCardPayment->id,
                'credit_card_id' => $creditCard->id,
                'payment_type' => 'Credit Card',
            ]);

            return new PaymentCapture(true);
        }
        return new PaymentCapture(false);

    }

    /**
     * @param CreditCardPayment $payment
     * @param Customer $customer
     * @param int $amount
     * @param $notes
     * @return PaymentRefund
     * @throws \USAePay\Exception\CurlException
     * @throws \USAePay\Exception\SDKException
     * @throws \USAePay\Exception\ueException
     */
    public function refundPayment(CreditCardPayment $payment, Customer $customer, int $amount = 0, $notes = null): PaymentRefund
    {
        if($payment?->refnum){
            $data = [
                'command' => 'cc:refund',
                'amount' => $amount/100,
                'refnum' => $payment?->refnum,
                'creditcard' => [
                    'cardholder' => $customer->name,
                    'number' => $payment->token,
                    'expiration' => $payment->security_code,
                    'avs_zip' => $payment->zip
                ],
            ];

            $response = Transactions::post($data);

            if (!isset($response->error) && $response->result === "Approved") {
                return new PaymentRefund(true);
            }
        }

        return new PaymentRefund(false);
    }

    /**
     * {@inheritDoc}
     */
    public function authorize(): PaymentAuthorize
    {
        if (!$this->order) {
            if (!$this->order = $this->cart->order) {
                $this->order = $this->cart->createOrder();
            }
        }

        // ...

        $response = new PaymentAuthorize(
            success: true,
            message: 'The payment was successful',
            orderId: $this->order->id,
            paymentType: 'card'
        );

        PaymentAttemptEvent::dispatch($response);

        return $response;
    }

    /**
     * {@inheritDoc}
     */
    public function refund(Transaction $transaction, int $amount = 0, $notes = null): PaymentRefund
    {
        // ...
        return new PaymentRefund(true);
    }

    /**
     * {@inheritDoc}
     */
    public function capture(Transaction $transaction, $amount = 0): PaymentCapture
    {
        // ...
        return new PaymentCapture(true);
    }
}