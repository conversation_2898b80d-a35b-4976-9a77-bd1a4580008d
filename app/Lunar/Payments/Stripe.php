<?php

namespace App\Lunar\Payments;

use App\CreditCard;
use App\CreditCardPayment;
use App\Customer;
use App\Payment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lunar\Base\DataTransferObjects\PaymentCapture;
use Lunar\Base\DataTransferObjects\PaymentRefund;
use Lunar\Base\DataTransferObjects\PaymentAuthorize;
use Lunar\Events\PaymentAttemptEvent;
use Lunar\Models\Transaction;
use Lunar\PaymentTypes\AbstractPayment;
use phpDocumentor\Reflection\DocBlock\Tags\Throws;
use PHPUnit\Exception;
use Stripe\Exception\ApiErrorException;
use Stripe\StripeClient;
use function Symfony\Component\String\b;

class Stripe extends AbstractPayment
{
    private StripeClient $paymentClient;

    private $amount = null;

    public function __construct()
    {
        $this->paymentClient = new StripeClient(config('services.stripe.key'));
    }


    /**
     * @param $token
     * @param $type
     * @param $lastFour
     * @param $zip
     * @param $customerId
     * @param $expires
     * @return array
     */
    private function makeCreditCardPayment(
        $token,
        $type,
        $lastFour,
        $zip,
        $customerId,
        $expires
    ): array
    {
        $creditCardId = DB::table('credit_cards')->insertGetId([
            'token' => $token,
            'type' => $type,
            'last_four' => $lastFour,
            'zip' => $zip,
            'customer_id' => $customerId,
            'expires' => $expires,
            'created_at' => now(),
            'updated_at' => now(),
            'payment_method' => 'stripe'
        ]);
        return [
            'creditCardId' => $creditCardId,
            'token' => $token,
            'last_four' => $lastFour,
        ];
    }

    private function isMatchingCard($payment, $source): bool
    {
        return $payment['last4'] === $source['last4']
            && $payment['brand'] === $source['brand']
            && $payment['address_zip'] === $source['address_zip']
            && $payment['exp_month'] === $source['exp_month']
            && $payment['token'] === $source['token']
            && $payment['exp_year'] === $source['exp_year'];
    }

    /**
     * @param array $payment [token, card, brand, last4, address_zip, exp_month, exp_year]
     * @param $customer
     * @param bool $withoutCard
     * @return array
     * @throws ApiErrorException
     */
    public function getToken(
        array $payment,
        $customer,
        bool $isCreating = true,
        bool $withoutCard = false
    )
    {
        if($withoutCard){
            if (!$customer->stripe_token) {
                $response = $this->paymentClient->customers->create([
                    'name' => $customer->name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                    'metadata' => [
                        'id' => $customer->id,
                    ]
                ]);
                $customer->update(['stripe_token' => $response['id']]);
            }
            return [];
        }

        if ($customer->stripe_token) {


            if(isset($payment['id'])){
                $card = CreditCard::find($payment['id']);

                return [
                    'creditCardId' => $card->id,
                    'token' => $card->token,
                    'last_four' => $card->last_four,
                ];
            }


            $creditCards = $customer->cards;

            $sources = $this->paymentClient->customers->allSources(
                $customer->stripe_token,
                ['object' => 'card']
            );

            $checkSource = false;
            if($creditCards){
                foreach($sources['data'] as $source){
                    if($this->isMatchingCard($payment, $source)){
                        $checkSource = true;
                        break;
                    }
                }
            }

            if($checkSource){
                $card = $creditCards
                    ->whereNull('security_code')
                    ->where('last_four', $payment['last4'])
                    ->where('zip', $payment['address_zip'])
                    ->where('expires', expiresMake($payment['exp_month'],$payment['exp_year']))
                    ->where('type', $payment['brand'])
                    ->first();

                if($card){
                    return [
                        'creditCardId' => $card->id,
                        'token' => $card->token,
                        'last_four' => $card->last_four,
                    ];
                }else{
                    return $this->makeCreditCardPayment(
                        token: $payment['card'],
                        type: $payment['brand'],
                        lastFour: $payment['last4'],
                        zip: $payment['address_zip'],
                        customerId: $customer->id,
                        expires: expiresMake($payment['exp_month'],$payment['exp_year'])
                    );
                }
            }

            try {
                $response = $this->paymentClient->customers->createSource($customer->stripe_token, ['source' => $payment['token']]);
                return $this->makeCreditCardPayment(
                    token: $response['id'],
                    type: $response['brand'],
                    lastFour: $response['last4'],
                    zip: $response['address_zip'],
                    customerId: $customer->id,
                    expires: expiresMake($response['exp_month'],$response['exp_year'])
                );
            }catch (\Exception $exception){
                abort(422, $exception->getMessage());
            }
        }

        try {
            $response = $this->paymentClient->customers->create([
                'name' => $customer->name,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'metadata' => [
                    'id' => $customer->id,
                ],
                'source' => $payment['token']
            ]);

            $customer->update(['stripe_token' => $response['id']]);


            return $this->makeCreditCardPayment(
                $response['default_source'],
                type: $payment['brand'],
                lastFour: $payment['last4'],
                zip: $payment['address_zip'],
                customerId: $customer->id,
                expires: expiresMake($payment['exp_month'],$payment['exp_year'])
            );
        }catch (\Exception $exception){
            abort(422, $exception->getMessage());
        }
    }

    public function detachCard($card): void
    {
        if($card->customer->stripe_token && !$card->security_code) {
            $response = $this->paymentClient->customers->deleteSource(
                $card->customer->stripe_token,
                $card->token,
                []
            )  ;

//            Log::info($response);
        }
    }

    public function attachCard($model, array $payment) // token, card, brand, last4, address_zip, exp_month, exp_year
    {
        if ($model->stripe_token) {
            try {
                $response = $this->paymentClient->customers->createSource($model->stripe_token, ['source' => $payment['token']]);

                return [
                    'token' => $response['id'],
                    'expires' => expiresMake($response['exp_month'],$response['exp_year']),
                    'type' => $response['brand'],
                    'last_four' => $response['last4'],
                    'zip' => $response['address_zip'],
                ];
            }catch (\Exception $exception){
                abort(422, $exception->getMessage());
            }
        }

        try {
            $response = $this->paymentClient->customers->create([
                'name' => $model->name,
                'email' => $model->email,
                'phone' => $model->phone,
                'metadata' => [
                    'id' => $model->id,
                ],
                'source' => $payment['token']
            ]);
            $model->update(['stripe_token' => $response['id']]);

            return [
                'token' => $response['default_source'],
                'expires' => $payment['expires'],
                'type' => $payment['type'],
                'last_four' => $payment['last_four'],
                'zip' => $payment['zip'],
            ];
        }catch (\Exception $exception){
            abort(422, $exception->getMessage());
        }
    }

    public function setAmount(float $amount)
    {
        $this->amount = $amount;
    }

    /**
     * @return PaymentCapture
     * @throws ApiErrorException
     */
    public function charge()
    {
        if (!$this->order) {
            if (!$this->order = $this->cart->order) {
                $this->order = $this->cart->createOrder();
            }
        }

        $creditCardId = data_get($this->order->meta->toArray(), 'creditCardData.creditCardId');
        $creditCard = CreditCard::find($creditCardId);

        $amount = $this->amount ?? $this->order->total->decimal();


        $response = $this->paymentClient->charges->create([
            'amount' => intval($amount*100),
            'currency' => 'usd',
            'source' => $creditCard->token, //$responseToken['id'],
            'customer' => $this->order->customer->stripe_token,
        ]);

        Log::channel('charges')->info(json_encode($response));

        if (!isset($response->error) && $response->status === "succeeded") {

            $creditCardPayment = CreditCardPayment::create([
                'credit_card_id' => $creditCard->id,
                'amount' => $amount,
                'status' => 'active',
                'charges' => [
                    [
                        'Date' => now(),
                        'RefNum' => $response->id, // Corrected the case
                        'Authcode' => $response->id, // Corrected the case
                        'Amount' => $amount,
                    ]
                ],
                'refnum' => $response->id // Corrected the case
            ]);

            Payment::create([
                'amount' => $amount,
                'order_id' => $this->order->id,
                'type' => 'card',
                'credit_card_payment_id' => $creditCardPayment->id,
                'credit_card_id' => $creditCard->id,
                'payment_type' => 'Credit Card',
            ]);


            return new PaymentCapture(true);
        }
        return new PaymentCapture(false);

    }

    /**
     * {@inheritDoc}
     */
    public function authorize(): PaymentAuthorize
    {
        if (!$this->order) {
            if (!$this->order = $this->cart->order) {
                $this->order = $this->cart->createOrder();
            }
        }

        // ...

        $response = new PaymentAuthorize(
            success: true,
            message: 'The payment was successful',
            orderId: $this->order->id,
            paymentType: 'card'
        );

        PaymentAttemptEvent::dispatch($response);

        return $response;
    }

    /**
     * @param CreditCardPayment $payment
     * @param Customer $customer
     * @param int $amount
     * @param $notes
     * @return PaymentRefund
     * @throws ApiErrorException
     */
    public function refundPayment(CreditCardPayment $payment, Customer $customer, int $amount = 0, $notes = null): PaymentRefund
    {
        if($payment?->refnum){
            $response = $this->paymentClient->refunds->create(['charge' => $payment?->refnum, 'amount' => $amount]);

            if (!isset($response->error) && $response->status === "succeeded") {
                return new PaymentRefund(true);
            }
        }

        return new PaymentRefund(false);
    }

    /**
     * @param Transaction $transaction
     * @param $amount
     * @return PaymentCapture
     */
    public function capture(Transaction $transaction, $amount = 0): PaymentCapture
    {
        // ...
        return new PaymentCapture(true);
    }


    /**
     * @param Transaction $transaction
     * @param int $amount
     * @param $notes
     * @return PaymentRefund
     */
    public function refund(Transaction $transaction, int $amount = 0, $notes = null): PaymentRefund
    {
        // ...
        return new PaymentRefund(true);
    }
}