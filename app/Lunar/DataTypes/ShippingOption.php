<?php

namespace App\Lunar\DataTypes;

use App\Zone;
use Illuminate\Pipeline\Pipeline;
use Lunar\DataTypes\Price;
use Lunar\DataTypes\ShippingOption as BaseShippingOption;
use Lunar\Exceptions\InvalidDataTypeValueException;
use Lunar\Models\TaxClass;

class ShippingOption extends BaseShippingOption
{
    /**
     * Cart sub total lines
     *
     * @var int
     */
    private int $cartSubTotal = 0;

    /**
     * @param $cart
     * @param string $name
     * @param string|null $description
     * @param string $identifier
     * @param $taxClass
     * @param string|null $taxReference
     * @param string|null $option
     * @param bool $collect
     * @param array|null $meta
     * @throws InvalidDataTypeValueException
     */

    public function __construct(
        public          $cart,
        public string   $name,
        public ?string  $description,
        public string   $identifier,
        public ?string  $taxReference = null,
        public ?string  $option = null,
        public bool     $collect = false,
        public ?array   $meta = null,
    ) {
        $taxClass = TaxClass::query()->first();

        parent::__construct(
            name: $this->name,
            description: $this->description,
            identifier: $this->identifier,
            price: new Price(0, $this->cart->currency, 1),
            taxClass: $taxClass,
            taxReference: $this->taxReference,
            option: $this->option,
            collect: $this->collect,
            meta: $this->meta
        );

        $this->cartCalculationHandler();
        $this->shippingOptionHandler();
    }

    /**
     * @param $price
     * @return void
     * @throws InvalidDataTypeValueException
     */
    protected function setPrice($price): void
    {
        $this->price = new Price((int)$price, $this->cart->currency, 1);
    }

    /**
     * @return void
     * @throws InvalidDataTypeValueException
     */
    protected function shippingOptionHandler(): void
    {
        $priceBasic = 0;

        if($this->cart->isShippable()){
            if(!$this->cart->shippingAddress){
                $this->setPrice(0);
                return;
            }
            $shippingAddress = $this->cart->shippingAddress;
            $entryZone =  Zone::getZip($shippingAddress?->postcode);


            if($entryZone?->free_shipping && $this->cartSubTotal >= $entryZone->free_shipping_price){
                $this->setPrice(0);
            }else{
                $this->setPrice(setting()->getValue('default_shipping_price'));
            }
            return;
        }
        $this->setPrice($priceBasic);
    }

    /**
     * Custom recalculate sub total cart
     *
     * @return void
     */
    protected function cartCalculationHandler(): void
    {
        foreach ($this->cart->lines as $line) {
            $cartLine = app(Pipeline::class)
                ->send($line)
                ->through(
                    config('lunar.cart.pipelines.cart_lines', [])
                )->thenReturn(function ($cartLine) {
                    $cartLine->cacheProperties();

                    return $cartLine;
                });

            $unitPrice = $cartLine->unitPrice->unitDecimal(false) * $this->cart->currency->factor;

            $subTotal = (int) round($unitPrice * $cartLine->quantity, $this->cart->currency->decimal_places);

            $this->cartSubTotal += $subTotal;
        }
    }
}