<?php

namespace App\Lunar\Actions\Carts;

use Lunar\Actions\AbstractAction;
use Lunar\Actions\Carts\GetExistingCartLine;
use Lunar\Base\Purchasable;
use Lunar\Exceptions\InvalidCartLineQuantityException;
use Lunar\Models\Cart;

class AddOrUpdatePurchasable extends AbstractAction
{
    public function execute(
        Cart        $cart,
        Purchasable $purchasable,
        int         $quantity = 1,
        array       $meta = []
    ): self
    {
        throw_if(!$quantity, InvalidCartLineQuantityException::class);

        $existing = app(
            config('lunar.cart.actions.get_existing_cart_line', GetExistingCartLine::class)
        )->execute(
            cart: $cart,
            purchasable: $purchasable,
            meta: $meta
        );

        if ($existing) {
            $existing->update([
                'quantity' => $quantity,
            ]);

            return $this;
        }

        $cart->lines()->create([
            'purchasable_id' => $purchasable->id,
            'purchasable_type' => $purchasable->getMorphClass(),
            'quantity' => $quantity,
            'meta' => $meta,
        ]);

        return $this;
    }

}