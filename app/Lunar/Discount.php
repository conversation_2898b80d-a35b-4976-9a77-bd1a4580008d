<?php

namespace App\Lunar;

use Lunar\Models\ProductVariant;
use Lunar\Models\Product;

class Discount extends \Lunar\Models\Discount
{
    protected string $morphClass = \Lunar\Models\Discount::class;

    public function getExclusionamountoffAttribute(): bool
    {
        return $this->purchasableExclusions->count() > 0;
    }

    public function getLimitationsAttribute()
    {
        //nova attribute
        return $this->purchasableLimitations->filter(function ($item) {
            return $item->purchasable_type == Product::class;
        })->pluck('purchasable_id')->toArray();
    }

    public function getExclusionsAttribute()
    {
        //nova attribute
        return $this->purchasableExclusions->filter(function ($item) {
            return $item->purchasable_type == Product::class;
        })->pluck('purchasable_id')->toArray();
    }
    public function getRewardsAttribute()
    {
        //nova attribute
        return $this->purchasableRewards->filter(function ($item) {
            return $item->purchasable_type == Product::class;
        })->pluck('purchasable_id')->toArray();
    }
    public function getConditionsAttribute()
    {
        //nova attribute
        return $this->purchasableConditions->filter(function ($item) {
            return $item->purchasable_type == Product::class;
        })->pluck('purchasable_id')->toArray();
    }

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'data' => 'array',
        'enabled' => 'boolean',
    ];


    public function purchasableProducts(): \Illuminate\Database\Eloquent\Relations\MorphToMany
    {
        return $this->morphedByMany(
            Product::class,
            'purchasable',
            'lunar_discount_purchasables',
        )
            ->withPivot(['type']);
    }

    public function purchasableProductVariants(): \Illuminate\Database\Eloquent\Relations\MorphToMany
    {
        return $this->morphedByMany(
            ProductVariant::class,
            'purchasable',
            'lunar_discount_purchasables',
        )
            ->withPivot(['type']);
    }
}
