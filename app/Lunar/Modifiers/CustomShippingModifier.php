<?php

namespace App\Lunar\Modifiers;

use App\Lunar\DataTypes\ShippingOption;
use Lunar\Exceptions\InvalidDataTypeValueException;
use Lunar\Facades\ShippingManifest;

class CustomShippingModifier //extends ShippingModifier
{
    /**
     * @param $cart
     * @param \Closure $next
     * @return mixed
     * @throws InvalidDataTypeValueException
     */
    public function handle($cart, \Closure $next)
    {
        ShippingManifest::addOption(
            new ShippingOption(
                cart: $cart,
                name: 'Basic Delivery',
                description: 'A basic delivery option',
                identifier: 'basic',
            )
        );

        return $next($cart);
    }
}