<?php

namespace App\Lunar;

use App\Customer;
use App\Payment;
use App\Product;
use App\Shipment;
use App\Subscription;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Order extends \Dystcz\LunarApi\Domain\Orders\Models\Order
{
    protected $attributes = [
        'channel_id' => 1,
        'status' => 'draft',
    ];

    public function customer(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    public function subscription()
    {
        return $this
            ->digitalLines
            ->map(function ($line) {
                return Product::find($line->purchasable->product_id)->toArray($line->quantity);
            });
    }

    public function lunarSubscriptionsThrough()
    {
        return $this->hasManyThrough(
            Subscription::class,
            OrderLine::class,
            'order_id',      // Foreign key on order_lines table
            'id',            // Foreign key on subscriptions table
            'id',            // Local key on orders table
            'meta->subscription->id' // JSON key (если через cast, то 'subscription_id')
        );
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function products()
    {
        return $this
            ->physicalLines
            ->map(function ($line) {
                return Product::find($line->purchasable->product_id)?->toArray($line->quantity);
            });
    }

    public function productDigitalMany()
    {
        return $this->hasMany(OrderLine::class, 'order_id')
            ->where('type', 'digital');
    }

    public function productPhysicalMany()
    {
        return $this->hasMany(OrderLine::class, 'order_id')
            ->where('type', 'physical');
    }


    public function productVariants()
    {
        return $this
            ->morphedByMany(\Lunar\Models\ProductVariant::class,
                'purchasable',
                'lunar_order_lines',
            )
            ->withPivot(['type', 'description', 'quantity', 'identifier', 'tax_breakdown', 'tax_total', 'unit_price', 'unit_quantity', 'sub_total', 'discount_total', 'total']);
    }

    public function shipments()
    {
        return $this->hasMany(Shipment::class);
    }

    function shippableLines()
    {
        return $this->lines
            ->filter(function ($item) {
                return $item->type == 'physical';
            });
    }

    public function physicalTotal()
    {
        return $this->shippableLines()
            ->map(function ($line) {
                return $line->total->decimal();
            })->sum();
    }

    public function subscriptionTotal()
    {
        return $this
            ->lines
            ->filter(function ($item) {
                return $item->type == 'digital';
            })
            ->map(function ($line) {
                return $line->total->decimal();
            })
            ->sum();
    }

    public function client()
    {
        return $this->belongsTo(Customer::class, $this->customer_id ?? $this->meta->customer_id);
    }

    public function getfrontEndLinesAttribute()
    {
        return $this->lines->toArray();
    }

    public function getAddressStringAttribute()
    {
        return collect([
            $this->shippingAddress?->line_one,
            $this->shippingAddress?->line_two,
            $this->shippingAddress?->city,
            $this->shippingAddress?->state,
            $this->shippingAddress?->postcode,
        ])
            ->join(', ');
    }
}