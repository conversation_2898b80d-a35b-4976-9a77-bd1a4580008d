<?php

namespace App\Http\Requests\Code;

use App\Rules\ReCaptcha;
use Illuminate\Foundation\Http\FormRequest;

class GetRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'value' => 'required|string',
            'type' => 'required|string',
            'g-recaptcha-response' => ['required', new ReCaptcha],
        ];
    }
}
