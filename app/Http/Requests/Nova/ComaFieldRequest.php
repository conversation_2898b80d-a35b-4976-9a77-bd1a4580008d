<?php

namespace App\Http\Requests\Nova;


use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ComaFieldRequest implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($value)) {
            $fail('The :attribute must not be empty.');
        }

        $promoArray = explode(',', $value);

        $promoArray = array_filter($promoArray, function ($value) {
            return !empty(trim($value));
        });

        if (count($promoArray) <= 0) {
            $fail('The :attribute must not be empty.');
        }

        if (count($promoArray) !== count(array_unique($promoArray))) {
            $fail('The :attribute contains duplicate values.');
        }
    }
}
