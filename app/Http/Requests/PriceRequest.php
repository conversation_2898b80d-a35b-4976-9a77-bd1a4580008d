<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Price;

class PriceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => 'required|in:monthly,yearly',
            'amount' => 'required|numeric|min:0',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateUniqueType($validator);
        });
    }

    protected function validateUniqueType($validator)
    {
        $type = $this->input('type');
        if (Price::where('type', $type)->exists()) {
            $validator->errors()->add('type', "The {$type} price already exists.");
        }
    }
}
