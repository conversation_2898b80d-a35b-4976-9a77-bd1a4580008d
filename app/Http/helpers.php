<?php

use App\Logging\CsvFormatter;
use App\Services\SettingService;
use App\SubscriptionMonth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Kfirba\Formats\Format;
use Kfirba\GregorianDate;

if (!function_exists('validatedDiscount')) {
    function validatedDiscount($discount): bool
    {
        $discount = \App\Lunar\Discount::query()
            ->whereRaw('UPPER(coupon) = ?', [strtoupper($discount)])
            ->where('enabled', true)
            ->first();
        if ($discount?->status != \App\Lunar\Discount::ACTIVE) {
            return false;
        }

        return true;
    }
}

if (!function_exists('getCycleIds')) {
    function getCycleIds(): array
    {
        $def = [
            'yearly' => 45,
            'monthly' => 46,
        ];

        if (config('custom.cycle_ids.yearly')) {
            $def['yearly'] = config('custom.cycle_ids.yearly');
        }
        if (config('custom.cycle_ids.monthly')) {
            $def['monthly'] = config('custom.cycle_ids.monthly');
        }

        return $def;
    }
}

if (!function_exists('setting')) {
    function setting()
    {
        return app(SettingService::class);
    }
}

if (!function_exists('parseCell')) {
    function parseCell($cell)
    {
        $number = preg_replace('/[^0-9]/', '', $cell);
        return strlen($number) == 10 && !Str::startsWith($number, '1') ? '1' . $number : $number;
    }
}

if (!function_exists('forceArray')) {
    function forceArray($array)
    {
        return (is_array($array) ? $array : json_decode($array, true)) ?: [];
    }
}

if (!function_exists('slack')) {
    function slack($message, $level = 'info'): void
    {
        Log::channel('slack')->$level($message);
    }
}

if (!function_exists('niceDateFormat')) {
    function niceDateFormat($date)
    {
        return $date ? now()->parse($date)->format('l, F d, Y, h:i a') : null;
    }
}

if (!function_exists('slug')) {
    function slug($text)
    {
        return preg_replace(
            '/ /',
            '-',
            preg_replace(
                '/[^a-zA-Z0-9\p{Hebrew} ]/u',
                '',
                $text
            )
        );
    }
}

if (!function_exists('DateToArray')) {
// Hebrew Dates
    function DateToArray($date, $del = ' '): array
    {
        $array = explode($del, $date);

        return [
            'year' => $array[2],
            'month' => $array[1],
            'day' => $array[0],
        ];
    }
}

if (!function_exists('HebArrayToEnglishDate')) {
    function HebArrayToEnglishDate($array)
    {
        $date = DateToArray(
            GregorianDate::fromJewish([$array['day'], $array['month'], $array['year']])->convert(),
            '/'
        );

        return today()->parse(collect($date)->join('/'));
    }
}

if (!function_exists('hebDateArray')) {
    function hebDateArray($date = null): array
    {
        $array = explode(' ', GregorianDate::toJewish($date ?? today())->convert());

        return [
            'day' => (int)$array[0],
            'month' => (int)$array[1],
            'year' => (int)$array[2],
        ];
    }
}

if (!function_exists('hebDateStringArray')) {
    function hebDateStringArray($date = null): array
    {
        $array = explode(' ', GregorianDate::toJewish($date ?? today())->format(Format::HEBREW_FULL)->convert());

        if (count($array) == 4) {
            // leap year
            return [
                'day' => $array[0],
                'month' => "$array[1] $array[2]",
                'year' => $array[3],
            ];

        }
        return [
            'day' => $array[0],
            'month' => $array[1],
            'year' => $array[2],
        ];
    }
}

if (!function_exists('getHebByPeriodMonth')) {
    function getHebByPeriodMonth($dateStart = null, $period = 12, bool $skipFirst = false): array
    {
        $dataDates = [];
        for ($i = 1; $i <= $period; $i++) {
            if ($skipFirst || $i > 1) {
                $current = nextHebRoshChodesh($dateStart);
            } else {
                $current = currentHebRoshChodesh($dateStart);
            }

            $dateStart = HebArrayToEnglishDate($current);

            $subscriptionMonth = SubscriptionMonth::getFindByDate(
                year: (int)$current['year'],
                month: (int)$current['month']
            );

            $dataDates[] = [
                ...$current,
                'id' => $subscriptionMonth->id,
                'date' => $dateStart->format('Y-m-d'),
                'name' => $subscriptionMonth->name,
            ];
        }

        return $dataDates;
    }
}

if (!function_exists('nextRoshChodesh')) {
    function nextRoshChodesh($date = null)
    {
        $heb_today = DateToArray(GregorianDate::toJewish(($date ?? today()))->convert());

        return HebArrayToEnglishDate([
            'year' => $heb_today['month'] == 13 ? $heb_today['year'] + 1 : $heb_today['year'],
            'month' => ($heb_today['month'] % 13) + 1,
            'day' => 1
        ]);
    }
}

if (!function_exists('nextHebMonth')) {
    function nextHebMonth($heb_today): array
    {
        // $heb_today['month'] += 1;

        if ($heb_today['month'] == 13) {
            return [
                'year' => $heb_today['year'] + 1,
                'month' => 1,
                'day' => 1
            ];
        }
        return [
            'year' => $heb_today['year'],
            'month' => $heb_today['month'] + 1,
            'day' => 1
        ];
    }
}

if (!function_exists('nextHebRoshChodesh')) {
    function nextHebRoshChodesh($date = null): array
    {
        $heb_today = DateToArray(
            GregorianDate::toJewish(($date ?? today()))->convert()
        );

        if (!isJewishLeapYear($heb_today['year']) && $heb_today['month'] == 6) {
            $heb_today['month']++;
        }

        if ($heb_today['month'] == 13) {
            return [
                'year' => $heb_today['year'] + 1,
                'month' => 1,
                'day' => 1
            ];
        }
        return [
            'year' => (int)$heb_today['year'],
            'month' => $heb_today['month'] + 1,
            'day' => 1
        ];
    }
}

if (!function_exists('currentHebRoshChodesh')) {
    function currentHebRoshChodesh($date = null): array
    {
        $heb_today = DateToArray(
            GregorianDate::toJewish(($date ?? today()))->convert()
        );

        return [
            'year' => (int)$heb_today['year'],
            'month' => (int)$heb_today['month'],
            'day' => 1
        ];
    }
}

if (!function_exists('logToPath')) {
    function logToPath($path, $message): void
    {
        $dateString = now()->format('Y-m-d');

        Log::build([
            'driver' => 'single',
            'path' => storage_path("logs/$path" . "-$dateString" . '.log'),
            'permission' => 0666,
        ])->info($message);
    }
}

if (!function_exists('logToCsv')) {
    function logToCsv($path, $message): void
    {
        $dateString = now()->format('Y-m-d');

        Log::build([
            'driver' => 'single',
            'path' => storage_path("logs/$path" . "-$dateString" . '.log'),
            'tap' => [CsvFormatter::class],
            'permission' => 0666,
        ])->info($message);
    }
}

if (!function_exists('expiresMake')) {
    function expiresMake($expMonth, $expYear)
    {
        $expYear = substr($expYear, -2);
        return "{$expMonth}{$expYear}";
    }
}

if (!function_exists('generateCode') ){
    function generateCode(): int
    {
        return rand(100000, 999999);
    }
}

if (!function_exists('hebrewToNumber')) {
    function hebrewToNumber($hebrewYear): int
    {
        $map = [
            'א' => 1, 'ב' => 2, 'ג' => 3, 'ד' => 4, 'ה' => 5,
            'ו' => 6, 'ז' => 7, 'ח' => 8, 'ט' => 9, 'י' => 10,
            'כ' => 20, 'ל' => 30, 'מ' => 40, 'נ' => 50, 'ס' => 60,
            'ע' => 70, 'פ' => 80, 'צ' => 90, 'ק' => 100, 'ר' => 200,
            'ש' => 300, 'ת' => 400
        ];

        $sum = 0;
        $letters = mb_str_split($hebrewYear);
        foreach ($letters as $letter) {
            $sum += $map[$letter] ?? 0;
        }
        return $sum;
    }
}