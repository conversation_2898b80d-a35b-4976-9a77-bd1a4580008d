<?php

namespace App\Http\Controllers\Dev;

use App\Customer;
use App\Cycle;
use App\Http\Controllers\Controller;
use App\Models\Collections\Order;
use App\Services\Notification\PrepareData;
use App\Subscription;
use Carbon\Carbon;

class PreviewEmailController extends Controller
{

    use PrepareData;
    // {{url}}/preview_email?path=RenewalConfirmation
    // {{url}}/preview_email?path=RenewalFailes
    // {{url}}/preview_email?path=RenewalReminder


    /**
     * images: <EMAIL> | land2.png | mailbox.png
     */
    public function show()
    {
        $path = request()->input('path');

        switch ($path) {
            case 'order-confirmation':
            case 'order-failed':
            case 'subscription-confirmation':
                $order = Order::query()->latest()->first();
                $data = $this->orderData($order);
                return view("emails.templates.$path", $data);
            case 'subscription-renewal':
            case 'subscription-renewal-manual':
            case 'subscription-renewal-reminder':
                $data = $this->subscriptionData(Subscription::query()->latest()->first());
                return view("emails.templates.$path", $data);
            case 'admin-subscription-updated-address':
                $subscription = $this->subscriptionUpdateAddressData(Subscription::query()->latest()->first());
                return view("emails.templates.$path", ['old' => $subscription, 'new' => $subscription]);

            case 'generate-route':
                $data = [
                    'user' => Customer::query()->first()->toArray(),
                    'month' => \App\SubscriptionMonth::getCurrent()->toArray(),
                ];
                return view("emails.templates.$path", $data);
//            case 'subscription-canceling':
//                $data = $this->makeDemoSubscriptionConfirmationHandler();
//                return view("emails.templates.$path", $data);
//            case 'subscription-renewal-failed':
//                $data = $this->makeDemoSubscriptionRenewalFailedHandler();
//                return view("emails.templates.$path", $data);
            default:
                abort(404);
        }
    }

    private function makeDemoSubscriptionRenewalFailedHandler(): array
    {
        return [
            'renewal_date' => Carbon::now()->toDateString(),
            'price' => 111,
            'credit_card' => '1234'
        ];
    }

    private function makeDemoSubscriptionRenewalReminderHandler(): array
    {
        return [
        ];
    }

    private function makeDemoSubscriptionRenewalHandler(): array
    {
        return [
            'renewal_date' => now()->format('Y-m-d'),
            'cycle' => 12,
            'price' => 123
        ];
    }

    private function makeDemoSubscriptionConfirmationHandler(): array
    {
        return [
            'customer' => Customer::first()->only('name'),
            'cycle' => Cycle::first()->only(['name', 'month']),
            'order' => [
                'amount' => 111
            ],
            'subscription' => Subscription::first()->only(['name','address_line_1', 'address_line_2', 'city', 'state', 'postal_code']),
            'subscription_month_name' => 'yearly',
            'price' => 111,
        ];
    }
}
