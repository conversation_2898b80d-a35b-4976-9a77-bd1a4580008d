<?php

namespace App\Http\Controllers;

use App\Promo;
use Illuminate\Http\Request;
use App\Exceptions\PromoException;
use App\Services\PromoService;


class PromoController extends Controller
{
    protected $promoService;

    public function __construct(PromoService $promoService)
    {
        $this->promoService = $promoService;
    }

    /**
     * Get Promo By Code
     *
     * Fetches a promo by its code. Returns various details about the promo if it exists and is active. Otherwise, returns an error.
     *
     * @unauthenticated
     *
     * @group Promo endpoints
     *
     * @urlParam promo string required The promo code to fetch details for.
     *
     * @response 200 {
     *    "id": "promo_id",
     *    "name": "promo_name",
     *    "amount": "discount_amount",
     *    "type": "discount_type"
     * }
     * @response 400 {"message": "This discount has reached the limit."}
     * @response 400 {"message": "This discount is not active yet."}
     * @response 400 {"message": "The promo code has expired."}
     * @response 404 {"message": "Promo not found."}
     */

    public function index($promoCode)
    {
        // Fetch the promo
        $promo = Promo::where('code', $promoCode)->first();

        // Validate promo conditions using the PromoService
        try {
            $this->promoService->validatePromo($promo);
        } catch (PromoException $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }

        // Return promo details
        return [
            'id' => $promo->id,
            'name' => $promo->name,
            'amount' => $promo->amount,
            'type' => $promo->type,
        ];
    }

    /**
     * Calculate Price With Promo
     *
     * This endpoint calculates the subscription price after applying a promotional code.
     * It returns the full price, discount amount, and the final price.
     * If the promo code is invalid or has expired, it returns an error message.
     *
     * @unauthenticated
     *
     * @group Promo endpoints
     *
     * @bodyParam subscription_type string The type of subscription (e.g., 'yearly', 'monthly'). Defaults to 'yearly'. Example: yearly
     * @bodyParam promo_code string The promotional code to be applied. Example: DISCOUNT2023
     *
     * @response 200 {
     *  "Fullprice": 200,
     *  "discount": 20,
     *  "Total": 180,
     *  "promo_id": 1
     * }
     * @response 422 {
     *  "error": "Invalid or expired promo code.",
     *  "promo_id": null,
     *  "Fullprice": 200
     * }
     */
    public function calculatePriceWithPromo(Request $request)
    {
        $priceType = $request->input('subscription_type', 'yearly');

        // Validate and get price
        try {
            $price = $this->promoService->validatePriceType($priceType);
        } catch (PromoException $e) {
            return response()->json(['error' => $e->getMessage()], 422);
        }

        $promoCode = $request->input('promo_code');

        // If no promo code was provided, return the full price
        if (!$promoCode) {
            return response()->json([
                'message' => 'No promo code provided.',
                'Fullprice' => $price->amount,
                'discount' => 0,
                'Total' => $price->amount,
                'promo_id' => null,
            ]);
        }

        $promo = Promo::where('code', $promoCode)->first();

        // If not found in Promo table, check Lunar Discount table (case-insensitive)
        if (!$promo) {
            $lunarDiscount = \Lunar\Models\Discount::whereRaw('UPPER(coupon) = ?', [strtoupper($promoCode)])
                ->where('enabled', true)
                ->first();

            if ($lunarDiscount && $lunarDiscount->status === \Lunar\Models\Discount::ACTIVE) {
                // Convert Lunar discount to Promo-like object for calculation
                $discountData = $lunarDiscount->data;

                if ($discountData['fixed_value'] ?? false) {
                    // Fixed amount discount
                    $discountAmount = $discountData['fixed_values']['USD'] ?? 0;
                } else {
                    // Percentage discount
                    $percentage = floatval($discountData['percentage'] ?? 0);
                    $discountAmount = $price->amount * ($percentage / 100);
                }

                $total = max(0, $price->amount - $discountAmount);

                return response()->json([
                    'Fullprice' => $price->amount,
                    'discount' => $discountAmount,
                    'Total' => $total,
                    'promo_id' => $lunarDiscount->id,
                ]);
            }
        }

        // Validate promo code (for Promo table entries)
        try {
            $this->promoService->validatePromo($promo);
        } catch (PromoException $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'promo_id' => null,
                'Fullprice' => $price->amount,
            ], 422);
        }

        $discountAmount = $promo->type === 'fixed' ? $promo->amount : $price->amount * ($promo->amount / 100);
        $total = max(0, $price->amount - $discountAmount);

        return response()->json([
            'Fullprice' => $price->amount,
            'discount' => $discountAmount,
            'Total' => $total,
            'promo_id' => $promo->id,
        ]);
    }
}
