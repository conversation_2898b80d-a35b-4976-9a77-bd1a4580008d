<?php

namespace App\Http\Controllers;

use App\Cycle;
use App\Product;
use App\Promo;
use App\Zone;

class ZipController extends Controller
{
    public function index($zip, $promo = null)
    {
        if (Zone::getZip($zip)) { // $zone = Zone::getZip($zip)
            $cycles = Product::cycles()->get();
//            $promo = Promo::where('code', $promo)->first();
            $yearlyPrice = $cycles->firstWhere('id', getCycleIds()['yearly'])->price;
            $monthlyPrice = $cycles->firstWhere('id', getCycleIds()['monthly'])->price * 12;
            $yearlyPercentOff = round(($monthlyPrice - $yearlyPrice) / $monthlyPrice * 100);

            return $cycles->map(function ($cycle) use ($yearlyPercentOff, $yearlyPrice) {//, $zone, $promo
                $isYearly = $cycle->id == getCycleIds()['yearly'];
                return [
                    'name' => $isYearly ? "יערליך" : "חודש'ליך",
                    'id' => $cycle->id,
                    'type' => $cycle->name,
                    'amount' => $cycle->price,
                    // if there is more than 1% savings
                    'save' => null, //$isYearly && $yearlyPercentOff > 1 ? $yearlyPercentOff . '%' : null,
                    'perIssue' => $isYearly
                        ? round($yearlyPrice / 12, 2)
                        : null,
                    'default' => config('custom.cycle_default') == $cycle->id
                ];
            });

        }

        abort(404);

    }


    public function subscriptionTotal()
    {
        $zone = Zone::find(request('zone'));
        $cycle = Cycle::find(request('cycle'));
        $promo = Promo::find(request('promo'));
        if ($zone && $cycle) {
            return $zone->priceByCycle($cycle, $promo);
        }
        return 0;
    }

    public function subscriptionDates($zip)
    {
        $zip = Zone::getZip($zip) ?? abort(404);
        return $zip->dates();
    }

    public function admin($zip)
    {
        $zone = Zone::getZip($zip) ?? abort(404);

        $cycles = $zone->belongsToMany(\App\Cycle::class)
            ->where('active', true)
            ->where('price', '>', 0)
            ->as('price')
            ->withPivot('price', 'percent')
            ->get()
            ->map(function ($cycle) {
                return [
                    'id' => $cycle->id,
                    'name' => $cycle->name,
                    'percent' => $cycle->price->percent,
                    'price' => $cycle->price->price,
                    'publications' => $cycle->publications,
                    'gifts' => $cycle->gifts->map(function ($gift) {
                        return [
                            'id' => $gift->id,
                            'name' => $gift->name,
                            'price' => $gift->price,
                            'message' => $gift->message,
                            'url' => $gift->getFirstMediaUrl('picture'),
                            'fields' => collect($gift->fields)->map->attributes,
                            'field_keys' => collect(collect($gift->fields)->map->attributes)->pluck('title'),
                        ];
                    })
                ];
            });

        $location = $zone->location($zip);

        return [
            'cycles' => $cycles,
            'zone_id' => $zone->id,
            'dates' => $zone->dates(),
            'city' => $location['city'],
            'state' => $location['state'],
        ];
    }
}
