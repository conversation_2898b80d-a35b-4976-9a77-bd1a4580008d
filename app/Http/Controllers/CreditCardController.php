<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use USAePay\API;
use USAePay\Tokens;
use USAePay\Transactions;

class CreditCardController extends Controller
{

    private static function setAuthentication()
    {
        if (env('APP_ENV') === 'local') {
            Log::debug('here');
            API::setSubdomain('sandbox');
        }

        API::setAuthentication(env('USAEPAY_API_KEY'), env('USAEPAY_API_PIN'));
    }

    public static function getTokenSubForm($data, $customerData)
    {
        Log::info('este es el customer: ' . print_r($customerData, true));
        self::setAuthentication();
        $postData = [
            'command' => 'cc:save',
            'creditcard' => [
                'number' => $data->get('credit_card_number'),
                'expiration' => $data->get('credit_card_exp_date'),
                'cvc' => $data->get('credit_card_cvv'),
                'avs_postalcode' => $data->get('credit_card_zip'),
            ],
        ];

        $response = Tokens::post($postData);

        Log::info('Response from SDK Token', (array)$response);

        // Check if there's a token in the response
        if (isset($response->key)) {
            $creditCardId = DB::table('credit_cards')->insertGetId([
                'token' => $response->key,
                'expires' => $postData['creditcard']['expiration'],
                'type' => $response->type,
                'security_code' => $postData['creditcard']['cvc'],
                'last_four' => substr($postData['creditcard']['number'], -4),
                'zip' => $postData['creditcard']['avs_postalcode'],
                'customer_id' => $customerData['data']['id'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            Log::info('here');
            return ['token' => $response->key, 'creditCardId' => $creditCardId];
        } else {
            abort(400, 'No token found in the response');
        }
    }

    public static function chargeCardSubForm(Request $request, $finalAmount)
    {
        Log::info('entro en charge card sub' . $request);
        self::setAuthentication();
        $data = [
            'command' => 'cc:sale',
            'amount' => $finalAmount,
            'creditcard' => [
                'cardholder' => $request->get('name'),
                'number' => $request->get('credit_card_number'),
                'expiration' => $request->get('credit_card_exp_date'),
            ],
        ];
        try {
            $response = Transactions::post($data);
            //Log::info('Response from SDK Charge', (array) $response);
        } catch (\Exception $e) {
            // Throw the exception to be handled at a higher level
            throw new \Exception('Error in chargeCardSubForm: ' . $e->getMessage());
        }

        return $response;
    }


    public static function getToken($data)
    {
        self::setAuthentication();

        $postData = [
            'command' => 'cc:save',
            'creditcard' => [
                'number' => $data->get('credit_card_number'),
                'expiration' => $data->get('credit_card_exp_date'),
            ],
        ];

        $response = Tokens::post($postData);

        Log::info('Response from SDK Token', (array)$response);

        // Check if there's a token in the response
        if (isset($response->key)) {
            Log::info('Token obtained');

            return ['token' => $response->key];
        } else {
            abort(400, 'No token found in the response');
        }
    }

    public static function ChargeCard($payment)
    {
        $creditCardPayment = $payment->creditCardPayment;

        $subscription = optional(optional($payment->order)->subscription);

        $tran = self::GetTran();

        $tran->command = 'cc:sale';

        $tran->card = $payment->creditCardPayment->token;

        $tran->exp = '0000';

        $tran->amount = $payment->amount;
        $tran->invoice = $payment->id;

        $creditCardPayment = $payment->creditCardPayment;

        $tran->cardholder = $subscription->name;
        // $tran->street = data_get($enrollment, 'payer_info.address_line_1');
        $tran->zip = $subscription->postal_code;

        $tran->description = "Online Order";
        $tran->cvv2 = $creditCardPayment->security_code;

        $result = self::ProccessAndReturn($tran);

        if ($result['Message'] == 'Request Approved') {
            $charges = $creditCardPayment->charges ?? [];

            array_push($charges, [
                'Date' => now(),
                'RefNum' => $result['RefNum'],
                'Authcode' => $result['Authcode'],
                'Amount' => $payment->amount
            ]);

            $creditCardPayment->update([
                'charges' => $charges,
                'refnum' => $result['RefNum']
            ]);

            return $result;
        } else {
            if (request()->is('nova-api/*')) {
                abort(500, $result['Message']);
            }
            abort(400, $result['Message']);
        }
    }

    public static function Refund($payment, $amount)
    {
        // $enrollment = $payment->enrollment;
        $creditCardPayment = $payment->creditCardPayment;

        $creditCard = DB::table('credit_cards')->where('id', $creditCardPayment->credit_card_id)->first();

        $subscription = optional(optional($payment->order)->subscription);

        $tran = self::GetTran();
        $tran->card = $creditCard->token;

        $tran->exp = '0000';

        $tran->command = 'refund'; //'credit';
        $tran->refnum = $creditCardPayment->refnum;

        $tran->amount = $amount ?? $payment->amount;
        $tran->invoice = $payment->id;

        $tran->cardholder = $subscription->name;
        // $tran->street = data_get($enrollment, 'payer_info.address_line_1');
        $tran->zip = $subscription->postal_code;

        $tran->description = "Online Order";
        $tran->cvv2 = $creditCard->security_code;

        $result = self::ProccessAndReturn($tran);
        // $result = forceArray($result);

        if ($result['Message'] == 'Request Approved') {
            $refunds = $creditCardPayment->refunds ?? [];

            array_push($refunds, [
                'date' => now(),
                'RefNum' => $result['RefNum'],
                'Authcode' => $result['Authcode'],
                'Amount' => $amount
            ]);

            $payment->creditCardPayment->update([
                'refunds' => $refunds,
            ]);

            return $result;
        }
        abort(500, $result['Reason']);
    }

    public static function Cancel($payment)
    {
        $tran = self::GetTran();

        $tran->command = 'creditvoid';

        $tran->refnum = $payment->creditCardPayment->refnum;

        $result = self::ProccessAndReturn($tran);

        if ($result['Message'] != 'Request Approved') {
            abort(400, $result['Reason']);
        }

        $payment->creditCardPayment->update([
            'status' => 'cancelled',
        ]);
    }

    private static function ProccessAndReturn($tran)
    {
        // flush();

        if ($tran->Process()) {
            return [
                'Message' => 'Request Approved',
                'Authcode' => $tran->authcode,
                'RefNum' => $tran->refnum,
                'AVS Result' => $tran->avs_result,
                'Cvv2 Result' => $tran->cvv2_result,
                'Token' => $tran->cardref
            ];
        } else {
            return
                [
                    'Message' => 'Card Declined', 'Reason' => $tran->error, 'Curl Error' => @$tran->curlerror ? $tran->curlerror : ''
                ];
        }
    }

    private static function GetTran()
    {
        $tran = new \umTransaction;

        $tran->cabundle = 'C:\xampp\apache\bin\curl-ca-bundle.crt';

        $tran->key = ('_01m7ax7m0qGH523F8vk5uj2it9cQNL6');

        $tran->pin = ('1234');

        // $tran->usesandbox = true;

        $tran->testmode = 0;

        return $tran;
    }
}
