<?php

namespace App\Http\Controllers;

use Route4Me\Geocoding;
use Route4Me\Route4Me;

class RouteForMeController extends Controller
{
    public static function geocodeAddress($address)
    {
        Route4Me::setApiKey('********************************');

        $geocodingParameters = array(
            'strExportFormat' => 'json',
            "addresses" => $address
        );
        return data_get(forceArray((new Geocoding())->forwardGeocoding($geocodingParameters)), '0');
    }
}
