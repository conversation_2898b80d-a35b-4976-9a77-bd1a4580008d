<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Subscriber;

class SubscriberController extends Controller
{
    public function store(Request $request)
    {
        return Subscriber::updateOrCreate([
            'email' => $request->input('emailAddress'),
        ], [
            'name' => $request->input('fullName'),
            'state' => 'Active',
        ]);

    }
}
