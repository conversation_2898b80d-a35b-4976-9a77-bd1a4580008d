<?php

namespace App\Http\Controllers;

use App\ContactInfo;
use App\Http\Requests\ContactInfoStoreRequest;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Http\Requests\ContactInfoUpdateRequest;
use Illuminate\Http\JsonResponse;
use Exception;

class ContactInfoController extends Controller
{
    /**
     * Get All Contact Info
     *
     * Return all contact information stored in the system. If an exception occurs, a message is returned to the client.
     *
     * @unauthenticated
     *
     * @group Contact info endpoints
     *
     * @response 200 {"data": [
     *     {
     *         "id": 1,
     *         "name": "<PERSON>",
     *         "email": "<EMAIL>",
     *         "phone": "************"
     *     },
     *     {
     *         "id": 2,
     *         "name": "<PERSON>",
     *         "email": "<EMAIL>",
     *         "phone": "************"
     *     }
     * ]}
     * @response 500 {"error": "Failed to retrieve contact info."}
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $contactInfos = ContactInfo::all();
            return response()->json(['data' => $contactInfos]);
        } catch (Exception $e) {
            report($e); // Report the exception for logging
            return response()->json(['error' => 'Failed to retrieve contact info.'], 500);
        }
    }


    /**
     * Store Contact Info
     *
     * Create a new contact info record in the system. Fields like phone, fax, and email are mandatory.
     *
     * @unauthenticated
     *
     * @group Contact info endpoints
     *
     * @bodyParam phone string required The phone number.
     * @bodyParam email string required The email address. Must be a valid email format.
     *
     * @response 200 {"message": "Contact info successfully created",
     *                "contact_info": {
     *                     "id": 1,
     *                     "phone": "************",
     *                     "fax": "************",
     *                     "email": "<EMAIL>"
     *                  }
     *               }
     * @response 400 {"error": {"email": ["The email field is required."]}}
     * @response 500 {"error": "Failed to create contact info."}
     *
     * @param ContactInfoStoreRequest $request
     * @return JsonResponse
     */
    public function store(ContactInfoStoreRequest $request): JsonResponse
    {
        try {
            $contactInfo = ContactInfo::query()->create($request->validated());
            return response()->json(['message' => 'Contact info successfully created', 'contact_info' => $contactInfo]);
        } catch (Exception $e) {
            report($e);  // Report the exception for logging
            return response()->json(['error' => 'Failed to create contact info.'], 500);
        }
    }

    /**
     * Show Contact Info
     *
     * Fetch and return a specific contact info record based on the provided ID.
     *
     * @unauthenticated
     *
     * @group Contact info endpoints
     *
     * @urlParam id integer required The ID of the contact info record to fetch. Example: 1
     *
     * @response 200 {"data": {
     *                   "id": 1,
     *                   "phone": "************",
     *                   "fax": "************",
     *                   "email": "<EMAIL>"
     *               }
     *             }
     * @response 404 {"error": "Contact info not found."}
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $contactInfo = ContactInfo::query()->findOrFail($id);
            return response()->json(['data' => $contactInfo]);
        } catch (ModelNotFoundException) {
            return response()->json(['error' => 'Contact info not found.'], 404);
        } catch (Exception $e) {
            report($e);  // Report the exception for logging
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }


    /**
     * Update Contact Info
     *
     * Update an existing contact info record in the system.
     *
     * @group Contact info endpoints
     *
     * @unauthenticated
     *
     * @urlParam id integer required The ID of the contact info record to update. Example: 1
     *
     * @bodyParam phone string required The phone number.
     * @bodyParam email string required The email address. Must be a valid email format.
     *
     * @response 200 {"message": "Contact info successfully updated",
     *                "contact_info": {
     *                     "id": 1,
     *                     "phone": "************",
     *                     "fax": "************",
     *                     "email": "<EMAIL>"
     *                  }
     *               }
     * @response 400 {"error": {"email": ["The email field is required."]}}
     * @response 404 {"error": "Contact info not found."}
     * @response 500 {"error": "Failed to update contact info."}
     *
     * @param ContactInfoUpdateRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(ContactInfoUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $contactInfo = ContactInfo::query()->findOrFail($id);
            $contactInfo->update($request->validated());
            return response()->json(['message' => 'Contact info successfully updated', 'contact_info' => $contactInfo]);
        } catch (ModelNotFoundException) {
            return response()->json(['error' => 'Contact info not found.'], 404);
        } catch (Exception $e) {
            report($e);  // Report the exception for logging
            return response()->json(['error' => 'Failed to update contact info.'], 500);
        }
    }

    /**
     * Delete Contact Info
     *
     * Delete a specific contact info record from the system.
     *
     * @unauthenticated
     *
     * @group Contact info endpoints
     *
     * @urlParam id integer required The ID of the contact info record to delete. Example: 1
     *
     * @response 200 {"message": "Contact info successfully deleted"}
     * @response 404 {"error": "Contact info not found."}
     * @response 500 {"error": "Failed to delete contact info."}
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $contactInfo = ContactInfo::query()->findOrFail($id);
            $contactInfo->delete();
            return response()->json(['message' => 'Contact info successfully deleted']);
        } catch (ModelNotFoundException) {
            return response()->json(['error' => 'Contact info not found.'], 404);
        } catch (Exception $e) {
            report($e);  // Report the exception for logging
            return response()->json(['error' => 'Failed to delete contact info.'], 500);
        }
    }

    public function contact()
    {
        return ContactInfo::first()->only('phone', 'email');
    }
}
