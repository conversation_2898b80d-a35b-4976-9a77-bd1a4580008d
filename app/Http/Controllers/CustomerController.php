<?php

namespace App\Http\Controllers;

use App\Customer;
use App\Subscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CustomerController extends Controller
{
    public function show()
    {
        return auth()->user()->frontEnd;
    }

    public function update()
    {
        $customer = auth()->user();

        $customer->update([
            'name' => request('name'),
            'email' => request('email'),
            'phone' => request('phone'),
        ]);

        Subscriber::updateOrCreate(
            ['email' => $customer->email],
            [
                'email' => $customer->email,
                'state' => request('subscribe') ? 'Active' : null,
            ]
        );
        return $customer->frontEnd;
    }

    /**
     * Create a New Customer
     *
     * This endpoint is used to create a new customer. Returns customer data if successful or an error message.
     *
     * @unauthenticated
     *
     * @group Customer endpoints
     *
     * @bodyParam name string required The name of the customer.
     * @bodyParam email string required The email address of the customer.
     * @bodyParam phone string required The phone number of the customer.
     *
     * @response 201 {
     *    "message": "Customer created successfully",
     *    "data": "customer_data_here"
     * }
     * @response 400 {
     *    "error": "Error description"
     * }
     * @response 403 scenario="Invalid Token"
     */

    public function create(Request $request)
    {
        // Validation and creation logic moved to a new function
        $createCustomerResponse = $this->createCustomer($request);

        if (isset($createCustomerResponse['error'])) {
            return response()->json(['error' => $createCustomerResponse['error']], 400);
        }

        // Return a response
        return response()->json(['message' => 'Customer created successfully', 'data' => $createCustomerResponse['data']], 201);
    }


    // New public static function
    public static function createCustomer($request)
    {
        // Validation rules
        $rules = [
            'name' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
        ];

        // Validate input
        $validator = Validator::make($request->all(), $rules);

        // Check if validation fails
        if ($validator->fails()) {
            $errors = $validator->errors();

            return ['error' => $errors];
        }

        // Try to find an existing customer
        $customer = Customer::where('email', $request->input('email'))->first();

        // If customer doesn't exist, create a new one
        if (!$customer) {
            $customer = new Customer;
            $customer->name = $request->input('name');
            $customer->email = $request->input('email');
            $customer->phone = $request->input('phone');
            // $customer->subscription_month_id = $request->input('subscription_month_id');

            // Save the new customer
            $customer->save();
        }

        // Return the new or existing customer
        return ['data' => $customer];
    }
}
