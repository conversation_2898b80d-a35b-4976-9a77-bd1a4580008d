<?php

namespace App\Http\Controllers;

use App\Campaign;
use Illuminate\Support\Str;

class CampaignController extends Controller
{
    private $media;

    public function show(Campaign $campaign)
    {
        $this->media = $campaign->media;

        $components = $this->getComponents($campaign->components)->toArray();
        // return ($this->getComponents($campaign->components));
        return view('layouts.webpage', compact('components', 'campaign'));
    }

    public function getComponents($components)
    {
        return collect($components)->map(function ($component) {
            return collect($component)->merge([
                'images' => $this->getImages($component['key'], $component['layout'])
            ]);
        });
    }

    public function getImages($key, $layout)
    {
        if ($layout == 'clips') {
            $allmedia = $this->media->filter(function ($image) use ($key) {
                return Str::startsWith($image->name, $key . '__');
            });
            return [
                'image' => $allmedia->map(function ($image) {
                    if ($image->collection_name == 'images') {
                        return $image->getFullUrl('components');
                    }
                })->filter()->values(),
                'clip' => $allmedia->map(function ($image) {
                    if ($image->collection_name == 'clips') {
                        return $image->getFullUrl('components');
                    }
                })->filter()->values(),
            ];
        }
        return $this->media->sortBy('order_column')->filter(function ($image) use ($key) {
            return Str::startsWith($image->name, $key . '__');
        })->map(function ($image) {
            return [
                'url' => $image->mime_type == 'image/gif' ? $image->getFullUrl() : $image->getFullUrl('components'),
                'title' => optional($image->custom_properties)['image_title'],
            ];
        })
            ->values();
    }
}
