<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Note;

class NotesController extends Controller
{
    public function store(Request $request)
    {
        return Note::create([
            'user_id' => auth()->id(),
            'reason' => $request->reason,
            'content' => $request->content,
            'model_id' => $request->model_id,
            'model_type' => $request->model_type,
        ])->frontEnd;
    }

    public function update(Request $request, Note $note)
    {
        $note->update(['content' => $request->content, 'reason' => $request->reason,]);
    }

    public function destroy(Note $note)
    {
        $note->delete();
    }
}
