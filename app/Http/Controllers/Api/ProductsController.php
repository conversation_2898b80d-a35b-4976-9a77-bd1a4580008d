<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Product;

class ProductsController extends Controller
{
    public function index()
    {
        return Product::accessories()
            ->ordered()
            ->status('published')
            ->latest()
            ->paginate(12);
    }

    public function forSignupPage()
    {
        return Product::accessories()
            ->status('published')
            ->ordered()
            ->get();
    }

    public static function latest()
    {
        return Product::accessories()
            ->ordered()
            ->status('published')
            ->latest()
            ->take(4)
            ->get();
    }
}
