<?php

namespace App\Http\Controllers\Api;

use App\Cart;
use App\Product;
use App\Http\Controllers\Controller;
use Dystcz\LunarApi\Domain\Carts\Actions\SetCoupon;
use Illuminate\Http\Request;
use Lunar\Facades\CartSession;
use Lunar\Facades\Discounts;
use Lunar\Models\Discount;

class CartsController extends Controller
{
    public function index()
    {
        return Cart::frontEnd();
    }

    public function incrementItem(Request $request)
    {
        $variant = Product::find($request->product_id)->variant;

        Cart::getCurrent()->incrementItem($variant, $request->increment);

        return Cart::frontEnd();
    }

    public function removeItem(Request $request)
    {
        $variant = Product::find($request->product_id)->variant;

        Cart::getCurrent()->removeItem($variant);

        return Cart::frontEnd();
    }


    public function update(Request $request)
    {
        CartSession::current();

        $variant = Product::findOrFail($request->product_id)->variant;

        CartSession::add($variant, $request->quantity);

        return Cart::frontEnd();
    }

    public function clear()
    {
        CartSession::clear();
    }

    public function setDiscount(Request $request, SetCoupon $setCoupon)
    {
        $cart = Cart::getCurrent();

        if(!validatedDiscount($request->input('discount'))){
            return response()->json(['error' => 'Invalid or expired coupon'], 422);
        }

        $setCoupon($cart, $request->input('discount'));


        return Cart::frontEnd();
    }

    public function unsetDiscount()
    {
        $cart = Cart::getCurrent();
        $cart->coupon_code = null;
        $cart->save();
        return Cart::frontEnd();
    }

    public function setShipping(Request $request): void
    {
        $cart = Cart::getCurrent();

        $cart->setShippingAddress([
            'postcode' => $request->input('postcode')
        ]);
    }
}
