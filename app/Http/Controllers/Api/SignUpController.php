<?php

namespace App\Http\Controllers\Api;

use App\Cart;
use App\Http\Controllers\Controller;
use App\Product;
use App\ProductVariant;
use Carbon\Carbon;
use Dystcz\LunarApi\Domain\Carts\Actions\SetCoupon;
use Dystcz\LunarApi\Domain\Carts\JsonApi\V1\SetCouponToCartRequest;
use Illuminate\Http\Request;
use LaravelJsonApi\Core\Responses\DataResponse;
use Lunar\Facades\Discounts;
use Lunar\Models\Discount;

class SignUpController extends Controller
{
    public function index()
    {
        Discounts::resetDiscounts();
        return Cart::signupFrontEnd();
    }

    public function setDiscount(Request $request, SetCoupon $setCoupon)
    {
        $cart = Cart::getCurrentSignUp();

        if(!$request->input('discount')){
            return $this->unsetDiscount();
        }

        if(!validatedDiscount($request->input('discount'))){
            return response()->json(['error' => 'Invalid or expired coupon'], 422);
        }
        $setCoupon($cart, $request->input('discount'));

        return Cart::signupFrontEnd();
    }
    public function unsetDiscount()
    {
        $cart = Cart::getCurrentSignUp();
        $cart->coupon_code = null;
        $cart->save();

        return Cart::signupFrontEnd();
    }

    public function setShipping(Request $request): void
    {
        $cart = Cart::getCurrentSignUp();

        $cart->setShippingAddress([
            'postcode' => $request->input('postcode'),
        ]);

        $cart->setBillingAddress(
            $cart->shippingAddress
        );
    }
    public function setCycle(Request $request)
    {
        $cart = Cart::getCurrentSignUp();

        $removeCycleIds = collect(getCycleIds())->values()->reject($request->cycle_id);
        $variantIds = ProductVariant::whereIn('product_id', $removeCycleIds)->pluck('id');

        $cart->lines()->whereIn('purchasable_id', $variantIds)->delete();

        $variant = Product::findOrFail($request->cycle_id)->variant;

        $cart->add($variant, 1);

        return Cart::signupFrontEnd();
    }

    public function addItem(Request $request)
    {
        $variant = Product::find($request->product_id)->variant;

        Cart::getCurrentSignUp()->add($variant, 1);

        return Cart::signupFrontEnd();
    }

    public function removeItem(Request $request)
    {
        $variant = Product::find($request->product_id)->variant;

        Cart::getCurrentSignUp()->removeItem($variant);

        return Cart::signupFrontEnd();
    }


    public function clear()
    {
        $cart = Cart::getCurrentSignUp();
        $cart->clear();
        $cart->update(['coupon_code' => '']);
    }

    public function getHebSelect(): array
    {
        $createShipmentsDay = (int)setting()->getValue('create_shipments_day');
        $currentMonth = hebDateArray();
        $skipFirst = $createShipmentsDay && $currentMonth['day'] >= $createShipmentsDay;
        $entries = getHebByPeriodMonth(period: 12, skipFirst: false);

        return collect($entries)->map(function ($item, $i) use ($skipFirst) {
            $item['default'] = false;
            if($skipFirst && $i == 1){
                $item['default'] = true;
            }else if(!$skipFirst && $i == 0){
                $item['default'] = true;
            }
            return $item;
        })->toArray();
    }
}
