<?php

namespace App\Http\Controllers\Api;

use App\Cart;
use App\Http\Requests\Api\OrderRequest;
use App\Services\Checkout\CheckoutFrontend;
use App\Http\Controllers\Controller;

class OrdersController extends Controller
{

    protected CheckoutFrontend $checkout;

    public function __construct(CheckoutFrontend $checkout)
    {
        $this->checkout = $checkout;
    }

    public function store(OrderRequest $request)
    {
        $this->checkout->setRequestData($request->all(), true);

        $isCircle = array_key_exists('subscriptions', $request->input('order'));
        $cart = $isCircle ? Cart::getCurrentSignUp() : Cart::getCurrent();

        $this->checkout->setCart($cart);
        $this->checkout->setCustomer(data_get($request->input('order'), 'customer'), firstOrCreate: true);
        $this->checkout->setPayment(data_get($request->input('order'), 'paymentInfo'));

        if($isCircle){
            $this->checkout->setAddress(data_get($request->input('order'), 'subscriptions.0.address'));
            $this->checkout->setSubscribes(data_get($request->input('order'), 'subscriptions'));
        }else{
            $this->checkout->setAddress(data_get($request->input('order'), 'address'));
            $this->checkout->setProducts([]);
        }

        $response = $this->checkout->store();

        if($response->status == 201){
            return $response->data;
        }else{
            return response()->json($response->data, $response->status);
        }
    }

}
