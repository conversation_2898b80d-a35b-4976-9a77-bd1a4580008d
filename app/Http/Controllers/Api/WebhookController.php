<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Subscriber;

class WebhookController extends Controller
{
    public function campaignmonitor(Request $request)
    {
        collect($request->Events)->each(function ($item) {
            $data = [
                'email' => $item['EmailAddress'],
                'name' => $item['Name'],
            ];

            if (isset($item['State']) && $item['State'] != 'Active') {
                $data['state'] = $item['State'];
            } else {
                $data['state'] = 'Subscribe';
            }

            $subscriber = Subscriber::updateOrCreate(
                ['email' => $data['email']],
                $data
            );
            // if($subscriber = Subscriber::where('email', $item['EmailAddress'])->first()){
            //     $subscriber->update($data);
            // } else {
            //     $subscriber = Subscriber::create($data);
            // }
        });
    }

    public function createWebHook()
    {
        CampaignMonitor::Lists(env('CAMPAIGNMONITOR_LIST_ID'))
            ->create_webhook(array(
                'Events' => [
                    CS_REST_LIST_WEBHOOK_SUBSCRIBE,
                    CS_REST_LIST_WEBHOOK_DEACTIVATE,
                    CS_REST_LIST_WEBHOOK_UPDATE
                ],

                'Url' => env('APP_URL') . '/api/webhook/campaignmonitor',
                'PayloadFormat' => CS_REST_WEBHOOK_FORMAT_JSON
            ));
    }
}
