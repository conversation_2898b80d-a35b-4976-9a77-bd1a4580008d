<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\InformationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class InformationController extends Controller
{
    /**
     * @var InformationService
     */
    private InformationService $informationService;

    /**
     * @param InformationService $informationService
     */
    public function __construct(InformationService $informationService)
    {
        $this->informationService = $informationService;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function countrySearch(Request $request): JsonResponse
    {
        $entries = $this->informationService->getCountriesForSelect($request->input('search'));

        return response()->json([
            'data' => $entries
        ]);
    }

}