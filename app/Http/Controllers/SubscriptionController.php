<?php

namespace App\Http\Controllers;

use App\CreditCard;
use App\CreditCardPayment;
use App\Customer;
use App\Cycle;
use App\Emails\FailedSubscriptionEmail;
use App\Order;
use App\Payment;
use App\Promo;
use App\Subscriber;
use App\Subscription;
use App\Zone;
use App\SubscriptionMonth;
use App\Services\NotificationService;
use DateTime;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use USAePay\Customers;
use USAePay\API;
use Carbon\Carbon;


class SubscriptionController extends Controller
{
    public $credit_card;

    public function index()
    {
        return auth()->user()->subscriptions->map->frontEnd;
    }

    public function fullSubscription($subscription)
    {
        return Subscription::findOrFail($subscription)->frontEnd;
    }

    protected SubscriberController $subscriberController;

    private CreditCardController $creditCardController;

    protected NotificationService $notificationService;

    public function __construct(SubscriberController $subscriberController, CreditCardController $creditCardController, NotificationService $notificationService)
    {
        $this->subscriberController = $subscriberController;
        $this->creditCardController = $creditCardController;
        $this->notificationService = $notificationService;
    }

    private static function setAuthentication()
    {
        if (env('APP_ENV') === 'local') {
            API::setSubdomain('sandbox');
        }

        API::setAuthentication(env('USAEPAY_API_KEY'), env('USAEPAY_API_PIN'));
    }

    public function show($id)
    {
        $orders = \App\Order::where(['group_id' => decrypt($id)])->get();
        $customer = $orders->first()->customer;
        $orders = $orders->map->frontEnd;

        return [
            'orders' => $orders,
            'email' => $customer->email,
        ];
    }

    public function update($subscription)
    {

        $subscription = Subscription::find($subscription);

        $updatedSubscription = tap($subscription)->update(request()->only(['address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'renew']));

        return $updatedSubscription->frontEnd;
    }

    public function updatePayment($subscription)
    {
        $user = auth()->user();
        $subscription = $user->subscriptions()->findOrFail($subscription);
        /*         //Change for testing it with the endpoint
        $subscriptionId = request('subId');
        $userId = request('user_id'); */
        $subscription = Subscription::find($subscription);

        if (!$subscription) {
            // Return an error response or throw an exception
            return response()->json(['message' => 'Subscription not found'], 404);
        }

        $data = [
            'card' => request('card'),
            'exp' => request('expires'),
            'zip' => request('zipCode'),
            'type' => request('type'),
            'expires' => request('expires'),
            'cvc' => request('security'),
            'last_four' => substr(request('card'), -4),
        ];

        $credit_card = CreditCard::createFromCard($data, $user);

        if (!$credit_card) {
            // Return an error response or throw an exception
            return response()->json(['message' => 'Failed to create credit card'], 400);
        }

        return tap($subscription)->update([
            'credit_card_id' => $credit_card->id,
            'payment_type' => 'Credit Card'
        ])->frontEnd;

        /* Change for testing the endpoint        
            $updated = $subscription->update([
            'credit_card_id' => $credit_card->id,
            'payment_type' => 'Credit Card'
        ]);
        if (!$updated) {
            // Return an error response or throw an exception
            return response()->json(['message' => 'Failed to update subscription'], 500);
        }
        
        return $subscription->fresh(); //fresh() method will reload the data from the database
         */
    }

    public function create(Request $request)
    {
        Log::channel('orders')->info($request->all());

        $promo = $request->promo_code ? Promo::where('code', $request->promo_code)->first() : null;

        $personal_information = data_get($request->subscription_forms, '0.personal_information');

        $customer = $this->getCustomer($personal_information);

        $this->signUp($personal_information);

        $uuid = Str::random(32);

        collect($request->subscription_forms)
            ->each(function ($subscription) use ($customer, $request, $uuid, $promo) {
                $zone = Zone::getZip(data_get($subscription, 'delivery_information.zip'));
                $cycle = Cycle::find(data_get($subscription, 'cycle.id'));
                $percent = $zone->percentByCycle($cycle);

                $price = $zone->priceByCycle($cycle);

                $price = $promo ? $promo->getPrice($price) : $price;

                if ($price > 0) {
                    $this->credit_card = $this->credit_card ?? $this->makeCreditCard($request, $customer);
                    $credit_card_payment = $this->credit_card->charge($price);
                }

                $subscription = Subscription::create([
                    'zone_id' => $zone->id,
                    'cycle_id' => $cycle->id,
                    'customer_id' => $customer->id,
                    'payment_type' => 'Credit Card',
                    'credit_card_id' => optional($this->credit_card)->id,
                    'is_gift' => data_get($subscription, 'gift'),
                    'renew' => $cycle->hide_auto_renew ? true : data_get($subscription, 'auto_renew'),
                    'gift_id' => data_get($subscription, 'free_gift.id'),
                    'gift_fields' => data_get($subscription, 'gift_fields'),
                    'city' => data_get($subscription, 'delivery_information.city'),
                    'name' => data_get($subscription, 'delivery_information.name'),
                    'note' => data_get($subscription, 'gift_information.gift_note'),
                    'state' => data_get($subscription, 'delivery_information.state'),
                    'postal_code' => data_get($subscription, 'delivery_information.zip'),
                    'address_line_1' => data_get($subscription, 'delivery_information.address_line_1'),
                    'address_line_2' => data_get($subscription, 'delivery_information.address_line_2'),
                    'start' => data_get($subscription, 'start')
                        ? now()->parse(data_get($subscription, 'start'))
                        : $zone->upcomingStartDate(),
                ]);

                $order = Order::create([
                    'amount' => $price,
                    'group_id' => $uuid,
                    'customer_id' => $customer->id,
                    'subscription_id' => $subscription->id,
                ]);

                if ($price > 0) {
                    Payment::create([
                        'amount' => $price,
                        'order_id' => $order->id,
                        'credit_card_payment_id' => $credit_card_payment->id,
                        'credit_card_id' => optional($this->credit_card)->id,
                        'payment_type' => $subscription->payment_type,
                    ]);
                }

                // $this->notificationService->sendNewSubscriptionNotification($subscription, $order, $promo);
            });

        return encrypt($uuid);
    }

//    public function createUserSubscription(Request $request)
//    {
//        Log::channel('orders')->info($request->all());
//
//        $this->validate($request, [
//            'g-recaptcha-response' => ['required', new ReCaptcha],
//            // 'zip' => 'required'
//        ]);
//
//        try {
//            // DB::beginTransaction();
//
//            $validator = Validator::make(
//                $request->all(),
//                $this->getValidationRules(),
//                // messages
//                [
//                    'zip.regex' => 'We currently only serve the USA'
//                ],
//            );
//            if ($validator->fails()) {
//                // DB::rollBack();
//                return response()->json(['error' => $validator->errors()], 400);
//            }
//
//            $customerData = $this->createCustomer($request);
//            // if (!isset($customerData['data']['id'])) {
//            //     // DB::rollBack();
//            //     return response()->json(['error' => 'Failed to create customer.'], 500);
//            // }
//
//            $successMessages[] = 'Customer created successfully in the DB.';
//
//            $tokenResponse = $this->creditCardController->getTokenSubForm($request, $customerData);
//            // if (!$tokenResponse || !isset($tokenResponse['token']) || !isset($tokenResponse['creditCardId'])) {
//            //     DB::rollBack();
//            //     return response()->json(['error' => 'Failed to get token and credit card id'], 400);
//            // }
//
//            $result = app('App\Http\Controllers\PromoController')->calculatePriceWithPromo($request);
//            if ($result->getStatusCode() != 200) {
//                // handle error here and return if necessary
//                return response()->json($result->getData(), $result->getStatusCode());
//            }
//            Log::debug('after promo');
//
//            $subscription = $this->createSubscription($request, $customerData, $tokenResponse);
//            Log::debug($subscription);
//            $successMessages[] = 'Successfully created the sub.';
//
//            $data = $result->getData();
//            $fullPrice = $data->Fullprice;
//            $finalAmount = $data->Total;
//            $promoId = $data->promo_id;
//
//            $order = $this->createOrder($subscription, $promoId, $finalAmount);
//            // if (!$order) {
//            //     DB::rollBack();
//            //     return response()->json(['error' => 'Failed to create order'], 400);
//            // }
//
//            if ($finalAmount > 0) {
//                $chargeResponse = $this->creditCardController->chargeCardSubForm($request, $finalAmount);
//                $custkey = $this->handleCustomerCreation($request);
//                // if (isset($custkey['error'])) {
//                //     // DB::rollBack();
//                //     return response()->json(['error' => $custkey['error']], 400);
//                // }
//
//                //$this->handlePaymentMethodCreation($request, $custkey);
//
//                if (!isset($chargeResponse->error) && $chargeResponse->result === "Approved") {
//                    $successMessages[] = 'Successfully charged the user';
//
//                    /*  if ($request->input('renew')) {
//                        $this->handleRenewal($request->input('renew'), $custkey);
//                        $successMessages[] = 'User wants to renew yearly! Successfully created the yearly renew.';
//                    } */
//
//                    $creditCardPaymentId = $this->createCreditCardPayment($tokenResponse['creditCardId'], $finalAmount, $chargeResponse);
//                    $successMessages[] = 'Successfully created the credit card payment';
//
//                    $payment = $this->createPayment($finalAmount, $subscription, $order, $creditCardPaymentId, $tokenResponse['creditCardId']);
//                    if (!$payment) {
//                        // DB::rollBack();
//                        return response()->json(['error' => 'Failed to create payment'], 400);
//                    }
//                    $this->sendSubscriptionEmails($order, $customerData, $subscription, $request, $data);
//                    $successMessages[] = 'Successfully created the payment.';
//                } else {
//                    $subscription->update([
//                        'canceled' => true
//                    ]);
//                    // DB::rollBack();
//                    $errorMessage = $chargeResponse->error ?? "Unknown error";
//                    return response()->json(['error' => 'Payment was not approved. Error: ' . $errorMessage], 400);
//                }
//            }
//
//            if ($request->input('subscriber')) {
//                $this->handleSubscriber($request->all());
//                $successMessages[] = 'Successfully created the subscriber.';
//            }
//
//            $subscription->save();
//
//            // DB::commit();
//            return response()->json(['messages' => $successMessages]);
//        } catch (\Exception $e) {
//            // DB::rollBack();
//            Log::error('Exception caught: ', ['exception' => $e]);
//            return response()->json(['error' => 'Failed to create subscription. Reason: ' . $e->getMessage()], 500);
//        }
//    }

    //validations for subs
    private function getValidationRules(): array
    {
        return [
            'name' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
            'subscriber' => 'required|boolean',
            'address_line_1' => 'required',
            'address_line_2' => 'nullable',
            'city' => 'required',
            'state' => 'required',
            'zip' => 'required|regex:/^(?:(\d{5})(?:[ \-](\d{4}))?)$/i',
            // 'renew' => 'nullable|boolean',
            'promo_code' => 'nullable|exists:promos,code',
            'apartment_building' => 'required|boolean',
            'apt_number' => 'required_if:apartment_building,true',
            'building_door_code' => 'nullable',
            'subscription_type' => 'required',
            'subscription_month_name' => 'required',
            'subscription_month_is_current' => 'required|boolean',
            'credit_card_name' => 'required',
            'credit_card_number' => 'required',
            'credit_card_exp_date' => 'required|',
            'credit_card_cvv' => 'required',
            'credit_card_zip' => 'required',
        ];
    }

    private function createOrder(Subscription $subscription, $promoId, $finalAmount): ?Order
    {
        try {
            // Create a new Order
            $order = new Order();

            // Set the order fields
            $order->amount = $finalAmount;
            $order->group_id = 'Website';
            $order->customer_id = $subscription->customer_id;
            $order->subscription_id = $subscription->id;
            $order->promo_id = $promoId;

            // Add subscription details to the order
            $order->subscription_data = [
                'date' => $subscription->created_at->toDateString(),
                'start' => $subscription->start,
                'finish' => $subscription->finish,
                'cycle' => Cycle::find($subscription->cycle_id)->only('id', 'name', 'months', 'percent'),
                'customer' => $subscription->customer->only('name', 'phone', 'email'),
                'delivery' => [
                    'name' => $subscription->name,
                    'address_line_1' => $subscription->address_line_1,
                    'city' => $subscription->city,
                    'state' => $subscription->state,
                    'postal_code' => $subscription->postal_code,

                ]
            ];

            // Save the Order
            $order->save();

            return $order;
        } catch (\Exception $e) {
            // Log the exception or handle the error as you see fit
            Log::error('Error creating order: ' . $e->getMessage());

            return null;
        }
    }

    private function createPayment($price, Subscription $subscription, Order $order, $creditCardPaymentId, $creditCardId = null)
    {
        try {
            return Payment::create([
                'amount' => $price,
                'order_id' => $order->id,
                'type' => 'card',
                'credit_card_payment_id' => $creditCardPaymentId,
                'credit_card_id' => $creditCardId,
                'payment_type' => $subscription->payment_type,
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating payment: ' . $e->getMessage());

            return null;
        }
    }

    private function createCreditCardPayment($creditCardId, $finalAmount, $chargeResponse)
    {
        try {
            $creditCardPayment = CreditCardPayment::create([
                'credit_card_id' => $creditCardId,
                'amount' => $finalAmount,
                'status' => 'active',
                'charges' => [
                    [
                        'Date' => now(),
                        'RefNum' => $chargeResponse->refnum, // Corrected the case
                        'Authcode' => $chargeResponse->authcode, // Corrected the case
                        'Amount' => $finalAmount,
                    ]
                ],
                'refnum' => $chargeResponse->refnum // Corrected the case
            ]);

            if ($creditCardPayment === null || !$creditCardPayment->id) {
                throw new \Exception('Failed to save the credit card payment');
            }

            return $creditCardPayment->id;
        } catch (\Exception $e) {
            Log::error('Error creating credit card payment: ' . $e->getMessage());

            throw $e;  // Rethrow the exception
        }
    }

    private function createCustomer(Request $request): array
    {
        return app('App\Http\Controllers\CustomerController')->create($request)->original;
    }

    private function createSubscription(Request $request, array $customerData, $tokenResponse): Subscription
    {
        Log::info('creating the subscription');

        $subscription_type = $request->input('subscription_type');
        $months = $subscription_type === 'yearly' ? 12 : 1;
        $finish = $subscription_type === 'yearly' ? '+1 year' : '+1 month';

        // Create a new subscription
        $subscription = new Subscription(
            $request->only('name', 'address_line_1', 'city', 'state', 'canceled', 'payment_type', 'user_id', 'verified_address', 'meta', 'subscription_type', 'apartment_building', 'building_door_code', 'apt_number')
            + ['renew' => true]
        );

        // Set the cycle
        $cycle = Cycle::where('months', $months)->first();

        // Check if cycle was found and assign the id, otherwise leave as null
        $subscription->cycle_id = optional($cycle)->id;

        //Set the payment type
        $subscription->payment_type = 'card';
        $subscription->credit_card_id = $tokenResponse['creditCardId'];

        // Set customer_id to the ID of the newly created customer
        $subscription->customer_id = $customerData['data']['id'];

        //Set the postal code
        $subscription->postal_code = $request->input('zip');
        $this->setSubscriptionZone($subscription, $request->input('zip'));

        // Set start and finish date
        $subscription->start = new DateTime();

        $subscription->finish = (new DateTime())->modify($finish);
        $subscription->renew = true;// $subscription_type === 'monthly' ? 1 : 0;

        //Subscription months
        $subscription->subscription_month_name = $request->input('subscription_month_name');
        $subscription->is_current = $request->input('subscription_month_is_current');

        $subscriptionMonth = SubscriptionMonth::where([
            'name' => $request->input('subscription_month_name'),
            // 'is_current' => $request->input('subscription_month_is_current')
        ])->first();

        if (is_null($subscriptionMonth)) {
            $name = $request->input('subscription_month_name');
            $isCurrent = $request->input('subscription_month_is_current');

            throw new \Exception("Could not find a matching SubscriptionMonth for name: '{$name}' and is_current: '{$isCurrent}'.");
        }

        $subscription->subscription_month_id = $subscriptionMonth->id;

        //Subscription issues 
        $subscription->issues = $months;

        $subscription->save();

        return $subscription;
    }

    private function setSubscriptionZone(Subscription $subscription, string $postalCode): void
    {
        $zone = Zone::getZip($postalCode);
        if ($zone) {
            $subscription->zone_id = $zone->id;
        } else {
            $subscription->zone_id = 0;
            Log::warning('Subscription with postal code ' . $postalCode . ' does not have an associated Zone.');
        }
    }

    private function handleSubscriber($subscriberData)
    {
        if ($subscriberData['subscriber']) {
            // Create the new Request for the store function
            $request = new Request();
            $request->replace([
                'email_address' => $subscriberData['email']
            ]);

            // Use the store function from the SubscriberController
            try {
                $this->subscriberController->store($request);
                return response()->json(['message' => 'Subscriber created successfully']);
            } catch (\Exception $e) {
                return response()->json(['error' => 'Failed to create subscriber. ' . $e->getMessage()], 500);
            }
        }
    }

    private function handleCustomerCreation(Request $request)
    {
        self::setAuthentication();

        $customerData = [
            'first_name' => $request->input('name'),
            'last_name' => $request->input('name'),
            'street' => $request->input('address_line_1'),
            'city' => $request->input('city'),
            'state' => $request->input('state'),
            'country' => 'USA',
            'phone' => $request->input('state'),
        ];
        try {

            $response = Customers::post($customerData);
            // Log::info('Response from SDK Recurrent', (array) $response);

            return $response->key;
        } catch (\Exception $e) {
            Log::error('Error in handleCustomerCreation: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return ['error' => $e->getMessage()];
        }
    }

//    protected function sendSubscriptionEmails($order, $customer, $subscription, $request, $data)
//    {
//        $this->notificationService->sendNewSubscriptionNotification($subscription, $order);
//    }


    /*     private function handlePaymentMethodCreation(Requests $request, $custkey)
    {
        Log::info('entro a crear el metodo de pago');

        self::setAuthentication();
        $paymentMethodData = [
            "custkey" => $custkey,
            "CustomerPaymentMethodRequests" => [
                "expiration" => $request->input('credit_card_exp_date'),
                "number" => $request->input('credit_card_number'),
                "pay_type" => 'cc',
            ],
        ];
        Log::info('Payment method data', (array) $paymentMethodData);
        try {
            $response = Payment_methods::post((array) $paymentMethodData);
            Log::info('Response from SDK payment method', (array) $response);
        } catch (\Exception $e) {
            Log::error('Error in handlePaymentMethodCreation: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return ['error' => $e->getMessage()];
        }
    }
 */
    /*     private function handleRenewal($renew, $custkey)
    {
        if ($renew) {
            Log::info('entro a renovar');
            $subscriptionAmount = DB::table('prices')->value('amount');

            $dayOffset = date('j');
            $monthOffset = date('n');
            $renewalData = [
                "custkey" => $custkey,
                'amount' => strval($subscriptionAmount),
                'frequency' => 'yearly',
                'next_date' => (new DateTime())->modify('+1 year')->format('Y-m-d'),
                'numleft' => -1,
                'start_date' => (new DateTime())->format('Y-m-d'),
                'skip_count' => 1,
                'rules' => [
                    [
                        'day_offset' => $dayOffset,
                        'month_offset' => $monthOffset,
                        'subject' => 'Day'
                    ]
                ],
            ];
            try {
                $response = Billing_schedules::post($renewalData);
                Log::info('Response from SDK handleRenewal', (array) $response);
            } catch (\Exception $e) {
                Log::error('Error in handleRenewal: ' . $e->getMessage());
                Log::error($e->getTraceAsString());
                return ['error' => $e->getMessage()];
            }
        }
    } */

    public function renew($id = null)
    {
        $promoPriceData = null;
        if ($id === null) {
            $subscription = auth()->user()->subscriptions->find($id);
            if ($subscription === null) {
                Log::info("No subscription found for the authenticated user.");
                return;
            }
        } else {
            // If $id is provided (via cron), find the subscription using the given ID
            $subscription = Subscription::find($id);
            if ($subscription === null) {
                Log::info("No subscription found with id: {$id}");
                return;
            }
        }

        $credit_card = $subscription->creditCard;
        Log::info('esta es la credit cardo' . $credit_card);
        $uuid = Str::random(32);
        $customer = $subscription->customer;
        $order = $subscription->lunar_order;
        $price = $order->total;

        if ($order && $order->promo && $order->promo->work_on_renew) {
            // Calculate the price using the promo
            $promoPriceData = app('App\Http\Controllers\PromoController')->calculatePriceWithPromo(new Request([
                'subscription_type' => $subscription->subscription_type,
                'promo_code' => $order->promo->code
            ]));

            // Override the price
            if (!isset($promoPriceData->getData()->error)) {
                $price = $promoPriceData->getData()->Total;
            }
        }

        if ($price > 0) {

            $chargeResult = $credit_card->charge($price);

            if ($chargeResult->result !== "Approved") {
                Log::error("Transaction not approved for subscription id: {$subscription->id}. Result Code: {$chargeResult->result}, Error: {$chargeResult->error}");
                $data = [
                    'renewal_date' => Carbon::now()->toDateString(),
                    'credit_card' => $credit_card->last_four,
                    'price' => $price
                ];

                $subscription->update([
                    'renew' => false,
                    'meta->failed_at' => now(),
                ]);

                (new FailedSubscriptionEmail)
                    ->withData($data)
                    ->withUser($customer)
                    ->sendTo();

                (new FailedSubscriptionEmail)
                    ->withData($data)
                    ->withUser(Customer::make(['email' => env('SubscriptionEmail')]))
                    ->sendTo();
                return;
            }
            $credit_card_payment = $chargeResult;
        }

        $today = Carbon::today();
        // If the subscription has expired
        if ($today->greaterThanOrEqualTo($subscription->finish)) {
            $start = $today;
        } else {
            // If the subscription is still active
            $start = $subscription->finish->addDay();
        }

        // Calculate the finish date based on the subscription type
        if ($subscription->subscription_type === 'monthly') {
            $finish = $start->copy()->addMonth();
        } else {
            $finish = $start->copy()->addYear();
        }

        $months = $subscription->subscription_type === 'yearly' ? 12 : 1;
        $subscription->update(['finish' => $finish]);
        $subscription->update(['issues' => $months]);


        $order = \App\Order::create([
            'amount' => $price,
            'group_id' => $uuid,
            'customer_id' => $customer->id,
            'subscription_id' => $subscription->id,
            'promo_id' => $order->promo_id,
        ]);

        if ($price > 0) {
            Payment::create([
                'type' => 'card',
                'amount' => $price,
                'order_id' => $order->id,
                'credit_card_payment_id' => $credit_card_payment->id,
                'credit_card_id' => optional($credit_card)->id,
                'payment_type' => $subscription->payment_type,
            ]);
        }

        $this->notificationService->subscriptionRenewalConfirmation($order, $subscription);

        return encrypt($uuid);
    }

    public function updateRenewalStatus($id, Request $request)
    {
        try {
            $subscription = Subscription::findOrFail($id);
            $renew = $request->input('renew');

            if (is_null($renew)) {
                return response()->json([
                    'message' => 'Error: No renew status provided.'
                ], 400);
            }

            $subscription->renew = $renew;
            $subscription->save();

            return response()->json([
                'message' => 'Renewal status updated.',
                'renew' => $renew,
            ]);
        } catch (\Exception $e) {
            // Log the error message
            Log::error($e->getMessage());

            return response()->json([
                'message' => 'Error: Failed to update renewal status.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    private function makeCreditCard($request, $customer)
    {
        $data = [
            'card' => data_get($request->payment_information, 'card'),
            'exp' => data_get($request->payment_information, 'expires'),
            'zip' => data_get($request->payment_information, 'zipCode'),
            'type' => data_get($request->payment_information, 'type'),
            'expires' => data_get($request->payment_information, 'expires'),
            'security_code' => data_get($request->payment_information, 'security'),
            'last_four' => substr(data_get($request->payment_information, 'card'), -4),
        ];

        return CreditCard::createFromCard($data, $customer);
    }

    // public function getPromo($request)
    // {
    //     return $request->promo_code
    //         ? (new PromoController)->index($request->promo_code)
    //         : null;
    // }

    public function signUp($personal_information)
    {
        if (data_get($personal_information, 'signUp')) {
            Subscriber::firstOrCreate(
                ['email' => data_get($personal_information, 'email')],
                [
                    'name' => data_get($personal_information, 'name'),
                    'email' => data_get($personal_information, 'email'),
                    'state' => 'Active',
                ]
            );
        }
    }

    public function getCustomer($personal_information)
    {
        return Customer::getByEmail(
            data_get($personal_information, 'email'),
            [
                'name' => data_get($personal_information, 'name'),
                'phone' => data_get($personal_information, 'phone'),
                'email' => data_get($personal_information, 'email'),
            ]
        );
    }
}
