<?php

namespace App\Http\Controllers;

use App\Campaign;
use CampaignMonitor;
use Illuminate\Http\Request;

class EmailsController extends Controller
{
    public static function getCampaign($campaign)
    {
        $campaign = Campaign::findorFail($campaign);

        $drafts = CampaignMonitor::clients(env('CAMPAIGNMONITOR_CLIENT_ID'))->get_drafts();
        $scheduled = CampaignMonitor::clients(env('CAMPAIGNMONITOR_CLIENT_ID'))->get_scheduled();
        $campaigns = collect($drafts->response)->merge($scheduled->response);
        $data = $campaigns->first(function ($item) use ($campaign) {
            return $item->Name == "Vinderkind - $campaign->number";
        });

        if ($data) {
            $old = CampaignMonitor::Campaigns($data->CampaignID)->delete();
            if ($old->http_status_code == 200) {
                $new = CampaignMonitor::Campaigns()
                    ->create(env('CAMPAIGNMONITOR_CLIENT_ID'), [
                        'Subject' => $campaign->subject,
                        'Name' => "Vinderkind - $campaign->number",
                        'FromName' => env('FromName'),
                        'FromEmail' => env('FromEmail'),
                        'ReplyTo' => env('ReplyTo'),
                        'HtmlUrl' => rtrim(env('APP_URL'), '/') . $campaign->path,
                        'ListIDs' => [env('CAMPAIGNMONITOR_LIST_ID')],
                        // 'SegmentIDs' => array('First Segment', 'Second Segment')
                    ]);
            }
        } else {
            $new = CampaignMonitor::Campaigns()
                ->create(env('CAMPAIGNMONITOR_CLIENT_ID'), [
                    'Subject' => $campaign->subject,
                    'Name' => "Vinderkind - $campaign->number",
                    'FromName' => env('FromName'),
                    'FromEmail' => env('FromEmail'),
                    'ReplyTo' => env('ReplyTo'),
                    'HtmlUrl' => rtrim(env('APP_URL'), '/') . $campaign->path,
                    'ListIDs' => [env('CAMPAIGNMONITOR_LIST_ID')],
                    // 'SegmentIDs' => array('First Segment', 'Second Segment')
                ]);
        }

        info((array)$new);

        if ($new->http_status_code <= 199 || $new->http_status_code >= 300) {
            abort(500, $new->response->Message);
        }

        $campaign->update(['campaign_id' => $new->response]);

        return [
            'id' => $new->response,
            'campaign' => $campaign,
        ];
    }

    public static function deleteCampaign($campaign)
    {
        $campaign = Campaign::findorFail($campaign);

        if ($campaign->campaign_id) {
            CampaignMonitor::Campaigns($campaign->campaign_id)->delete();
        }

        $campaign->update([
            'sent' => false,
            'schedule' => null,
            'toggle_schedule' => false,
        ]);
    }

    public static function summary($campaign)
    {
        $campaign = Campaign::findorFail($campaign);

        if ($campaign->campaign_id) {
            $summary = CampaignMonitor::Campaigns($campaign->campaign_id)->get_summary();
            $campaign->update([
                'opens' => $summary->response->TotalOpened,
                'clicks' => $summary->response->Clicks,
            ]);
            return $campaign->fresh();
        }
    }
}
