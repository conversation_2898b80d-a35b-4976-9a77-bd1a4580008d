<?php

namespace App\Http\Controllers;

use App\Http\Requests\PriceRequest;
use App\Http\Requests\UpdatePriceRequest;
use App\Price;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class PriceController extends Controller
{
    /**
     * Store Price
     *
     * Creates a price with the given parameters.
     *
     * @unauthenticated
     *
     * @group Prices
     *
     * @bodyParam type string required The type of price. Must be either 'monthly' or 'yearly'. Example: yearly
     * @bodyParam amount int required The price amount. Example: 100
     *
     * @response 200 {"message": "Price created successfully."}
     * @response 422 {"message": "Validation Error", "errors": {"type": ["The selected type is invalid."], "amount": ["The amount field is required."]}}
     * @response 500 {"message": "Internal Server Error"}
     */
    public function store(PriceRequest $request)
    {
        try {
            // Perform validation in PriceRequest
            $validated = $request->validated();

            // Create the price
            $price = new Price;
            $price->type = $validated['type'];
            $price->amount = $validated['amount'];
            $price->save();

            return response()->json(['message' => 'Price created successfully.']);
        } catch (\Exception $e) {
            // Log the exception for debugging
            Log::error('Error creating price: ' . $e->getMessage());

            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }

    /**
     * Update Price
     *
     * Updates a price with the given parameters.
     *
     * @group Prices
     *
     * @unauthenticated
     *
     * @bodyParam type string required The type of price. Must be either 'monthly' or 'yearly'. Example: yearly
     * @bodyParam amount int required The price amount. Example: 100
     *
     * @response 200 {"message": "Yearly price updated successfully."}
     * @response 422 {"message": "Validation Error", "errors": {"type": ["The selected type is invalid."], "amount": ["The amount field is required."]}}
     * @response 404 {"message": "Price not found"}
     * @response 500 {"message": "Internal Server Error"}
     */
    public function update(UpdatePriceRequest $request)
    {
        try {
            // Perform validation in UpdatePriceRequest
            $validated = $request->validated();

            // Find the price by type
            $price = Price::where('type', $validated['type'])->firstOrFail();
            $price->amount = $validated['amount'];
            $price->save();

            return response()->json(['message' => ucfirst($validated['type']) . ' price updated successfully.']);
        } catch (ModelNotFoundException) {
            return response()->json(['message' => 'Price not found'], 404);
        } catch (\Exception $e) {
            // Log the exception for debugging
            Log::error('Error updating price: ' . $e->getMessage());

            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }

    /**
     * List Prices
     *
     * Retrieves the available monthly and yearly prices.
     *
     * @group Prices
     *
     * @response 200 {
     *   "monthly": 100,
     *   "yearly": 1000
     * }
     * @response 200 {"message": "Prices not found"}
     * @response 500 {"message": "Internal Server Error"}
     */
    public function index()
    {
        try {
            $response = [];

            $yearlyPrice = Price::where('type', 'yearly')->first();
            $monthlyPrice = Price::where('type', 'monthly')->first();

            if ($yearlyPrice) {
                $response['yearly'] = $yearlyPrice->amount;
            }

            if ($monthlyPrice) {
                $response['monthly'] = $monthlyPrice->amount;
            }

            if (empty($response)) {
                return response()->json(['message' => 'Prices not found']);
            }

            // $response['image'] = 'https://nyc3.digitaloceanspaces.com/cdn.deevoch/vk/media/2/Spaeker-ad.pdf5_Page_1_Image_0001.jpg';
            // $response['crossOutPrice'] = 239;


            return $response;
        } catch (\Exception $e) {
            // Log the exception for debugging
            Log::error('Error fetching prices: ' . $e->getMessage());

            return response()->json(['message' => 'Internal Server Error'], 500);
        }
    }
}
