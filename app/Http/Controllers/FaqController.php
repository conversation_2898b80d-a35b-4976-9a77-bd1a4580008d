<?php

namespace App\Http\Controllers;

use App\FAQ;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class FaqController extends Controller
{
    public function index(): JsonResponse
    {
        try {
            $faqs = FAQ::all();

            if ($faqs->isEmpty()) {
                return response()->json(['message' => 'No FAQs found'], 204);
            }

            return response()->json($faqs);
        } catch (\Exception $e) {
            // Log the exception message and return a general error response
            Log::error('Failed to retrieve FAQs: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to retrieve FAQs, please try again later'], 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        // Define your validation rules
        $rules = [
            'question' => 'required|string',
            'answer' => 'required|string',
        ];

        // Validate the request data
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        try {
            $faq = new FAQ;
            $faq->question = $request->input('question');
            $faq->answer = $request->input('answer');
            $faq->save();

            return response()->json(['message' => 'FAQ created successfully', 'faq' => $faq], 201);
        } catch (\Exception $e) {
            // Log the exception message and return a general error response
            Log::error('Failed to store FAQ: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to store FAQ, please try again later'], 500);
        }
    }

    public function show($id): JsonResponse
    {
        try {
            $faq = FAQ::query()->findOrFail($id);
            return response()->json(['faq' => $faq]);
        } catch (ModelNotFoundException $e) {
            // Log the exception message and return a not found error response
            Log::error('FAQ not found: ' . $e->getMessage());
            return response()->json(['error' => 'FAQ not found'], 404);
        } catch (\Exception $e) {
            // Log the exception message and return a general error response
            Log::error('Failed to retrieve FAQ: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to retrieve FAQ, please try again later'], 500);
        }
    }

    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'question' => 'required',
            'answer' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        try {
            $faq = Faq::findOrFail($id);
            $faq->question = $request->input('question');
            $faq->answer = $request->input('answer');
            $faq->save();

            return response()->json(['message' => 'FAQ updated successfully']);
        } catch (ModelNotFoundException) {
            return response()->json(['error' => 'FAQ not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to update FAQ. ' . $e->getMessage()], 500);
        }
    }

    public function destroy($id): JsonResponse
    {
        try {
            $faq = Faq::query()->findOrFail($id);
            $faq->delete();

            return response()->json(['message' => 'FAQ deleted successfully']);
        } catch (ModelNotFoundException) {
            return response()->json(['error' => 'FAQ not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete FAQ. ' . $e->getMessage()], 500);
        }
    }
}
