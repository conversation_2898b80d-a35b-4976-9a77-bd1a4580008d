<?php

namespace App\Http\Controllers;

use App\Product;

class ImageController extends Controller
{
    public function getCoverImages()
    {
        return Product::query()
            ->issues()
            ->latest('id')
            ->status('published')
            ->take(4)
            ->get()
            ->sortDesc()
            ->map(function ($product) {
                return [
                    'image' => $product->img
                ];
            })
            ->values();
    }
}
