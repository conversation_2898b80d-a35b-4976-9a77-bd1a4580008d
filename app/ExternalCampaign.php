<?php

namespace App;

use App\Http\Controllers\EmailsController;
use CampaignMonitor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ExternalCampaign extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'schedule' => 'datetime',
        'components' => 'array',
    ];

    public function getPathAttribute()
    {
        return '/external-campaign/' . $this->id;
    }

    public function updateStatus()
    {
        EmailsController::summary($this->id);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        $this->addMediaConversion('components')
            ->width(600)
            ->performOnCollections('images');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('components');
    }

    public function getStatusAttribute()
    {
        if ($this->sent && $this->schedule && $this->schedule->isAfter(now())) {
            return 'Scheduled';
        } elseif ($this->sent) {
            return 'Sent';
        } else {
            return 'Draft';
        }
    }

    public static function boot()
    {
        parent::boot();
        return;

        self::deleted(function ($model) {
            if ($model->campaign_id) {
                CampaignMonitor::Campaigns($model->campaign_id)->delete();
            }
        });

        self::saved(function ($model) {
            if (!$model->toggle_schedule && $model->getOriginal('toggle_schedule')) {
                EmailsController::deleteCampaign($model->id);
            }
        });
    }
}
