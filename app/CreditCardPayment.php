<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Lunar\Facades\Payments;

class CreditCardPayment extends Model
{
    protected $guarded = [];

    public $casts = [
        'captures' => 'array',
        'authorized' => 'array',
        'refunds' => 'array',
        'charges' => 'array',
        'payer_info' => 'array',
    ];

    public function getTotalAttribute()
    {
        return $this->amount;
    }

    public function getRefundAttribute()
    {
        return collect($this->refunds)->map->Amount->sum();
    }

    public function creditCard()
    {
        return $this->belongsTo(CreditCard::class);
    }

    public function refund($amount)
    {
        Log::info("CreditCardPayment Refund process started for amount: $amount");
        $creditCard = $this->creditCard;
        $paymentDriver = Payments::driver($creditCard->payment_method);
        $response = $paymentDriver->refundPayment($this, $creditCard->customer, $amount);
        if($response->success === true){
            Log::info("Transaction Approved with refnum: {$this->refnum}");
            $payment = $creditCard->credit_card_payments()->create([
                'amount' => $amount / 100,
                'charges' => [
                    'Date' => now(),
                    'RefNum' => $this->refnum,
                    'Authcode' => $this->refnum,
                    'Amount' => $amount
                ],
                'refnum' => $this->refnum,
                'refunds' => 'This is a refund'
            ]);

            Log::info('Payment record created.');

            return (object)array_merge((array)$payment->toArray(), ['result' => $response->message]);
        }else{
            if (request()->is('nova-api/*')) {
                Log::error("Transaction failed with message: {$response->message}");
                abort(500, $response->message);
            }
            abort(400, $response->message);
        }
    }
}
