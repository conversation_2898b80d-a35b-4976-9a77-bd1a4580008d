<?php


namespace App\Services;

use Illuminate\Support\Facades\DB;

class ShipStationConfigurationService
{
    public function checkMissingFields()
    {
        $missingFields = [];
        $record = DB::table('ship_station_configurations')->first();

        if (!$record) {
            return 'Please, make sure you have configured ShipStation in the Configurations Group.';
        }

        $fieldsToCheck = ['carrier_code', 'service_code', 'package_code', 'weight_units'];

        foreach ($fieldsToCheck as $field) {
            if (is_null($record->$field)) {
                $missingFields[] = $field;
            }
        }

        return $missingFields;
    }
}
