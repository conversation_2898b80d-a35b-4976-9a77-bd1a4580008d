<?php


namespace App\Services;

use App\Shipment;
use App\Subscription;
use App\SubscriptionMonth;
use App\NamedPublication;
use Carbon\Carbon;

class SubscriptionService
{
    public function updatePublicationsToKislev()
    {
        // Get the ID for 'טבת'
        $tevetId = SubscriptionMonth::where('name', 'טבת')->value('id');
        if (!$tevetId) {
            return;
        }

        // Get the ID for 'כסלו'
        $kislevId = SubscriptionMonth::where('name', 'כסלו')->value('id');
        if (!$kislevId) {
            return;
        }

        // Get yesterday's date
        $fixedDate = Carbon::createFromFormat('Y-m-d', '2023-11-13');

        // Find all named publications created yesterday with 'טבת' ID
        $namedPublicationsToUpdate = Shipment::whereDate('created_at', $fixedDate)
            ->where('subscription_month_id', $tevetId)
            ->get();

        $updatedPublicationIds = [];
        $updatedSubscriptionIds = [];

        // Update these named publications to 'כסלו'
        foreach ($namedPublicationsToUpdate as $publication) {
            $publication->update([
                'subscription_month_id' => $kislevId
            ]);
            $updatedPublicationIds[] = $publication->id;
            // Update corresponding records in the subscriptions table
            Subscription::where('id', $publication->subscription_id)
                ->update([
                    'subscription_month_id' => $kislevId,
                    'subscription_month_name' => 'כסלו'
                ]);
            $updatedSubscriptionIds[] = $publication->subscription_id;
        }
        return ['publications' => $updatedPublicationIds, 'subscriptions' => $updatedSubscriptionIds];
    }

}
