<?php

namespace App\Services;

use App\Setting;

class SettingService
{
    private static $instance = null;
    private $setting;

    public function __construct()
    {
    }

    public static function getInstance()
    {
        if (self::$instance == null) {
            $class = new SettingService();
            $class->init();
            self::$instance = $class;
        }

        return self::$instance;
    }

    public function init()
    {
        $this->setting = Setting::all();
    }

    public function getSetting($key)
    {
        return $this->setting->filter(function ($setting) use ($key) {
            return $setting->key == $key;
        })->first();
    }

    public function getValue($key)
    {
        return optional($this->setting->where('key', $key)->first())->value;
    }

    public function getId($key)
    {
        return optional($this->setting->where('key', $key)->first())->id;
    }

    public function getObject($key)
    {
        return optional($this->setting->where('key', $key)->first());
    }

    public function getOptions($key)
    {
        $array = [];
        collect(explode(',', optional($this->setting->firstWhere('key', $key))->value))->each(function ($l) use (&$array) {
            $array[$l] = $l;
        });
        return $array;
    }
}