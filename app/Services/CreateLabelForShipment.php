<?php

namespace App\Services;

use App\NamedPublication;
use App\Export;
use App\Shipment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

class CreateLabelForShipment
{
    protected $apiKey;
    protected $apiSecret;
    private $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('shipstation.apiKey');
        $this->apiSecret = config('shipstation.apiSecret');
        $this->baseUrl = config('shipstation.apiURL');
    }

    public static function createLabelForShipment(array $labelData, string $batchId = null): ?array
    {
        $baseUrl = config('shipstation.apiURL');
        $action = '/shipments/createlabel';
        $apiKey = config('shipstation.apiKey');
        $apiSecret = config('shipstation.apiSecret');


        try {
            $response = Http::withBasicAuth($apiKey, $apiSecret)
                ->post("{$baseUrl}{$action}", $labelData);
            if ($response->successful()) {
                $responseData = $response->json();
                // Initialize labelPath as null
                $labelPath = null;

                // Decode and Save the Label
                if (isset($responseData['labelData'])) {
                    $decodedLabel = base64_decode($responseData['labelData']);
                    // Determine the directory and file name
                    if ($batchId) {
                        $batchDirectory = "labels/{$batchId}";
                        if (!Storage::disk('public')->exists($batchDirectory)) {
                            Storage::disk('public')->makeDirectory($batchDirectory);
                        }
                        $labelFileName = "{$batchDirectory}/{$labelData['orderId']}.pdf";
                    } else {
                        $labelFileName = "labels/{$labelData['orderId']}.pdf";
                    }
                    Storage::disk('public')->put($labelFileName, $decodedLabel);

                    // Generate the path manually since Storage::url() is not available
                    $labelPath = "storage/" . $labelFileName;
                }

                // Find the NamedPublication record by orderId
                $namedPublication = Shipment::where('order_id', $labelData['orderId'])->first();

                if ($namedPublication) {
                    Log::info('Found NamedPublication to update.', ['NamedPublication' => $namedPublication]);
                    $namedPublication->update([
                        'shipment_id' => $responseData['shipmentId'] ?? null,
                        'shipment_cost' => $responseData['shipmentCost'] ?? null,
                        'tracking_number' => $responseData['trackingNumber'] ?? null,
                        'label_path' => $labelPath,
                    ]);
                }

                return $responseData;
            } else {
                $responseBody = $response->body();
                $responseArray = json_decode($responseBody, true);

                // Default error message if none is found in the response
                $errorMessage = "Unsuccessful request: HTTP Status Code - " . $response->status();

                $orderId = $labelData['orderId'] ?? 'Unknown';

                // If there's an error message in the response, use that
                if (!empty($responseArray) && isset($responseArray['ExceptionMessage'])) {
                    $errorMessage = $responseArray['ExceptionMessage'];
                } elseif (isset($responseArray['Message'])) {
                    $errorMessage = $responseArray['Message'];
                }

                // Log and throw the exception with the detailed error message
                Log::error("An error occurred while creating the label for ID {$orderId}: {$errorMessage}");
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::error('An error occurred while creating the label for shipment: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate ZIP file from the PDF labels of a specific batch.
     *
     * @param string $batchId
     * @return string|null
     */
    public function generateBatchZip(string $batchId): ?string
    {
        $zip = new ZipArchive;
        $batchDirectory = "labels/{$batchId}";
        $zipFilePath = storage_path("app/public/{$batchDirectory}/{$batchId}_ShipStation-Labels.zip");
        $relativeZipFilePath = "public/{$batchDirectory}/{$batchId}_ShipStation-Labels.zip";

        if ($zip->open($zipFilePath, ZipArchive::CREATE) === true) {
            $files = Storage::disk('public')->files($batchDirectory);

            foreach ($files as $file) {
                $zip->addFile(storage_path("app/public/{$file}"), basename($file));
            }

            $zip->close();

            // Save export metadata to the database.
            $result = $this->saveExport("{$batchId}_ShipStation-Labels", $relativeZipFilePath);

            return $result['file_path'] ?? null;
        } else {
            return null;
        }
    }

    protected function saveExport(string $name, string $filePath): array
    {
        $export = Export::create([
            'name' => $name,
            'file_path' => $filePath
        ]);

        return [
            'id' => $export->id,
            'file_path' => $export->file_path,
            'name' => $name
        ];
    }
}
