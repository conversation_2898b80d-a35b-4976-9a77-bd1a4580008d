<?php


namespace App\Services;

use App\Price;
use App\Exceptions\PromoException;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class PromoService
{
    public function validatePromo($promo)
    {
        if (!$promo) {
            throw new PromoException("The promo code does not exist.");
        }

        if ($promo->limit && $promo->left < 1) {
            throw new PromoException("This discount has reached the limit.");
        }

        if ($promo->start && $promo->start > today()) {
            throw new PromoException("This discount is not active yet.");
        }

        if ($promo->end && $promo->end < today()) {
            throw new PromoException("The promo code {$promo->code} has expired.");
        }
    }

    public function validatePriceType($priceType)
    {
        try {
            $price = Price::where('type', $priceType)->firstOrFail();
        } catch (ModelNotFoundException $e) {
            throw new PromoException("Subscription type '{$priceType}' not found.");
        }

        return $price;
    }
}
