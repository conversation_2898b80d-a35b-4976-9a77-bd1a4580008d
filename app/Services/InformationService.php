<?php

namespace App\Services;

use Lunar\Models\Country;

class InformationService
{
    private Country $countryModel;

    public function __construct(Country $countryModel)
    {
        $this->countryModel = $countryModel;
    }

    public function getCountriesForSelect($search = '')
    {
        return $this->countryModel
            ->query()
            ->whereLike('name', "%{$search}%")
            ->limit(20)
            ->get()
            ->map(fn($country) => [
                'code' => $country->id,
                'label' => $country->name,
            ]);
    }
}