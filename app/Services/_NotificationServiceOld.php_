<?php

namespace App\Services;

use App\Product;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Emails\RestartSubscriptionEmail;
use App\Emails\AdminRestartSubscriptionEmail;
use App\Emails\SubscriptionNewEmail;
use App\Emails\AdminNewSubscriptionEmail;
use App\Emails\SubscriptionRenewReminderEmail;
use App\Emails\NewOrderEmail;
use App\Customer;
use App\Order;
use App\Subscription;
use Vonage\Client;
use Vonage\Client\Credentials\Basic;
use Vonage\Client\Credentials\Keypair;
use Vonage\SMS\Message\SMS;
use Vonage\Voice\OutboundCall;
use Vonage\Voice\Endpoint\Phone;
use Vonage\Voice\NCCO\NCCO;
use Vonage\Voice\NCCO\Action\Talk;

class _NotificationService
{
    protected $smsClient;
    protected $voiceClient;

    public function __construct()
    {
        try {
            $basicCredentials = new Basic(config('vonage.api_key'), config('vonage.secret'));
            $keyPath = storage_path(config('vonage.application_private_key_path'));
            $keypairCredentials = new Keypair(
                file_get_contents($keyPath),
                config('vonage.application_id')
            );

            $this->smsClient = new Client($basicCredentials);
            $this->voiceClient = new Client($keypairCredentials);
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
        }
    }

    public function sendRenewalNotification(Subscription $subscription, Order $order)
    {
        $data = $this->prepareRenewalData($subscription, $order);
        $this->dispatchNotification($data, $subscription->customer, 'sendRenewal');
    }

    public function sendNewSubscriptionNotification(Subscription $subscription, Order $order)
    {
        $data = $this->prepareNewSubscriptionData($subscription, $order);
        $this->dispatchNotification($data, $subscription->customer, 'sendNewSubscription');
    }

    public function sendNewOrderNotification($order, $subscription = null)
    {
        $customer = Customer::find($order->meta->customer_id);
        $data = $this->prepareNewOrderData($order, $subscription);
        $this->dispatchNotification($data, $customer, 'sendNewOrder');
    }

    public function sendSubscriptionExpiryNotification(Subscription $subscription)
    {
        $data = $this->prepareExpiryNotificationData($subscription);
        $this->dispatchNotification($data, $subscription->customer, 'sendIssueExpiry');

        return true;
    }

    public function sendSubscriptionExpiredNotification(Subscription $subscription)
    {
        $data = $this->prepareExpiredNotificationData($subscription);
        $this->dispatchNotification($data, $subscription->customer, 'sendSubscriptionExpired');

        return true;
    }

    public function dispatchNotification(array $data, $customer, string $methodPrefix)
    {
        if ($method = 'sendNewOrder') {
            $this->sendNewOrderEmail($data, $customer);
            return true;
        }

        try {
            $method = $methodPrefix . ucfirst($customer->preferred_contact_method);

            if (method_exists($this, $method)) {
                $this->$method($data, $customer);
                return true;
            } else {
                throw new \Exception("Method {$method} does not exist.");
            }
        } catch (\Exception $e) {
            Log::error("Failed to dispatch notification: " . $e->getMessage());
            return false;
        }
    }


    private function prepareRenewalData(Subscription $subscription, Order $order)
    {
        $price = $order->amount;

        return [
            'renewal_date' => Carbon::now()->toDateString(),
            'price' => $price,
            'cycle' => $subscription->subscription_type === 'yearly' ? 12 : ($subscription->subscription_type === 'monthly' ? 1 : null),
        ];
    }

    private function prepareNewSubscriptionData(Subscription $subscription, Order $order)
    {
        $customer = $subscription->customer;
        $credit_card = $subscription->creditCard;
        $last_name = explode(' ', $customer->name);

        return [
            'order' => $order,
            'year' => date('Y'),
            'customer' => $customer,
            'id' => $subscription->id,
            'payment' => $order->payment,
            //'percent' => $promo,
            // '' => $subscription->gift,
            'cycle' => $subscription->cycle,
            'subscription_month_name' => $subscription->subscription_month_name,
            'is_current' => $subscription->subscription_month_is_current,
            'subscription' => $subscription,
            'renew' => $subscription->renew,
            'name' => end($last_name),
            'start' => $subscription->start->format('M j, Y'),
            'last_four' => data_get($credit_card, 'last_four'),
            'finish' => $subscription->finish->format('M j, Y'),
            'picture' => data_get($subscription->gift, 'picture'),
            'payment_type' => Str::title(data_get($credit_card, 'type')),
            'creditCard' => optional($credit_card)->only('id', 'last_four', 'type'),
        ];
    }

    /*
    * TODO prepareExpiryNotificationData. Still need to create templates and emails folders.
 */
    private function prepareExpiryNotificationData(Subscription $subscription)
    {
        $customer = $subscription->customer;
        $credit_card = $subscription->creditCard;
        $last_name = explode(' ', $customer->name);

        return [
            'year' => date('Y'),
            'customer' => $customer,
            'id' => $subscription->id,
            //'percent' => $promo,
            // '' => $subscription->gift,
            'cycle' => $subscription->cycle,
            'subscription_month_name' => $subscription->subscription_month_name,
            'is_current' => $subscription->subscription_month_is_current,
            'renew' => $subscription->renew,
            'name' => end($last_name),
            'start' => $subscription->start->format('M j, Y'),
            'last_four' => data_get($credit_card, 'last_four'),
            'finish' => $subscription->finish->format('M j, Y'),
            'picture' => data_get($subscription->gift, 'picture'),
            'payment_type' => Str::title(data_get($credit_card, 'type')),
            'creditCard' => optional($credit_card)->only('id', 'last_four', 'type'),
        ];
    }

    /*
    * TODO prepareExpiredNotificationData. Still need to create templates and emails folders.
 */

    private function prepareExpiredNotificationData(Subscription $subscription)
    {
        $customer = $subscription->customer;
        $credit_card = $subscription->creditCard;
        $last_name = explode(' ', $customer->name);

        return [
            'year' => date('Y'),
            'customer' => $customer,
            'id' => $subscription->id,
            //'percent' => $promo,
            // '' => $subscription->gift,
            'cycle' => $subscription->cycle,
            'subscription_month_name' => $subscription->subscription_month_name,
            'is_current' => $subscription->subscription_month_is_current,
            'renew' => $subscription->renew,
            'name' => end($last_name),
            'start' => $subscription->start->format('M j, Y'),
            'last_four' => data_get($credit_card, 'last_four'),
            'finish' => $subscription->finish->format('M j, Y'),
            'picture' => data_get($subscription->gift, 'picture'),
            'payment_type' => Str::title(data_get($credit_card, 'type')),
            'creditCard' => optional($credit_card)->only('id', 'last_four', 'type'),
        ];
    }

    private function sendRenewalEmail(array $data, Customer $customer)
    {
        (new RestartSubscriptionEmail)
            ->withData($data)
            ->sendTo($customer);

        (new AdminRestartSubscriptionEmail)
            ->withData($data)
            ->sendTo(Customer::make(['email' => env('SubscriptionEmail')]));
    }

    private function sendRenewalText(array $data, Customer $customer)
    {
        $this->sendSms($customer->phone, 'Your subscription renewal details are as follows: ', 'renewal-message');
    }

    private function sendRenewalPhone(array $data, Customer $customer)
    {
        $this->makeVoiceCall($customer, 'Your subscription has been renewed.');
    }

    private function sendNewOrderEmail(array $data, $customer)
    {
        (new NewOrderEmail)
            ->withData($data)
            ->sendTo($customer);

    }

    private function sendNewSubscriptionEmail(array $data, Customer $customer)
    {
        (new SubscriptionNewEmail)
            ->withData($data)
            ->sendTo($customer);

        (new AdminNewSubscriptionEmail)
            ->withData($data)
            ->sendTo(Customer::make(['email' => env('SubscriptionEmail')]));
    }

    private function sendNewSubscriptionText(array $data, Customer $customer)
    {
        $this->sendSms($customer->phone, 'Your new subscription details are as follows: ', 'new-subscription-message');
    }

    private function sendNewSubscriptionPhone(array $data, Customer $customer)
    {
        $this->makeVoiceCall($customer, 'You have just subscribed to Vinderkind.');
    }

    /*
    * TODO sendIssueExpiryEmail. Still need to create templates and emails folders.
 */
    private function sendIssueExpiryEmail(array $data, Customer $customer)
    {
        (new SubscriptionRenewReminderEmail)
            ->withData($data)
            ->sendTo($customer);
    }

    private function sendIssueExpiryPhone(array $data, Customer $customer)
    {
        $this->makeVoiceCall($customer, 'You have 1 issue remaining, your subscription is about to expire. Renew now at Vinderkind.com.');
    }

    private function sendIssueExpiryText(array $data, Customer $customer)
    {
        $message = "You have 1 issue remaining, your subscription is about to expire. Renew now at Vinderkind.com.";
        $this->sendSms($customer->phone, $message, 'issue-expiry-message');
    }

    /*
    * TODO sendSubscriptionExpiredEmail. Still need to create templates and emails folders.
 */
    private function sendSubscriptionExpiredEmail(array $data, Customer $customer)
    {
        (new SubscriptionNewEmail)
            ->withData($data)
            ->sendTo($customer);

        (new AdminNewSubscriptionEmail)
            ->withData($data)
            ->sendTo(Customer::make(['email' => env('SubscriptionEmail')]));
    }

    private function sendSubscriptionExpiredPhone(array $data, Customer $customer)
    {
        $this->makeVoiceCall($customer, 'Your subscription has expired. Renew now at Vinderkind.com.');
    }

    private function sendSubscriptionExpiredText(array $data, Customer $customer)
    {
        $message = "Your subscription has expired. Renew now at Vinderkind.com.";
        $this->sendSms($customer->phone, $message, 'issue-expiry-message');
    }

    private function sendSms($phoneNumber, $message, $clientRef)
    {
        $text = new SMS(
            $phoneNumber,
            config('vonage.from_number'),
            $message
        );

        $text->setClientRef($clientRef);
        $this->smsClient->sms()->send($text);
    }

    private function makeVoiceCall(Customer $customer, $message)
    {
        try {
            $outboundCall = new OutboundCall(
                new Phone($customer->phone),
                new Phone(config('vonage.from_number'))
            );

            $ncco = new NCCO();
            $ncco->addAction(new Talk($message));

            $outboundCall->setNCCO($ncco);

            $response = $this->voiceClient->voice()->createOutboundCall($outboundCall);

            return $response;
        } catch (\Exception $e) {
            Log::error("Failed to make call: " . $e->getMessage());
            return null;
        }
    }

    function prepareNewOrderData($order, $subscription = null)
    {
        $customerId = data_get($order->meta->toArray(), 'customer_id');
        $customer = Customer::find($customerId);

        $data = [
            'year' => date("Y"),
            'customer' => [
                'name' => $customer->name
            ],
            'delivery' => [
                'name' => $order->shippingAddress->first_name,
                'address_line_1' => $order->shippingAddress->line_one,
                'address_line_2' => $order->shippingAddress->line_two,
                'city' => $order->shippingAddress->city,
                'state' => $order->shippingAddress->state,
                'postal_code' => $order->shippingAddress->postcode,
            ],
        ];

        if ($subscription) {
            $data['subscription'] = [
                'month_name' => $subscription->subscription_month_name,
                'cycle' => $subscription->cycle->only(['name', 'months']),
                'amount' => $order->subscriptionTotal(),
            ];
        }

        if ($order->physicalTotal()) {
            $data['products'] = $order->physicalLines->map(function ($line) {
                return Product::find($line->purchasable->product_id)->toArray($line->quantity);
            });
            $data['order'] = [
                'amount' => $order->physicalTotal(),
                'id' => $order->id,
            ];
        }

        return $data;
    }
}

