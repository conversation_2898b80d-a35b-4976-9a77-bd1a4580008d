<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Log;

class ShipStationCarrierService
{
    private $apiKey;
    private $apiSecret;
    private $baseUrl;
    private $action = '/carriers';

    public function __construct()
    {
        $this->apiKey = config('shipstation.apiKey');
        $this->apiSecret = config('shipstation.apiSecret');
        $this->baseUrl = config('shipstation.apiURL');
    }

    public function listCarriers()
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
                ->get("{$this->baseUrl}{$this->action}");

            if ($response->successful()) {
                $carriers = collect($response->json())
                    ->pluck('name', 'code');
                //Log::info('Carriers: ', ['carriers' => $response]);
                return $carriers;
            } else {
                throw new \Exception("Unsuccessful request: HTTP Status Code - " . $response->status());
            }
        } catch (RequestException $e) {
            // Handles if there's a client or server error
            Log::error('Failed to create ShipStation order: ' . $e->getMessage());
            return null;
        } catch (\Exception $e) {
            // Handles any other errors
            Log::error('An error occurred: ' . $e->getMessage());
            return null;
        }
    }

    public function getStampsComBalance()
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
                ->get("{$this->baseUrl}");

            if ($response->successful()) {
                $carriers = collect($response->json());
                //Log::info('Carriers: ', ['carriers' => $response]);

                $stampsComCarrier = $carriers->firstWhere('code', 'stamps_com');
                return $stampsComCarrier['balance'] ?? 'N/A';
            } else {
                throw new \Exception("Unsuccessful request: HTTP Status Code - " . $response->status());
            }
        } catch (RequestException $e) {
            Log::error('Failed to get balance: ' . $e->getMessage());
            return null;
        } catch (\Exception $e) {
            Log::error('An error occurred: ' . $e->getMessage());
            return null;
        }
    }
}
