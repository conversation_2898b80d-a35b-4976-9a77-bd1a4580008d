<?php

namespace App\Services;

use App\Export;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ExportService
{
    public function createVersionedExport($name, $filePath, $excelExportClass)
    {
        $baseName = pathinfo($filePath, PATHINFO_FILENAME);
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);

        // Initialize version number
        $version = 0;

        // New name for the database
        $dbName = $name;

        // Check for existing versions and increment the version number
        while (Storage::exists($filePath)) {
            $version++;
            $filePath = "public/exports/{$baseName} ({$version}).{$extension}";
            $dbName = "{$name} ({$version})";
        }

        // Store the Excel file. Check if the passed argument is an object or a class name
        if (is_object($excelExportClass)) {
            Excel::store($excelExportClass, $filePath);
        } else {
            Excel::store(new $excelExportClass, $filePath);
        }

        // Create a new Export record
        $export = Export::create([
            'name' => $dbName,
            'file_path' => $filePath
        ]);

        return [
            'id' => $export->id,
            'file_path' => $export->file_path,
            'name' => $dbName
        ];
    }
}
