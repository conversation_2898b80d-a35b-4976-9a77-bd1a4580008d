<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class ShipStationService
{
    private $apiKey;
    private $apiSecret;
    private $baseUrl;
    private $action = '/orders/orderId';

    public function __construct()
    {
        $this->apiKey = config('shipstation.apiKey');
        $this->apiSecret = config('shipstation.apiSecret');
        $this->baseUrl = config('shipstation.apiURL');
    }

    public function getProductById()
    {
        $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
            ->get("{$this->baseUrl}{$this->action}");

        if ($response->successful()) {
            return $response->json();
        } else {
            // Handle errors here, you could throw a custom exception or return null
            return null;
        }
    }
}
