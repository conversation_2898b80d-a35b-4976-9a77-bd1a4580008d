<?php

namespace App\Services;

use App\Export;
use App\Shipment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ZipArchive;
use setasign\Fpdi\Fpdi;


class CreateLabelForOrderService
{
    protected Fpdi $fpdi;
    protected $apiKey;
    protected $apiSecret;
    protected $storage;
    private $baseUrl;
    private $action = '/orders/createlabelfororder';

    public function __construct(Fpdi $fpdi)
    {
        $this->fpdi = $fpdi;
        $this->apiKey = config('shipstation.apiKey');
        $this->apiSecret = config('shipstation.apiSecret');
        $this->baseUrl = config('shipstation.apiURL');
    }

    public function createLabelForOrder(array $labelData, string $batchId, string $shipmentId): ?array
    {
        // try {
        $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
            ->post("{$this->baseUrl}{$this->action}", $labelData);

        if ($response->successful()) {
            $responseData = $response->json();

            //Log::info('Successfully created label for shipment.', ['ResponseData' => $responseData]);

            $labelPath = $this->saveLabel($responseData, $batchId, $labelData['orderId']);

            if ($shipment = Shipment::find($shipmentId)) {
                Log::info('Found shipment to update.', ['shipment' => $shipment]);
                $shipment->update([
                    'shipment_id' => $responseData['shipmentId'] ?? null,
                    'shipment_cost' => $responseData['shipmentCost'] ?? null,
                    'tracking_number' => $responseData['trackingNumber'] ?? null,
                    'label_path' => $labelPath,
                ]);
            }

            return $responseData;
        } else {
            $responseBody = $response->body();
            $responseArray = json_decode($responseBody, true);

            // Default error message if none is found in the response
            $errorMessage = "Unsuccessful request: HTTP Status Code - " . $response->status();

            $orderId = $labelData['orderId'] ?? 'Unknown';

            // If there's an error message in the response, use that
            if (!empty($responseArray) && isset($responseArray['ExceptionMessage'])) {
                $errorMessage = $responseArray['ExceptionMessage'];
            } elseif (isset($responseArray['Message'])) {
                $errorMessage = $responseArray['Message'];
            }

            // Log and throw the exception with the detailed error message
            Log::error("An error occurred while creating the label for ID {$orderId}: {$errorMessage}");
            throw new \Exception($errorMessage);
        }
        // } catch (\Exception $e) {
        //     Log::error('An error occurred while creating the label for shipment: ' . $e->getMessage());
        //     throw $e;
        // }
    }

    private function saveLabel($responseData, $batchId, $orderId)
    {
        $labelPath = null;

        // Decode and Save the Label
        if (isset($responseData['labelData'])) {
            $decodedLabel = base64_decode($responseData['labelData']);

            // Create a directory specific to the batch if it doesn't exist
            $batchDirectory = "labels/{$batchId}";
            if (!Storage::disk('public')->exists($batchDirectory)) {
                Storage::disk('public')->makeDirectory($batchDirectory);
            }

            // Save the PDF in the batch-specific directory
            $labelFileName = "{$batchDirectory}/{$orderId}.pdf";
            Storage::disk('public')->put($labelFileName, $decodedLabel);
            $labelPath = "storage/" . $labelFileName;
        }

        return $labelPath;
    }

    /**
     * Generate ZIP file from the PDF labels of a specific batch.
     *
     * @param string $batchId
     * @return string|null
     */
    public function generateBatchZip(string $batchId): ?string
    {
        $zip = new ZipArchive;
        $batchDirectory = "labels/{$batchId}";
        $zipFilePath = storage_path("app/public/{$batchDirectory}/{$batchId}_ShipStation-Labels.zip");
        $relativeZipFilePath = "public/{$batchDirectory}/{$batchId}_ShipStation-Labels.zip";

        if ($zip->open($zipFilePath, ZipArchive::CREATE) === true) {
            $files = Storage::disk('public')->files($batchDirectory);

            foreach ($files as $file) {
                $zip->addFile(storage_path("app/public/{$file}"), basename($file));
            }

            $zip->close();

            // Save export metadata to the database.
            $result = $this->saveExport("{$batchId}_ShipStation-Labels", $relativeZipFilePath);

            return $result['file_path'] ?? null;
        } else {
            return null;
        }
    }

    protected function saveExport(string $name, string $filePath): array
    {
        $export = Export::create([
            'name' => $name,
            'file_path' => $filePath
        ]);

        return [
            'id' => $export->id,
            'file_path' => $export->file_path,
            'name' => $name
        ];
    }

    public function mergeBatchPdfs(string $batchId): ?string
    {
        $batchDirectory = "labels/{$batchId}";

        // Ensure the directory exists
        Storage::disk('public')->makeDirectory($batchDirectory);

        $pdfFilePath = storage_path("app/public/{$batchDirectory}/{$batchId}_ShipStation-Labels.pdf");
        $relativePdfFilePath = "public/{$batchDirectory}/{$batchId}_ShipStation-Labels.pdf";

        $pdf = $this->fpdi;

        $files = Storage::disk('public')->files($batchDirectory);

        foreach ($files as $file) {
            // Add a page
            $pdf->AddPage('P', [101.6, 152.4]);
            // Set the source file
            $pdf->setSourceFile(storage_path("app/public/{$file}"));
            $tplId = $pdf->importPage(1);
            // Use the imported page and place it at the right position and scale
            $pdf->useTemplate($tplId, 0, 0, 101.6, 152.4);
        }

        // Output the PDF to a file
        try {
            $pdf->Output('F', $pdfFilePath);
        } catch (\Exception $e) {
            // Handle Exception
            report($e);
            return null;
        }

        // Save export metadata to the database
        $result = $this->saveExport("{$batchId}_ShipStation-Labels", $relativePdfFilePath);

        return $result['file_path'] ?? null;
    }
}
