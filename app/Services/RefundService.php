<?php

namespace App\Services;

use App\CreditCard;
use App\Models\Collections\Order;
use App\Shipment;
use App\Subscription;
use phpDocumentor\Reflection\DocBlock\Tags\Throws;

class RefundService
{
    private $order;
    private array $refund = [];
    private array $cancelingSubscriptions = [];
    private array $cancelingShipment = [];


    public function setOrder($id): void
    {
        $this->order = Order::with(['digitalLines','physicalLines','shippingLines', 'shipments'])->find($id);

        $hasProduct = false;

        $subscriptions = Subscription::query()->whereIn('id', $this->order->digitalLines->pluck('meta.subscription.id'))->get();
        foreach($this->order->digitalLines as $line){
            $this->validateSubscription(
                subscription: $subscriptions->where('id', $line->meta->subscription['id'])->first(),
                price: $line->total->value
            );
        }

        $orderCard = CreditCard::query()->where('id', data_get($this->order->meta->toArray(), 'creditCardData.creditCardId'))->first();

        if(!$orderCard){
            return;
        }

        foreach($this->order->physicalLines as $line){
            $shipment = $this->order->shipments
                ->where('purchasable_id', $line->purchasable_id)
                ->where('purchasable_type', \Lunar\Models\ProductVariant::class)
                ->first();

            if(!$shipment || $shipment->canceled_at || $shipment->shipped_at || $shipment->tracking_number){
                continue;
            }

            $hasProduct = true;

            if (array_key_exists($orderCard->id, $this->refund)) {
                $this->refund[$orderCard->id] += $line->total->value ;
            } else {
                $this->refund[$orderCard->id] = $line->total->value ;
            }

            $this->cancelingShipment[] = $shipment->id;
        }

        if(!$hasProduct){
            return;
        }

        $shipping = $this->order->shippingLines->sum(function($line){
            return $line->sub_total->value + $line->tax_total->value;
        });


        if($shipping > 0){
            if (array_key_exists($orderCard->id, $this->refund)) {
                $this->refund[$orderCard->id] += $shipping;
            } else {
                $this->refund[$orderCard->id] = $shipping;
            }
        }
    }

    public function setSubscription($id)
    {
        $subscription = Subscription::query()->with(['lunar_order'])->find($id);

        if(!$subscription->lunar_order){
            return throw new \Exception('Order not found');
        }
        $this->order = $subscription->lunar_order;

        $line = $subscription->lunar_order->digitalLines->where('meta.subscription.id', $subscription->id)->first();

        if(!$line){
            return throw new \Exception('Subscription line not found');
        }

        $this->validateSubscription($subscription, $line->total->value);
    }


    public function partials($amountField): float|int
    {
        $refundTemp = $amountField * 100;
        if(!$amountField || $refundTemp >= array_sum($this->refund)){
            return $this->prorated();
        }

        $this->canceling();

        $cards = CreditCard::query()->whereIn('id', array_keys($this->refund))->get();
        foreach($this->refund as $creditCard => $amount){
            $refundAmountForCard = min($amount, $refundTemp);

            $cards->where('id', $creditCard)->first()->refund($refundAmountForCard);
            $refundTemp -= $refundAmountForCard;

            if ($refundTemp <= 0) {
                break;
            }
        }

        return $amountField * 100;
    }

    public function prorated(): float|int
    {
        $this->canceling();
        $cards = CreditCard::query()->whereIn('id', array_keys($this->refund))->get();
        foreach($this->refund as $creditCard => $amount){
            $cards->where('id', $creditCard)->first()->refund($amount);
        }

        return array_sum($this->refund);
    }

    private function canceling(): void
    {
        Subscription::query()->whereIn('id', $this->cancelingSubscriptions)->update(['canceled' => true, 'canceled_reason' => 'Refunded']);
        Shipment::query()->whereIn('id', $this->cancelingShipment)->update(['canceled_at' => now()]);
        $this->order->update(['status' => 'payment-refunded']);
    }

    private function validateSubscription($subscription, $price): void
    {
        if($subscription && !$subscription->canceled){
            $issuesLeft = $subscription->issues;

            $refundPrice = Subscription::calculateRefundAmount($price, $issuesLeft);
            if ($subscription->creditCard && $refundPrice > 0) {
                if(array_key_exists($subscription->credit_card_id, $this->refund)){
                    $this->refund[$subscription->credit_card_id] += $refundPrice;
                }else{
                    $this->refund[$subscription->credit_card_id] = $refundPrice;
                }
            }
            $this->cancelingSubscriptions[] = $subscription->id;
        }
    }

}