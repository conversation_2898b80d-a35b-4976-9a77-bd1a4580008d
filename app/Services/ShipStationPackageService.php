<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Log;

class ShipStationPackageService
{
    private $apiKey;
    private $apiSecret;
    private $baseUrl;
    private $action = '/carriers/listpackages?carrierCode=stamps_com';

    public function __construct()
    {
        $this->apiKey = config('shipstation.apiKey');
        $this->apiSecret = config('shipstation.apiSecret');
        $this->baseUrl = config('shipstation.apiURL');
    }

    public function listPackages()
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
                ->get("{$this->baseUrl}{$this->action}");

            if ($response->successful()) {
                $services = collect($response->json())
                    ->pluck('name', 'code'); // Assuming the response has 'name' and 'code' fields.

                Log::info('Services:', [$services]);

                return $services;
            } else {
                throw new \Exception("Unsuccessful request: HTTP Status Code - " . $response->status());
            }
        } catch (RequestException $e) {
            // Handles if there's a client or server error
            Log::error('Failed to fetch services: ' . $e->getMessage());
            return null;
        } catch (\Exception $e) {
            // Handles any other errors
            Log::error('An error occurred: ' . $e->getMessage());
            return null;
        }
    }
}
