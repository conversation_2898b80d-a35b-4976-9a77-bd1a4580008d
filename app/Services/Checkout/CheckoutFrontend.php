<?php

namespace App\Services\Checkout;

use App\Cart;
use App\Lunar\Cycle;
use App\Product;
use App\Subscription;
use App\SubscriptionMonth;
use App\Zone;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CheckoutFrontend extends Checkout
{

    /**
     * @return object
     * 'card' =>
     * array (
     * 'id' => 'card_1QT3NgQZkxrJjOUDINY8guyz',
     * 'object' => 'card',
     * 'address_city' => NULL,
     * 'address_country' => NULL,
     * 'address_line1' => NULL,
     * 'address_line1_check' => NULL,
     * 'address_line2' => NULL,
     * 'address_state' => NULL,
     * 'address_zip' => '22222',
     * 'address_zip_check' => 'unchecked',
     * 'brand' => 'Visa',
     * 'country' => 'US',
     * 'cvc_check' => 'unchecked',
     * 'dynamic_last4' => NULL,
     * 'exp_month' => 2,
     * 'exp_year' => 2042,
     * 'funding' => 'credit',
     * 'last4' => '0002',
     * 'name' => 'test name',
     * 'networks' =>
     * array (
     * 'preferred' => NULL,
     * ),
     * 'tokenization_method' => NULL,
     * 'wallet' => NULL,
     * ),
     */
    public function store(): object
    {
        DB::beginTransaction();
        $this->zoneProductHandler();
        $this->initShippingHandler();
        $this->cart->calculate();

        $this->isFullyDiscounted = $this->cart->discountTotal->decimal() === $this->cart->subTotal->decimal() && $this->cart->total->decimal() == 0;

        $this->order = $this->cart->createOrder();

        $creditCardData = $this->paymentDriver->getToken(
            customer: $this->customer,
            payment: $this->paymentInfo,
            withoutCard: $this->isFullyDiscounted,
        );

        $this->order->update([
            'meta' => [
                'creditCardData' => $creditCardData,
                'request' => $this->requestData,
                'customer_id' => $this->customer->id,
                'payment_type' => 'credit'
            ],
            'customer_id' => $this->customer->id,
            'resource' => 'client',
        ]);
        $this->paymentDriver->order($this->order);

        if ($this->isFullyDiscounted || $this->paymentDriver->charge()->success) {
            $response = $this->afterPayment();
            DB::commit();

            return $response;
        }

        $this->order->update([
            'status' => 'payment-declined'
        ]);


        $this->notificationService->orderFailed($this->order);

        return (object)[
            'data' => ['error' => 'Card Declined'],
            'status' => 403
        ];
    }

    public function setPayment($data): void
    {
        $this->paymentInfo = $data;
    }

    protected function afterPayment(): object
    {
        $this->order->update([
            'status' => 'payment-received'
        ]);

        if ($this->isSubscribe) {
            $subscribe = array_shift($this->subscribes);
            $product = Cycle::getProductByName(data_get($subscribe, 'cycle'));

            $subscribe_type = $product->name;
            $months = $subscribe_type === 'yearly' ? 12 : 1;

            if ($zone = Zone::getZip(data_get($this->addressInfo, 'zip'))) {
                $zone_id = $zone->id;
            } else {
                $zone_id = 0;
            }

            $startMonth = $subscribe['startMonth'] ?? 0;

            if (!$startMonth) {
                $subscriptionMonth = SubscriptionMonth::getCurrent();
                $start = Carbon::now();
            } else {
                list(, $jewMonth, $jewYear) = explode('_', $startMonth);
                $subscriptionMonth = SubscriptionMonth::getFindByDate(
                    year: $jewYear,
                    month: $jewMonth
                );
                $start = HebArrayToEnglishDate([
                    'year' => $subscriptionMonth->year,
                    'month' => $subscriptionMonth->month,
                    'day' => 1,
                ]);
            }

            $finish = $subscribe_type === 'yearly' ? Carbon::parse($start)->addYear() : Carbon::parse($start)->addMonth();

            $subscription = Subscription::query()->create([
                'renew' => !$this->isFullyDiscounted,
                'name' => data_get($this->addressInfo, 'name'),
                'address_line_1' => data_get($this->addressInfo, 'address_line_1'),
                'address_line_2' => data_get($this->addressInfo, 'address_line_2'),
                'city' => data_get($this->addressInfo, 'city'),
                'state' => data_get($this->addressInfo, 'state'),
                'postal_code' => data_get($this->addressInfo, 'zip'),
                'payment_type' => 'Credit Card',
                'subscription_type' => $subscribe_type,
                'cycle_id' => DB::table('cycles')->where('months', $months)->first()?->id,
                'zone_id' => $zone_id,
                'credit_card_id' => data_get($this->order->meta->toArray(), 'creditCardData.creditCardId'),
                'customer_id' => data_get($this->order->meta->toArray(), 'customer_id'),
                'start' => $start,
                'finish' => $finish,
                'subscription_month_name' => $subscriptionMonth->name,
                'subscription_month_id' => $subscriptionMonth->id,
                'issues' => $months,
                'order_id' => $this->order->id,
            ]);

            $this->order->load('digitalLines');
            $this->order->digitalLines->first()->update([
                'meta->subscription' => $subscription
            ]);
            $subscription->product = $product;


            $this->order->shippableLines()
                ->each(function ($item) {
                    $this->order->shipments()->create([
                        'zone_id' => Zone::getZip($this->order->shippingAddress?->postcode)?->id,
                        'address_id' => $this->order->shippingAddress?->id,
                        'purchasable_id' => $item->purchasable_id,
                        'purchasable_type' => $item->purchasable_type,
                        'line_one' => $this->order->shippingAddress->line_one,
                        'line_two' => $this->order->shippingAddress->line_two,
                        'city' => $this->order->shippingAddress->city,
                        'state' => $this->order->shippingAddress->state,
                        'postcode' => $this->order->shippingAddress->postcode,
                        'quantity' => $item->quantity,
                    ]);
                });

            $products = $this->order->physicalLines->map(function ($line) {
                return Product::query()->find($line->purchasable->product_id)->toArray($line->quantity);
            });


            Cart::forgetCurrentSignUp();

            $this->notificationService->subscriptionConfirmation($this->order);

            return (object)[
                'data' => [
                    'id' => $subscription->id,
                    'email' => $this->customer->email,
                    'subscriptionId' => $subscription->id,
                    'cycleName' => Str::title($subscription->subscription_type),
                    'monthName' => $subscription->subscriptionMonth?->name,
                    'start' => $subscription->start->format('M j, Y'),
                    'finish' => $subscription->finish->format('M j, Y'),
                    'price' => $subscription->product->variant->price,
                    'savings' => $this->isFullyDiscounted ? false : Product::YearlyPercentOff(),
                    'subscriptionTotal' => $this->order->digitalLines->pluck('total')->map->decimal()->sum(),
                    'total' => $this->order->total->decimal(),
                    'lastFour' => $subscription->creditCard->last_four ?? false,
                    'delivery' => $subscription->only([
                        'name', 'address_line_1', 'address_line_2', 'city', 'state', 'postal_code'
                    ]),
                    'isRenew' => $subscription->renew,
                    'products' => $products,
                    'is_gift' => false,
                ],
                'status' => 201
            ];
        }

        if ($this->isProduct) {
            $this->order->shippableLines()
                ->each(function ($item) {
                    $this->order->shipments()->create([
                        'zone_id' => $this->zoneProduct?->id,
                        'address_id' => $this->order->shippingAddress?->id,
                        'purchasable_id' => $item->purchasable_id,
                        'purchasable_type' => $item->purchasable_type,
                        'line_one' => $this->order->shippingAddress->line_one,
                        'line_two' => $this->order->shippingAddress->line_two,
                        'city' => $this->order->shippingAddress->city,
                        'state' => $this->order->shippingAddress->state,
                        'postcode' => $this->order->shippingAddress->postcode,
                        'quantity' => $item->quantity,
                    ]);
                });
            $products = $this->order->physicalLines->map(function ($line) {
                return Product::find($line->purchasable->product_id)->toArray($line->quantity);
            });

            $delivery = [
                'name' => $this->order->shippingAddress->first_name,
                'address_line_1' => $this->order->shippingAddress->line_one,
                'city' => $this->order->shippingAddress->city,
                'state' => $this->order->shippingAddress->state,
                'postal_code' => $this->order->shippingAddress->postcode,
            ];

            Cart::forgetCurrent();

            $this->notificationService->orderConfirmation($this->order);

            return (object)[
                'data' => [
                    'email' => $this->customer->email,
                    'total' => $this->order->total->decimal(),
                    'lastFour' => data_get($this->order->meta->toArray(), 'creditCardData.last_four'),

                    'delivery' => $delivery,
                    'products' => $products,
                    'is_gift' => false,
                ],
                'status' => 201
            ];
        }



        return (object)[
            'data' => ['error' => 'Product is not selected!'],
            'status' => 403
        ];
    }

}
