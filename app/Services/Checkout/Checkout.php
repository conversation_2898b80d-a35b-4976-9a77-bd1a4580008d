<?php

namespace App\Services\Checkout;

use App\Cart;
use App\Customer;
use App\Services\NotificationService;
use App\Subscription;
use App\SubscriptionMonth;
use App\Zone;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Lunar\Facades\Payments;
use Psr\Http\Client\ClientExceptionInterface;

class Checkout
{
    protected $customer;
    protected $order;
    protected $cart;
    protected $products = null;
    protected $subscribes = null;
    protected $cardNumber = null;
    protected $zoneProduct = null;
    protected $paymentDriver = null;
    protected array $paymentInfo = [];
    protected array $addressInfo;
    protected array $requestData = [];
    protected array $meta = [];
    protected bool $isSubscribe = false;
    protected bool $isProduct = false;
    protected $discount = null;

    protected bool $isFullyDiscounted = false;

    protected NotificationService $notificationService;
    protected bool $autoRenew = true;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
        $this->paymentDriver = Payments::driver(config('lunar.payments.default'));
    }
    public function setRequestData(array $data, bool $isLogged = false): void
    {
        $this->requestData = $this->modifyRequestData($data);

        if ($isLogged) {
            Log::channel('orders')->info($this->requestData);
        }
    }

    public function setAddress(array $data): void
    {
        $this->addressInfo = $data;
    }


    public function setAutoRenew(bool $renew): void
    {
        $this->autoRenew = $renew;
    }

    public function setDiscount($data): void
    {
        if($data->discount) {
            $this->discount = \Lunar\Models\Discount::find($data->discount);
        } else {
            $this->discount = null; 
        }
    }

    public function setProducts($data): void
    {
        $this->products = $data;
        $this->isProduct = true;
    }

    public function setSubscribes($data): void
    {
        $this->subscribes = $data;
        $this->isSubscribe = true;
    }

    public function setPayment(array $data): void
    {
        $this->paymentInfo = $data;

        $this->cardNumber = substr($data['cardNumber'], -4);
    }

    protected function modifyRequestData($data)
    {
        data_set(
            $data,
            'order.paymentInfo.cardNumber',
            substr(data_get($data, 'order.paymentInfo.cardNumber'), -4)
        );
        return $data;
    }

    public function setCustomer($data, bool $firstOrCreate = false): void
    {
        if ($firstOrCreate) {
            if($data['has_email']){
                $this->customer = Customer::getByEmail($data['email'], $data);
                $this->customer->signUp(!!data_get($data, 'signUp'));
            }else{
                $this->customer = Customer::getByPhone($data['phone'], $data);
//                $this->customer->signUp(!!data_get($data, 'signUp'));
            }
        } else {
            $this->customer = $data;
        }
    }

    public function setCart($cart): void
    {
        $this->cart = $cart;
    }

    protected function initShippingHandler(): void
    {
        $this->cart->setShippingAddress([
            'country_id' => 236,
            'title' => null,
            'first_name' => data_get($this->addressInfo, 'name'),
            'last_name' => data_get($this->addressInfo, 'name'),
            'city' => data_get($this->addressInfo, 'city'),
            'state' => data_get($this->addressInfo, 'state'),
            'postcode' => data_get($this->addressInfo, 'zip'),
            'line_one' => data_get($this->addressInfo, 'address_line_1'),
            'line_two' => data_get($this->addressInfo, 'address_line_2'),
            'shipping_option' => 'basic'
        ]);
        $this->cart->setBillingAddress(
            $this->cart->shippingAddress
        );
    }

    protected function zoneProductHandler(): void
    {
        $this->zoneProduct = Zone::getZip(data_get($this->addressInfo, 'zip'));
    }

    public function store(): object
    {
        return (object)[];
    }


    protected function makeResponseError($message, $code = 200, $field = null)
    {
        return [
            'message' => $message,
            'errors' => [
                $field => [
                    $message
                ]
            ],
            'status' => $code
        ];
    }

    protected function initPaymentDriver(): void
    {

//        $this->paymentDriver = Payments::driver('card');
//        $creditCardData = $this->paymentDriver->getToken([
//            'number' => $this->paymentInfo['cardNumber'],
//            'cvv' => $this->paymentInfo['cvv'],
//            'exp' => $this->paymentInfo['exp'],
//            'zip' => $this->paymentInfo['zip'],
//        ], $this->customer->id);
//
//        $this->order->update([
//            'meta' => [
//                'creditCardData' => $creditCardData,
//                'request' => $this->requestData,
//                'customer_id' => $this->customer->id,
//                'payment_type' => 'credit'
//            ],
//            'customer_id' => $this->customer->id
//        ]);




//        if ($intent->amount != $cart->total->value) {
//            Stripe::syncIntent($cart);
//        }

//        $this->paymentDriver->order($this->order);
    }

    /**
     * @throws ClientExceptionInterface
     * @throws \Exception
     */
    public function renew($subscriptionId)
    {
        $subscription = Subscription::with(['lunar_order'])
            ->where('issues', 0)
            ->where('canceled', 0)
            ->where('renew', 1)
            ->find($subscriptionId);

        if(!$subscription){
            Log::info("No subscription found with id: {$subscriptionId}");
            return [
                'success' => false,
                'error' => "No subscription found!"
            ];
        }

        $customer = $subscription->customer;
        $product = \App\Product::find($subscription->cycle_id);
        $variant = $product->variant;

        $cart = Cart::create([]);
        $cart->addOrUpdate($variant, 1);

        $cart->setShippingAddress([
            'country_id' => 236,
            'title' => null,
            'first_name' => $subscription->subscription_type,
            'last_name' => $subscription->subscription_type,
            'city' => $subscription->city,
            'name' => $subscription->name,
            'state' => $subscription->state,
            'postcode' => $subscription->postal_code,
            'line_one' => $subscription->address_line_1,
            'line_two' => $subscription->address_line_2,
            'shipping_option' => 'basic',
        ]);

        $cart->setBillingAddress(
            $cart->shippingAddress
        );

        $cart->calculate();
        $order = $cart->createOrder();

        $order->update([
            'meta' => [
                'creditCardData' => collect($subscription->creditCard)->merge(['creditCardId' => $subscription->creditCard->id]),
                'customer_id' => $customer->id,
                'payment_type' => 'credit'
            ],
            'customer_id' => $customer->id,
            'resource' => 'auto-renew',
        ]);

        $driver = Payments::driver($subscription->creditCard->payment_type);
        $driver->order($order);

        if ($driver->charge()->success) {
            $order->update([
                'status' => 'payment-received'
            ]);

            $subscribe_type = $product->name;
            $months = $subscribe_type === 'yearly' ? 12 : 1;
            $subscriptionMonth = SubscriptionMonth::getCurrent();
            $start = Carbon::now();
            $finish = $subscribe_type === 'yearly' ? Carbon::parse($start)->addYear() : Carbon::parse($start)->addMonth();

            $subscription->update([
                'renew' => true,
                'order_id' => $order->id,
                'start' => $start,
                'finish' => $finish,
                'issues' => $months,
                'subscription_month_name' => $subscriptionMonth->name,
                'subscription_month_id' => $subscriptionMonth->id,
                'updated_at' => now(),
            ]);

            $order->digitalLines->first()->update([
                'meta->subscription' => $subscription
            ]);
            $subscription->product = $product;


            $order->shippableLines()
                ->each(function ($item) use ($subscription, $order) {
                    $order->shipments()->create([
                        'zone_id' => $subscription->zone_id,
                        'address_id' => $order->shippingAddress?->id,
                        'purchasable_id' => $item->purchasable_id,
                        'purchasable_type' => $item->purchasable_type,
                        'line_one' => $this->order->shippingAddress->line_one,
                        'line_two' => $this->order->shippingAddress->line_two,
                        'city' => $this->order->shippingAddress->city,
                        'state' => $this->order->shippingAddress->state,
                        'postcode' => $this->order->shippingAddress->postcode,
                        'quantity' => $item->quantity,
                    ]);
                });

            $this->notificationService->subscriptionRenewalConfirmation($order, $subscription);

            Cart::forgetCurrentSignUp();

            return [
                'success' => true,
            ];

        } else {
            $subscription->update([
                'renew' => false,
                'meta->failed_at' => now(),
                'canceled_reason' => 'cc failed'
            ]);

            $order->update([
                'status' => 'payment-declined'
            ]);

            $this->notificationService->subscriptionRenewalFailed($order, $subscription);

            return [
                'success' => false,
                'error' => "Payment declined"
            ];
        }
    }
}
