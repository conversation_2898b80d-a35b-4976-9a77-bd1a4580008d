<?php

namespace App\Services\Checkout;

use App\Cart;
use App\CreditCard;
use App\Subscription;
use App\SubscriptionMonth;
use App\Zone;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Lunar\Facades\Payments;

class CheckoutBackend extends Checkout
{
    protected string $typePayment = 'credit';
    protected string $errorMessage = 'Card Declined';
    protected bool $adminRenewNotification = true;
    protected $cardId;

    public function store(): object
    {
        // try {
            $this->cart = Cart::query()->create([]);
            $this->addProductToCart();
            $this->addSubscribesToCart();
            $this->addDiscountToCart();

            if($this->isProduct) {
                $this->zoneProductHandler();
            }

            $this->meta['customer_id'] = $this->customer->id;
            $this->meta['request'] = $this->requestData;
            $this->meta['payment_type'] = $this->typePayment;

            $this->initShippingHandler();
            $this->cart->calculate();

            $this->isFullyDiscounted = $this->cart->discountTotal->decimal() === $this->cart->subTotal->decimal() && $this->cart->total->decimal() == 0;

            $this->order = $this->cart->createOrder();

            $this->order->update([
                'customer_id' => $this->customer->id,
                'admin_renew_notification' => $this->adminRenewNotification,
                'resource' => 'admin',
            ]);



            Cart::forgetCurrentSignUp();

            switch ($this->typePayment) {
                case 'credit':
                    return $this->paymentCreditCardHandler();
                case 'cash':
                case 'check':
                    return $this->afterPayment();
                default:
            }
        // } catch (\Exception $exception) {
        //     Log::error($exception->getMessage());
        //     Log::error($exception->getTraceAsString());
        // }

        return (object)$this->makeResponseError($this->errorMessage, 422, 'creditCard');
    }

    public function summary($postcode = null): array
    {
        $this->cart = Cart::getCurrentSignUp();
        $this->cart->lines()->delete();
        $this->addProductToCart();
        $this->addSubscribesToCart();

        if($this->isProduct) {
            $this->cart->setShippingAddress([
                'country_id' => 236,
                'title' => null,
                'first_name' => 'admin',
                'last_name' => 'admin',
                'city' => 'admin',
                'state' => 'admin',
                'postcode' => $postcode,
                'line_one' => 'admin',
                'line_two' => null,
                'shipping_option' => 'basic'
            ]);
            $this->cart->setBillingAddress(
                $this->cart->shippingAddress
            );
        }
        $this->addDiscountToCart();
        $this->cart->calculate();

        return [
            'id' => $this->cart->id,
            'items' => $this->cart
                ->lines->map(function ($line) {
                    $product = \App\Product::find($line->purchasable->product_id);
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'count' => $line->quantity,
                        'total' => $line->subTotal->decimal(),
                        'img' => $product->img,
                    ];
                }),
            'tax' => $this->cart->taxTotal->decimal(),
            'promo' => $this->cart->discountTotal->decimal(),
            'shipping' => $this->cart->shippingSubTotal->decimal(),
            'total' => $this->cart->total->decimal(),
        ];
    }

    public function setTypePayment(string $typePayment): void
    {
        $this->typePayment = $typePayment;
    }

    public function setAdminRenewNotification(bool $adminRenewNotification): void
    {
        $this->adminRenewNotification = $adminRenewNotification;
    }

    protected function paymentCreditCardHandler(): object
    {
        $this->initPaymentDriver();

        $this->order->update([
            'meta' => $this->meta,
        ]);

        if ($this->isFullyDiscounted || $this->paymentDriver->charge($this->customer->stripe_token)->success) {
            return $this->afterPayment();
        }

        $this->notificationService->orderFailed($this->order);

        return (object)$this->makeResponseError($this->errorMessage, 422, 'creditCard');
    }


    public function setCardId($cardId): void
    {
        $this->cardId = $cardId;
    }

    protected function initPaymentDriver(): void
    {
        if($this->cardId) {
            $card = CreditCard::query()->find($this->cardId);
            $this->paymentDriver = Payments::driver($card->payment_method ?? config('lunar.payments.default'));
            $this->paymentInfo['id'] = $this->cardId;

            $creditCardData =  [
                'creditCardId' => $card->id,
                'token' => $card->token,
                'last_four' => $card->last_four,
            ];
        } else {
            $this->paymentDriver = Payments::driver(config('lunar.payments.default'));
            $creditCardData = $this->paymentDriver->getToken(
                customer: $this->customer,
                payment: $this->paymentInfo,
                withoutCard: $this->isFullyDiscounted,
            );
        }

        $this->meta['creditCardData'] = $creditCardData;
        $this->paymentDriver->order($this->order);
    }

    protected function afterPayment(): object
    {
        $statusPayment =  match ($this->typePayment) {
            'check', 'cash' => 'cash',
            'credit' => 'payment-received'
        };
        $typePaymentText = match ($this->typePayment) {
            'check' => 'Check',
            'cash' => 'Cash',
            'credit' => 'Credit Card',
        };

        $this->meta['status'] = $statusPayment;

        $this->order->update([
            'meta' => $this->meta,
            'status' => $statusPayment
        ]);

        $subscriptions = [];

        if ($this->isSubscribe) {
            $renew = $this->autoRenew;
            if($this->isFullyDiscounted){
                $renew = false;
            }

            foreach ($this->subscribes as $subscribe) {
                $subscribe_type = $subscribe->name;
                $months = $subscribe_type === 'yearly' ? 12 : 1;

                $zone = Zone::getZip(data_get($subscribe->delivery, 'zip'));
                $zone_id = $zone?->id;
                
                $subscriptionMonth = SubscriptionMonth::query()->find(data_get($subscribe, 'subscription_month_id'));
                $subscriptionMonth = $subscriptionMonth ?? SubscriptionMonth::getCurrent();
                $start = Carbon::now();

                $finish = $subscribe_type === 'yearly' ? $start->addYear() : $start->addMonth();
                $subscription = Subscription::create([
                    'renew' => $renew,
                    'name' => data_get($subscribe->delivery, 'name'),
                    'address_line_1' => data_get($subscribe->delivery, 'address_line_1'),
                    'address_line_2' => data_get($subscribe->delivery, 'address_line_2'),
                    'city' => data_get($subscribe->delivery, 'city'),
                    'state' => data_get($subscribe->delivery, 'state'),
                    'postal_code' => data_get($subscribe->delivery, 'zip'),
                    'payment_type' => $typePaymentText,
                    'subscription_type' => $subscribe_type,
                    'cycle_id' => DB::table('cycles')->where('months', $months)->first()?->id,
                    'zone_id' => $zone_id,
                    'credit_card_id' => data_get($this->order->meta->toArray(), 'creditCardData.creditCardId'),
                    'customer_id' => $this->order->customer_id,
                    'start' => $start,
                    'finish' => $finish,
                    'subscription_month_name' => $subscriptionMonth->name,
                    'subscription_month_id' => $subscriptionMonth->id,
                    'issues' => $months,
                    'order_id' => $this->order->id,
                ]);
                $this->order->digitalLines->first()->update([
                    'meta->subscription' => $subscription
                ]);

                $this->notificationService->subscriptionConfirmation($this->order);

                $subscription->product = $subscribe;

                if($zone_id && $this->isProduct === false) {
                    $this->order->shippableLines()
                        ->each(function ($item) use ($zone_id) {
                            $this->order->shipments()->create([
                                'zone_id' => $zone_id,
                                'address_id' => $this->order->shippingAddress?->id,
                                'purchasable_id' => $item->purchasable_id,
                                'purchasable_type' => $item->purchasable_type,
                                'line_one' => $this->order->shippingAddress->line_one,
                                'line_two' => $this->order->shippingAddress->line_two,
                                'city' => $this->order->shippingAddress->city,
                                'state' => $this->order->shippingAddress->state,
                                'postcode' => $this->order->shippingAddress->postcode,
                                'quantity' => $item->quantity,
                            ]);
                        });
                }

                $subscriptions[] = $subscription;
            }
        }


        if($this->isProduct) {
            $this->order->shippableLines()
                ->each(function ($item) {
                    $this->order->shipments()->create([
                        'name' => data_get($this->addressInfo, 'name'),
                        'zone_id' => $this->zoneProduct?->id,
                        'address_id' => $this->order->shippingAddress?->id,
                        'purchasable_id' => $item->purchasable_id,
                        'purchasable_type' => $item->purchasable_type,
                        'line_one' => $this->order->shippingAddress->line_one,
                        'line_two' => $this->order->shippingAddress->line_two,
                        'city' => $this->order->shippingAddress->city,
                        'state' => $this->order->shippingAddress->state,
                        'postcode' => $this->order->shippingAddress->postcode,
                        'quantity' => $item->quantity,
                    ]);
                });

            $this->notificationService->orderConfirmation($this->order);

        }


        return (object)[
            'success' => 'Created',
            'status' => 200,
        ];
    }

    private function addProductToCart(): void
    {
        if($this->products == null){
            return;
        }

        $this->products = $this->products->groupBy('id')->map(function ($items) {
            $item = $items->first();
            $item->count = $items->sum('count');

            return $item;
        });


        foreach ($this->products as $product) {
            $existing = app(
                config('lunar.cart.actions.get_existing_cart_line')
            )->execute(
                cart: $this->cart,
                purchasable: $product,
                meta: []
            );
            if ($existing) {
                $existing->update([
                    'quantity' => $product->count,
                ]);
                continue;
            }

            $this->cart->lines()->create([
                'purchasable_id' => $product->id,
                'purchasable_type' => $product->getMorphClass(),
                'quantity' => $product->count,
                'meta' => [],
            ]);
        }
    }

    private function addSubscribesToCart(): void
    {
        if($this->subscribes == null){
            return;
        }

        $this->subscribes = $this->subscribes->groupBy('id')->map(function ($items) {
            $item = $items->first();
            $item->count = $items->count();

            return $item;
        });

        $this->subscribes->map(function ($item) {
            $this->cart->add($item->variant, $item->count);
        });
    }

    private function addDiscountToCart(): void
    {
        if($this->discount) {
            $this->cart->update([
                'coupon_code' => $this->discount->coupon
            ]);
        }
    }
}