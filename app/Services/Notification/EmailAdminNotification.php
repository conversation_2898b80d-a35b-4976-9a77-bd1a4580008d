<?php

namespace App\Services\Notification;

use App\Emails\_old\SubscriptionCancelingEmail;
use App\Emails\AdminExportJobCompleted;
use App\Emails\AdminResetPassword;
use App\Emails\AdminUpdatedAddressEmail;
use App\Emails\OrderConfirmationEmail;
use App\Emails\OrderFailedEmail;
use App\Emails\SubscriptionConfirmationEmail;
use App\Emails\SubscriptionRenewalEmail;
use App\Emails\SubscriptionRenewalFailedEmail;
use App\Emails\SubscriptionRenewalFailedFirstWillRetryEmail;
use App\Setting;
use Exception;

class EmailAdminNotification implements NotificationInterface
{
    use PrepareData;

    //done
    public function orderConfirmation($order): void
    {
        $senders = Setting::getOptions('order_new_email_address');

        if($senders) {
            $data = $this->orderData($order);

            (new OrderConfirmationEmail)
                ->withData($data)
                ->setSenders($senders)
                ->withUser($order->customer)
                ->sendTo();
        }
    }

    //done
    public function orderFailed($order): void
    {
        $senders = Setting::getOptions('order_failed_email_address');

        if(count($senders)) {
            $data = $this->orderData($order);

            (new OrderFailedEmail)
                ->setSenders($senders)
                ->withData($data)
                ->sendTo();
        }
    }

    // todo
    public function subscriptionConfirmation($order): void
    {
        $senders = Setting::getOptions('order_new_email_address');

        if(count($senders)) {
            $data = $this->orderData($order);
            (new SubscriptionConfirmationEmail)
                ->withData($data)
                ->setSenders($senders)
                ->withUser($order->customer)
                ->sendTo();
        }
    }

    // done
    public function subscriptionRenewalConfirmation($order, $subscription): void
    {
        $senders = Setting::getOptions('subscription_renewed_email_address');

        if(count($senders)) {
            $data = $this->subscriptionWithOrderData($order, $subscription);

            (new SubscriptionRenewalEmail)
                ->withData($data)
                ->setSenders($senders)
                ->withUser($subscription->customer)
                ->sendTo();
        }
    }

    public function subscriptionRenewalFailedWillRetry($order, $subscription): void
    {
        $data = $this->subscriptionWithOrderData($order, $subscription);

        $senders = Setting::getOptions('subscription_renewal_failed_email_address');

        if(count($senders)) {
            (new SubscriptionRenewalFailedFirstWillRetryEmail)
                ->withData($data)
                ->setSenders($senders)
                ->withUser($subscription->customer)
                ->sendTo();
        }
    }

    // done
    public function subscriptionRenewalFailedFinal($order, $subscription): void
    {
        $data = $this->subscriptionWithOrderData($order, $subscription);

        $senders = Setting::getOptions('subscription_renewal_failed_email_address');

        if(count($senders)) {
            (new SubscriptionRenewalFailedEmail)
                ->withData($data)
                ->setSenders($senders)
                ->withUser($subscription->customer)
                ->sendTo();
        }
    }

    // todo
    public function subscriptionRenewalCanceling($subscription): void
    {
        $data = $this->subscriptionData($subscription);

        $senders = Setting::getOptions('subscription_canceling_email_address');

        if(count($senders)) {
            (new SubscriptionCancelingEmail)
                ->withData($data)
                ->setSenders($senders)
                ->withUser($subscription->customer)
                ->sendTo();
        }
    }

    /**
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionExpired($subscription): void
    {
        if(!$subscription->renew){
            $senders = Setting::getOptions('subscription_manual_email_address');

            if(count($senders)) {
                $data = $this->subscriptionData($subscription);

                (new SubscriptionRenewalFailedFirstWillRetryEmail)
                    ->withData($data)
                    ->setSenders($senders)
                    ->sendTo();
            }
        }
    }

    //done
    public function subscriptionUpdateAddress($subscriptionOld, $subscriptionNew): void
    {
        $senders = Setting::getOptions('updated_address_email_address');

        if(count($senders)) {

            $old = $this->subscriptionUpdateAddressData($subscriptionOld);
            $new = $this->subscriptionUpdateAddressData($subscriptionNew);

            (new AdminUpdatedAddressEmail)
                ->withData([
                    'subscriptionId' => data_get($subscriptionOld, 'id'),
                    'old' => $old,
                    'new' => $new
                ])
                ->setSenders($senders)
                ->sendTo();
        }
    }

    /**
     * @param $data
     * @return void
     * @throws Exception
     */
    public function adminExportJobCompleted($data): void
    {
        $senders = Setting::getOptions('admin_export_job_email_address');

        if(count($senders)) {
            (new AdminExportJobCompleted)
                ->withData($data)
                ->setSenders(Setting::getOptions('admin_export_job_email_address'))
                ->sendTo();
        }
    }

    /**
     * @param $customer
     * @param $token
     * @return void
     * @throws Exception
     */
    public function resetPassword($customer, $token): void
    {
        $link = url(route('nova.password.reset', [
            'token' => $token,
            'email' => $customer->email,
        ]));

        (new AdminResetPassword)
            ->withData(['link' => $link])
            ->setSenders($customer->email)
            ->withUser($customer)
            ->sendTo();
    }

    public function trackingInformation($customer): void
    {
        // TODO: Implement trackingInformation() method.
    }
}