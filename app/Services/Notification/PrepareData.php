<?php

namespace App\Services\Notification;

use App\Product;
use Illuminate\Support\Str;

trait PrepareData
{
    /**
     * @param $order
     * @return array
     */
    protected function orderData($order): array
    {
        $data = [
            'orderId' => $order->id,
            'year' => date("Y"),
            'customer' => [
                'name' => $order->customer->name
            ],
            'delivery' => [
                'name' => $order->shippingAddress->first_name,
                'address_line_1' => $order->shippingAddress->line_one,
                'address_line_2' => $order->shippingAddress->line_two,
                'city' => $order->shippingAddress->city,
                'state' => $order->shippingAddress->state,
                'postal_code' => $order->shippingAddress->postcode,
            ],
            'subscriptions' => [],
            'products' => [],
            'card' => [
                'last_four' => $order->meta->creditCardData['last_four'] ?? null,
            ],
        ];

        foreach($order->subscriptions as $subscription){
            $data['subscriptions'][] = [
                'id' => $subscription->id,
                'month_name' => $subscription->subscription_month_name,
                'cycle' => $subscription->cycle->only(['name', 'months']),
                'amount' => number_format($order->subscriptionTotal(), 2),
                'current' => \App\SubscriptionMonth::getCurrent()->id ==  $subscription->subscription_month_id,
            ];
        }

        if ($order->physicalTotal()) {
            $productsId = $order->physicalLines->map(function ($line) {
                return $line->purchasable->product_id;
            });
            $products = Product::query()->whereIn('id', $productsId)->get();
            $data['products'] = $order->physicalLines
                ->filter(function ($line) use ($products) {
                    return $products->find($line->purchasable->product_id);
                })
                ->map(function ($line) use ($products) {
                    $product = $products->find($line->purchasable->product_id)->toArray($line->quantity);
                    $product['amount'] = '$'. number_format($line->quantity * $product['price'], 2);
                    return $product;
                });
        }
        $data['order'] = [
            'amount' => number_format($order->total->decimal(), 2),
            'id' => $order->id,
        ];
        $data['current'] = collect(data_get($data,'subscriptions.*.current'))->contains(true);

        return $data;
    }

    /**
     * @param $subscription
     * @return array
     */
    protected function subscriptionUpdateAddressData($subscription): array
    {
        return [
            'subscription_name' => $subscription['name'],
            'address_line_1' => $subscription['address_line_1'],
            'address_line_2' => $subscription['address_line_2'],
            'subscription_city' => $subscription['city'],
            'subscription_state' => $subscription['state'],
            'subscription_zip' => $subscription['postal_code'],
        ];
    }

    /**
     * @param $order
     * @param $subscription
     * @return array
     */
    protected function subscriptionWithOrderData($order, $subscription): array
    {
        return [
            'customer' => $subscription->customer->only('name'),
            'cycle' => $subscription->cycle->only(['name', 'months']),
            'cycle_name' => $subscription->cycle->months == 12 ? "יאר" : "חודש",
            'order' => [
                'amount' => number_format($order->subscriptionTotal(), 2)
            ],
            'subscription' => $subscription->only(['id', 'name', 'address_line_1', 'address_line_2', 'city', 'state', 'postal_code']),
            'subscription_month_name' => $subscription->subscription_month_name,
            'current' => \App\SubscriptionMonth::getCurrent()->id ==  $subscription->subscription_month_id,
            'price' => $order->subscriptionTotal(),
            'delivery' => $subscription->only([
                'name', 'address_line_1', 'address_line_2', 'city', 'state', 'postal_code'
            ]),
        ];
    }

    /**
     * @param $subscription
     * @return array
     */
    protected function subscriptionData($subscription): array
    {
        return [
            'id' => $subscription->id,
            'subscription' => ['id' => $subscription->id],
            'percent' => $subscription->zone->percentByCycle($subscription->cycle),
            'price' => number_format($subscription->zone->priceByCycle($subscription->cycle)/100, 2),
            'link' => env('APP_URL') . config('nova.path') . "/resources/subscriptions/$subscription->id",
            'cycle' => $subscription->cycle->only('name', 'months', 'publications'),
            'cycle_name' => $subscription->cycle->months == 12 ? "יאר" : "חודש",
            'credit_card' => $subscription->credit_card,
            'name' => explode(' ', $subscription->customer->name, 2)[0],
            'gift_information' => $subscription->note ? [
                'note' => $subscription->note,
                'name' => $subscription->customer->name,
            ] : null,
            'delivery' => $subscription->only([
                'name', 'address_line_1', 'address_line_2', 'city', 'state', 'postal_code'
            ]),
        ];
    }

    /**
     * @param $subscription
     * @return array
     */
    protected function subscriptionExpiredData($subscription): array
    {
        $credit_card = $subscription->creditCard;
        $last_name = explode(' ', $subscription->customer->name);

        return [
            'year' => date('Y'),
            'customer' => $subscription->customer,
            'id' => $subscription->id,
            //'percent' => $promo,
            // '' => $subscription->gift,
            'cycle' => $subscription->cycle,
            'subscription_month_name' => $subscription->subscription_month_name,
            'is_current' => $subscription->subscription_month_is_current,
            'renew' => $subscription->renew,
            'name' => end($last_name),
            'start' => $subscription->start->format('M j, Y'),
            'last_four' => data_get($credit_card, 'last_four'),
            'finish' => $subscription->finish->format('M j, Y'),
            'picture' => data_get($subscription->gift, 'picture'),
            'payment_type' => Str::title(data_get($credit_card, 'type')),
            'creditCard' => optional($credit_card)->only('id', 'last_four', 'type'),
        ];
    }
}