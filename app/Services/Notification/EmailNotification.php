<?php

namespace App\Services\Notification;

use App\Emails\GenerateRouteEmail;
use App\Emails\OrderConfirmationEmail;
use App\Emails\OrderFailedEmail;
use App\Emails\SendVerifyCode;
use App\Emails\SubscriptionConfirmationEmail;
use App\Emails\SubscriptionRenewalEmail;
use App\Emails\SubscriptionRenewalFailedEmail;
use App\Emails\SubscriptionRenewalFailedFirstWillRetryEmail;
use App\Emails\SubscriptionRenewReminderEmail;
use App\Emails\TrackingInformation;
use Exception;

class EmailNotification implements NotificationInterface
{
    use PrepareData;

    /**
     * @param $order
     * @return void
     * @throws Exception
     */
    public function orderConfirmation($order): void
    {
        $data = $this->orderData($order);

        (new OrderConfirmationEmail)
            ->withData($data)
            ->setSenders($order->customer->email)
            ->withUser($order->customer)
            ->sendTo();
    }

    /**
     * @param $order
     * @return void
     * @throws Exception
     */
    public function orderFailed($order): void
    {
        $data = $this->orderData($order);

        (new OrderFailedEmail)
            ->setSenders($order->customer->email)
            ->withData($data)
            ->sendTo();
    }

    /**
     * @param $order
     * @return void
     * @throws Exception
     */
    public function subscriptionConfirmation($order): void
    {
        $data = $this->orderData($order);

        (new SubscriptionConfirmationEmail)
            ->withData($data)
            ->setSenders($order->customer->email)
            ->sendTo();
    }

    /**
     * @param $order
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionRenewalConfirmation($order, $subscription): void
    {
        $data = $this->subscriptionWithOrderData($order, $subscription);

        (new SubscriptionRenewalEmail)
            ->withData($data)
            ->setSenders($subscription->customer->email)
            ->withUser($subscription->customer)
            ->sendTo();
    }

    /**
     * @param $order
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionRenewalFailedFinal($order, $subscription): void
    {
        $data = $this->subscriptionWithOrderData($order, $subscription);

        (new SubscriptionRenewalFailedEmail)
            ->withData($data)
            ->setSenders($subscription->customer->email)
            ->withUser($subscription->customer)
            ->sendTo();
    }
    /**
     * @param $order
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionRenewalFailedWillRetry($order, $subscription): void
    {
        $data = $this->subscriptionWithOrderData($order, $subscription);

        (new SubscriptionRenewalFailedFirstWillRetryEmail)
            ->withData($data)
            ->setSenders($subscription->customer->email)
            ->withUser($subscription->customer)
            ->sendTo();
    }

    /**
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionRenewalCanceling($subscription): void
    {
        $data = $this->subscriptionData($subscription);

        (new SubscriptionCancelingEmail)
            ->withData($data)
            ->setSenders($subscription->customer->email)
            ->withUser($subscription->customer)
            ->sendTo();
    }

    /**
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionExpired($subscription): void
    {
        $data = $this->subscriptionData($subscription);

        if($subscription->renew){
            // reminder
            (new SubscriptionRenewReminderEmail)
                ->withData($data)
                ->setSenders($subscription->customer->email)
                ->withUser($subscription->customer)
                ->sendTo();
        }else{
            // manual
            (new SubscriptionRenewalFailedFirstWillRetryEmail)
                ->withData($data)
                ->setSenders($subscription->customer->email)
                ->withUser($subscription->customer)
                ->sendTo();
        }
    }


    public function subscriptionReminder($subscription): void
    {
        $data = $this->subscriptionData($subscription);

        (new SubscriptionRenewReminderEmail)
            ->withData($data)
            ->setSenders($subscription->customer->email)
            ->withUser($subscription->customer)
            ->sendTo();
    }

    public function subscriptionManual($subscription): void
    {
        $data = $this->subscriptionData($subscription);

        (new SubscriptionRenewalFailedFirstWillRetryEmail)
            ->withData($data)
            ->setSenders($subscription->customer->email)
            ->withUser($subscription->customer)
            ->sendTo();
    }

    /**
     * @param $customer
     * @return void
     * @throws Exception
     */
    public function trackingInformation($customer, $subscriptionMonth = null): void
    {
        // (new GenerateRouteEmail())
        //     ->setSenders($customer->email)
        //     ->withUser($customer)
        //     ->sendTo();
    }

    /**
     * @param $customer
     * @param $code
     * @return object
     */
    public function verificationAccount($customer, $code): object
    {
        try {
            $data = [
                'name' => $customer->name,
                'code' => $code,
                'year' => now()->format('Y')
            ];
            (new SendVerifyCode)
                ->withData($data)
                ->setSenders($customer->email)
                ->withUser($customer)
                ->sendTo();
            return (object)[
                'success' => true,
            ];

        } catch (Exception $exception) {
            return (object)[
                'success' => false,
                'message' => $exception->getMessage()
            ];
        }
    }
}