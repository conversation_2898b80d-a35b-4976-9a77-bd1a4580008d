<?php

namespace App\Services\Notification;

interface NotificationInterface
{
//    public function orderConfirmation($order): void;

    public function orderFailed($order): void;

    public function subscriptionConfirmation($order): void;

    public function subscriptionRenewalConfirmation($order, $subscription): void;

    public function subscriptionRenewalFailedWillRetry($order, $subscription): void;

    public function subscriptionRenewalFailedFinal($order, $subscription): void;

    public function subscriptionRenewalCanceling($subscription): void;

    public function subscriptionExpired($subscription);

    public function trackingInformation($customer): void;

}