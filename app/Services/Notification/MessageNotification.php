<?php

namespace App\Services\Notification;

use App\Customer;
use Exception;
use Illuminate\Support\Facades\Log;
use Psr\Http\Client\ClientExceptionInterface;
use Vonage\Client;
use Vonage\Client\Credentials\Basic;
use Vonage\Client\Credentials\Keypair;
use Vonage\SMS\Message\SMS;
use Vonage\Voice\Endpoint\Phone;
use Vonage\Voice\NCCO\Action\Talk;
use Vonage\Voice\NCCO\NCCO;
use Vonage\Voice\OutboundCall;
use Vonage\Voice\Webhook\Event;

class MessageNotification implements NotificationInterface
{
    protected ?Client $smsClient = null;
    protected ?Client $voiceClient = null;

    public function __construct()
    {
        try {
            $basicCredentials = new Basic(config('vonage.api_key'), config('vonage.secret'));
            $keyPath = storage_path(config('vonage.application_private_key_path'));
            $keypairCredentials = new Keypair(
                file_get_contents($keyPath),
                config('vonage.application_id')
            );

            $this->smsClient = new Client($basicCredentials);
            $this->voiceClient = new Client($keypairCredentials);
        } catch (Exception $exception) {
            Log::error($exception->getMessage());
        }
    }

    /**
     * @param $order
     * @return void
     */
    public function orderConfirmation($order): void
    {
         // TODO: Implement orderConfirmation() method.
    }

    /**
     * @param $order
     * @return void
     * @throws ClientExceptionInterface
     */
    public function orderFailed($order): void
    {
        $this->sendSms($order->customer->phone, trans('sms.notification.order_failed'), 'order-failed');
    }

    /**
     * @param $order
     * @return void
     * @throws ClientExceptionInterface
     */
    public function subscriptionConfirmation($order): void
    {
//        $this->makeVoiceCall($customer, 'You have just subscribed to Vinderkind.');
        $this->sendSms($order->customer->phone, 'Your new subscription details are as follows: ', 'new-subscription-message');
    }

    /**
     * @param $order
     * @param $subscription
     * @return void
     * @throws ClientExceptionInterface
     */
    public function subscriptionRenewalConfirmation($order, $subscription): void
    {
//        $this->makeVoiceCall($customer, 'Your subscription has been renewed.');
        $this->sendSms($subscription->customer->phone, 'Your subscription renewal details are as follows: ', 'renewal-message');
    }

    /**
     * @param $order
     * @param $subscription
     * @return void
     */
    public function subscriptionRenewalFailedFinal($order, $subscription): void
    {
        // TODO: Implement subscriptionRenewalFailed() method.
    }

    /**
     * @param $order
     * @param $subscription
     * @return void
     */
    public function subscriptionRenewalFailedWillRetry($order, $subscription): void
    {
        // TODO: Implement subscriptionRenewalFailed() method.
    }

    /**
     * @param $subscription
     * @return void
     */
    public function subscriptionRenewalCanceling($subscription): void
    {
        // TODO: Implement subscriptionRenewalCanceling() method.
    }

    /**
     * @param $subscription
     * @return void
     */
    public function subscriptionExpired($subscription)
    {
        // TODO: Implement subscriptionExpired() method.
    }

    public function subscriptionReminder($subscription): void
    {
        // TODO: subscriptionReminder
    }

    public function subscriptionManual($subscription): void
    {
        // TODO: subscriptionManual
    }


    /**
     * @param $customer
     * @param $code
     * @return object
     * @throws ClientExceptionInterface
     */
    public function verificationAccount($customer, $code): object
    {
        try {
            $this->sendSms($customer->phone, 'Enter the 6 digit code below into your Vinderkind login page. ' . $code, 'test-message');
            return (object)[
                'success' => true,
            ];
        } catch (Exception $exception) {
            return (object)[
                'success' => false,
                'message' => $exception->getMessage()
            ];
        }
    }

    /**
     * @param $phoneNumber
     * @param $message
     * @param $clientRef
     * @return void
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    private function sendSms($phoneNumber, $message, $clientRef): void
    {
        return;
        if (!$this->smsClient) {
            Log::info('SMS not initialize');
//            throw new Exception('SMS not initialize');
            return;
        }
        try {
            $text = new SMS(
                $phoneNumber,
                config('vonage.from_number'),
                $message
            );

            $text->setClientRef($clientRef);
            $this->smsClient->sms()->send($text);
        } catch (Exception $exception) {
            Log::error($exception->getMessage());
//            throw new Exception($exception->getMessage());
        }
    }

    /**
     * @param Customer $customer
     * @param $message
     * @return Event|null
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    private function makeVoiceCall(Customer $customer, $message): ?Event
    {
        if (!$this->voiceClient) {
            Log::info('Voice not initialize');
            throw new Exception('Voice not initialize');
        }

        try {
            $outboundCall = new OutboundCall(
                new Phone($customer->phone),
                new Phone(config('vonage.from_number'))
            );

            $ncco = new NCCO();
            $ncco->addAction(new Talk($message));

            $outboundCall->setNCCO($ncco);

            return $this->voiceClient->voice()->createOutboundCall($outboundCall);
        } catch (Exception $e) {
            Log::error("Failed to make call: " . $e->getMessage());
            return null;
        }
    }



//    private function sendIssueExpiryText(array $data, Customer $customer): void
//    {
//        $message = "You have 1 issue remaining, your subscription is about to expire. Renew now at Vinderkind.com.";
//        $this->sendSms($customer->phone, $message, 'issue-expiry-message');
//    }
//
//
//    private function sendSubscriptionExpiredText(array $data, Customer $customer): void
//    {
//        $message = "Your subscription has expired. Renew now at Vinderkind.com.";
//        $this->sendSms($customer->phone, $message, 'issue-expiry-message');
//    }


//    private function sendSubscriptionExpiredPhone(array $data, Customer $customer): void
//    {
//        $this->makeVoiceCall($customer, 'Your subscription has expired. Renew now at Vinderkind.com.');
//    }

//    private function sendIssueExpiryPhone(array $data, Customer $customer): void
//    {
//        $this->makeVoiceCall($customer, 'You have 1 issue remaining, your subscription is about to expire. Renew now at Vinderkind.com.');
//    }

    public function trackingInformation($customer): void
    {
        // TODO: Implement trackingInformation() method.
    }
}