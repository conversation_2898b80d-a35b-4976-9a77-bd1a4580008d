<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class ShipStationOrderService
{
    private $apiKey;
    private $apiSecret;
    private $baseUrl;
    private $action = '/orders/createorder';

    public function __construct()
    {
        $this->apiKey = config('shipstation.apiKey');
        $this->apiSecret = config('shipstation.apiSecret');
        $this->baseUrl = config('shipstation.apiURL');
    }

    public static function createOrder(array $orderData)
    {
        $baseUrl = config('shipstation.apiURL');
        $action = '/orders/createorder';
        $apiKey = config('shipstation.apiKey');
        $apiSecret = config('shipstation.apiSecret');

        $response = null; // Initialize HTTP response variable
        // try {
        $formattedOrderData = [
            'orderNumber' => $orderData['orderNumber'],
            'orderDate' => $orderData['orderDate'],
            'orderStatus' => $orderData['orderStatus'],
            'billTo' => $orderData['billTo'],
            'shipTo' => $orderData['shipTo'],
        ];

        $response = Http::withBasicAuth($apiKey, $apiSecret)
            ->post("{$baseUrl}{$action}", $formattedOrderData);

        //Log::info('este es la response' . $response);
        if ($response->successful()) {
            $orderObject = $response->json();
            return $orderObject;
        } else {
            $responseBody = $response->body();
            $responseArray = json_decode($responseBody, true);

            // Default error message if none is found in the response
            $errorMessage = "Unsuccessful request: HTTP Status Code - " . $response->status();

            $orderId = $orderData['orderNumber'] ?? 'Unknown';

            // If there's an error message in the response, use that
            if (!empty($responseArray) && isset($responseArray['ExceptionMessage'])) {
                $errorMessage = $responseArray['ExceptionMessage'];
            } elseif (isset($responseArray['Message'])) {
                $errorMessage = $responseArray['Message'];
            }

            // Log and throw the exception with the detailed error message
            // Log::error("An error occurred while creating the label for ID {$orderId}: {$errorMessage}");
            throw new \Exception($errorMessage);
        }
        // } catch (\Exception $e) {

        //     $errorMessage = $e->getMessage();

        //     // Check if $response is not null and if so, try to extract the 'ExceptionMessage' from it
        //     if ($response) {
        //         $errorMessage = data_get($response->json(), 'ExceptionMessage', $e->getMessage());
        //     }

        //     $detailedMessage = 'An error occurred while creating the ShipStation order: ' . $errorMessage;

        //     // Check if $response is not null before attempting to get its JSON content
        //     if ($response) {
        //         $detailedMessage .= '. API Response: ' . json_encode($response->json());
        //     }

        //     Log::error($detailedMessage);
        //     throw new \Exception($detailedMessage);
        // }
    }

    public static function getOrder($orderId)
    {
        $baseUrl = config('shipstation.apiURL');
        $action = "/orders/{$orderId}";
        $apiKey = config('shipstation.apiKey');
        $apiSecret = config('shipstation.apiSecret');

        return Http::withBasicAuth($apiKey, $apiSecret)
            ->get("{$baseUrl}{$action}");
    }
}
