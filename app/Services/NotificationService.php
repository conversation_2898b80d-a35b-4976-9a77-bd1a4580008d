<?php

namespace App\Services;

use App\Services\Notification\EmailAdminNotification;
use App\Services\Notification\EmailNotification;
use App\Services\Notification\MessageNotification;
use App\Services\Notification\NotificationInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Exception;

class NotificationService implements NotificationInterface
{
    // subscription is many | change email template???
    // variables job mail | level???
    // When there are queues, implement queue processing...


    protected MessageNotification $messageNotification;
    protected EmailNotification $emailNotification;
    protected EmailAdminNotification $emailAdminNotification;

    public function __construct(
        MessageNotification $messageNotification,
        EmailNotification   $emailNotification,
        EmailAdminNotification $emailAdminNotification
    )
    {
        $this->emailNotification = $emailNotification;
        $this->messageNotification = $messageNotification;
        $this->emailAdminNotification = $emailAdminNotification;
    }

    /**
     * @param $customer
     * @return EmailNotification|MessageNotification|null
     */
    private function dispatchNotification($customer = null): MessageNotification|EmailNotification|null
    {
        if(!$customer){
            return null;
        }
        if ($customer->preferred_contact_method === 'phone' && !empty($customer->phone)) {
            return $this->messageNotification;
        }

        if ($customer->preferred_contact_method === 'email' && !empty($customer->email)) {
            return $this->emailNotification;
        }

        return null;
    }

    /**
     * Order new (configured) (Order without signup (products only))
     * @param $order
     * @return void
     * @throws Exception
     */
    public function orderConfirmation($order): void
    {
        $this->dispatchNotification($order->customer)?->orderConfirmation($order);

        $this->emailAdminNotification->orderConfirmation($order);
    }

    /**
     * Order failed (configured) (Signup Failed  (including products))
     * @param $order
     * @return void
     * @throws ClientExceptionInterface
     */
    public function orderFailed($order): void
    {
        $this->dispatchNotification($order->customer)?->orderFailed($order);

        $this->emailAdminNotification->orderFailed($order);
    }

    /**
     * Order new subscription (configured) Signup Confirmation (including products)
     * @param $order
     * @return void
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    public function subscriptionConfirmation($order): void
    {
        $this->dispatchNotification($order->customer)?->subscriptionConfirmation($order);

        $this->emailAdminNotification->subscriptionConfirmation($order);
    }

    /**
     * Subscription renewal confirmation (configured) (Renew confirmation)
     * @param $order
     * @param $subscription
     * @return void
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    public function subscriptionRenewalConfirmation($order, $subscription): void
    {
        $this->dispatchNotification($subscription->customer)?->subscriptionRenewalConfirmation($order, $subscription);

        $this->emailAdminNotification->subscriptionRenewalConfirmation($order, $subscription);
    }

    /**
     * Subscription renewal failed final
     * @param $order
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionRenewalFailedFinal($order, $subscription): void
    {
        $this->dispatchNotification($subscription->customer)?->subscriptionRenewalFailedFinal($order, $subscription);

        $this->emailAdminNotification->subscriptionRenewalFailedFinal($order, $subscription);
    }
    /**
     * Subscription renewal failed we will retry
     * @param $order
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionRenewalFailedWillRetry($order, $subscription): void
    {
        $this->dispatchNotification($subscription->customer)?->subscriptionRenewalFailedWillRetry($order, $subscription);
    }



    /**
     * Subscription renewal canceling
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionRenewalCanceling($subscription): void
    {
        $this->dispatchNotification($subscription->customer)?->subscriptionManual($subscription);

//        $this->emailAdminNotification->subscriptionRenewalCanceling($subscription);
    }

    /**
     * Reminder (configured) (Reminder for not auto renew / Reminder for auto renew)
     * @param $subscription
     * @return void
     * @throws Exception
     */
    public function subscriptionExpired($subscription): void
    {
        if($subscription->renew){
            $this->dispatchNotification($subscription->customer)?->subscriptionReminder($subscription);
        }else{
            $this->dispatchNotification($subscription->customer)?->subscriptionManual($subscription);
        }

        $this->emailAdminNotification->subscriptionExpired($subscription);
    }

    /**
     * @param $customer
     * @return void
     * @throws Exception
     */
    public function trackingInformation($customer): void
    {
        $this->dispatchNotification($customer)?->trackingInformation($customer);
    }


    /**
     * Verification account
     * @param $customer
     * @param $code
     * @return object
     * @throws ClientExceptionInterface
     */
    public function verificationAccount($customer, $code): object
    {
        return $this->dispatchNotification($customer)?->verificationAccount($customer, $code);
    }

    /**
     * Reset password
     * @param $customer
     * @param $token
     * @return void
     * @throws Exception
     */
    public function resetPassword($customer, $token): void
    {
        $this->emailAdminNotification->resetPassword($customer, $token);
    }

    /**
     * Subscription update address (configured) (Address Updated)
     * @param $subscriptionOld
     * @param $subscriptionNew
     * @return void
     */
    public function subscriptionUpdateAddress($subscriptionOld, $subscriptionNew): void
    {
        $this->emailAdminNotification->subscriptionUpdateAddress($subscriptionOld, $subscriptionNew);
    }

    /**
     * Admin export job completed
     * @param $data
     * @return void
     * @throws Exception
     */
    public function adminExportJobCompleted($data): void
    {
        $this->emailAdminNotification->adminExportJobCompleted($data);
    }
}
