<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use CampaignMonitor;

class Subscriber extends Model
{
    protected $guarded = [];

    public static function boot()
    {
        parent::boot();

        self::saved(function ($model) {
            $result = CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                ->add([
                    'EmailAddress' => $model->email,
                    'ConsentToTrack' => 'Yes'
                ]);
            Log::debug(json_encode($result->response));

            switch ($model->state) {
                case 'Deleted':
                    CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->delete($model->email);
                    break;
                case 'Unsubscribed':
                    CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->unsubscribe($model->email);
                    break;
                case 'Subscribe':
                case 'Active':
                    $active = CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->update($model->email, ['Resubscribe' => true]);
                    // Log::debug($active->response->error.': '.$active->response->error_description);
                    break;

            }
        });
    }

}
