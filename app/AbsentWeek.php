<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AbsentWeek extends Model
{

    public $guarded = [];

    public $catst = [
        'week' => 'datetime',
    ];

    public function scopeFuture($query)
    {
        return $query->where('date', '>=', today());
    }

    public static function boot()
    {
        parent::boot();

        static::saving(function ($week) {
            $week->week = now()->parse($week->week)->startOfWeek();
        });
    }
}
