<?php

namespace App;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Lunar\FieldTypes\Text;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class Product extends \Dystcz\LunarApi\Domain\Products\Models\Product
{
    use SortableTrait;

    public $sortable = [
        'order_column_name' => 'sort_order',
        'sort_when_creating' => true,
    ];



    public $guarded = [];

    public $with = ['variants'];

    static public $productTypes = [
        'accessories' => 1,
        'issues' => 2,
        'cycles' => 3
    ];

    protected $attributes = [
        'product_type_id' => 1, //static::$productType,
        'sort_order' => 99
    ];

    static public $variant_data = [
        'length',
        'width',
        'height',
        'weight',
    ];

    public $price_data = [
        'price'
    ];

    public function scopeAccessories($query)
    {
        return $query->where('product_type_id', static::$productTypes['accessories']);
    }

    public function scopeIssues($query)
    {
        return $query->where('product_type_id', static::$productTypes['issues']);
    }

    public function scopeCycles($query)
    {
        return $query->where('product_type_id', static::$productTypes['cycles']);
    }

    public function toArray($count = 0)
    {

        $name = hebDateStringArray();
        $currentString = "{$name['month']} {$name['year']}";

        return $this->only([
                'id',
                'name',
                'description',
                'price',
                'img',
                'pdf',
                'mp3',
            ])
            + [
                'count' => $count,
                'label' => $this->name == $currentString ? 'יעצטיגע אויסגאבע' : null,
            ];
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->translateAttribute('name'),
            set: fn(mixed $value) => [
                // 'attribute_data' => json_encode([])
            ],
        );
    }

    protected function description(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->translateAttribute('description'),
            set: fn(mixed $value) => [
                // 'attribute_data' => json_encode([])
            ],
        );
    }

    protected function img(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->getFirstMediaUrl('images'),
        );
    }

    protected function mp3(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->getFirstMediaUrl('mp3'),
        );
    }

    protected function pdf(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->getFirstMediaUrl('pdf'),
        );
    }

    protected function price(): Attribute
    {
        return Attribute::make(
            get: fn(mixed $value) => $this->prices->first()?->price->decimal(rounding: true),
        );
    }

    protected function variantJson(): Attribute
    {
        if ($variant = $this->variants->first()) {
            $data = collect(static::$variant_data)
                ->mapWithKeys(function ($index) use ($variant) {
                    $column = $index . '_value';
                    return [
                        $index => $variant->$column,
                    ];
                });
        } else {
            $data = [];
        }

        return Attribute::make(
            get: fn(mixed $value) => $data,
            set: fn(mixed $value) => [],
        );
    }

    public function getVariantAttribute()
    {
        return $this->variants->first();
    }

    static public function createFromArray($data = [])
    {
        $product = self::create([
            'attribute_data' => collect([
                'name' => new Text($data['name']),
                'description' => new Text($data['description']),
            ]),
            'status' => 'draft',
            'product_type_id' => $data['product_type_id'],
        ]);

        $variant = $product->variants()->create([
            'tax_class_id' => 1,

            'length_value' => $data['length'],
            'length_unit' => 'in',

            'width_value' => $data['width'],
            'width_unit' => 'in',

            'height_value' => $data['height'],
            'height_unit' => 'in',

            'weight_value' => $data['weight'],
            'weight_unit' => 'oz',

            'sku' => 'issue_' . $data['issue_id']
        ]);

        $variant->prices()->create([
            'price' => $data['price'] * 100,
            'currency_id' => 1
        ]);
        return $product;
    }

    public static function YearlyPercentOff()
    {
        $yearlyPrice = self::firstWhere('id', getCycleIds()['yearly'])->price;
        $monthlyPrice = self::firstWhere('id', getCycleIds()['monthly'])->price * 12;
        return round(($monthlyPrice - $yearlyPrice) / $monthlyPrice * 100);
    }
}
