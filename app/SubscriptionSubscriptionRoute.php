<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionSubscriptionRoute extends Model
{
    use HasFactory;

    public $guarded = [];

    public $casts = [
        'web_hooks' => 'array'
    ];

    public $table = 'subscription_subscription_route';

    public function subscriptionRoute()
    {
        return $this->belongsTo(SubscriptionRoute::class);
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    public function insertWebhook($data = [])
    {
        $web_hooks = $this->web_hooks ?? [];

        array_push(
            $web_hooks,
            $data
        );

        $this->update([
            'web_hooks' => $web_hooks,
            'visited' => (!$this->visited && data_get($data, 'activity_type') == 'mark-destination-visited') || $this->visited
        ]);
    }
}
