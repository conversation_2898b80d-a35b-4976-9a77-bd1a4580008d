<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Promo extends Model
{

    use HasFactory;

    protected $guarded = [];

    public static $types = [
        'percent' => 'Percent',
        'fixed' => 'Fixed',
    ];

    protected $casts = [
        'end' => 'date',
        'start' => 'date',
        'work_on_renew' => 'boolean'
    ];

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function getLeftAttribute()
    {
        return $this->limit ? $this->limit - $this->orders->unique('subscription_id')->count() : null;
    }

    public function getPrice($price)
    {
        if ($this->type == 'percent') {
            return $price - ($price * ($this->amount / 100));
        } else if ($this->type == 'fixed') {
            return $price = $price - $this->amount;
        }
        return $price;
    }
}
